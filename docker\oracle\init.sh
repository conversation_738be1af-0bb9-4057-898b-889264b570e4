USER=dev
PASSWD=7j2N#xjV
DUMP_NAME=${USER}_data

cat > /tmp/user.sql << EOL
-- New Scheme User
create temporary tablespace ${DUMP_NAME}_temp tempfile '/u01/app/oracle/oradata/${DUMP_NAME}_temp.dbf' size 512m autoextend on next 256m maxsize 20g extent management local;
create tablespace ${DUMP_NAME} datafile '/u01/app/oracle/oradata/${DUMP_NAME}.dbf' size 1024M autoextend on next 512M maxsize 30g extent management local;
alter tablespace ${DUMP_NAME} add datafile '/u01/app/oracle/oradata/${DUMP_NAME}_1.dbf' size 1024M autoextend on next 512M maxsize 30g;
alter tablespace ${DUMP_NAME} add datafile '/u01/app/oracle/oradata/${DUMP_NAME}_2.dbf' size 1024M autoextend on next 512M maxsize 30g;
alter tablespace ${DUMP_NAME} add datafile '/u01/app/oracle/oradata/${DUMP_NAME}_3.dbf' size 1024M autoextend on next 512M maxsize 30g;
alter tablespace ${DUMP_NAME} add datafile '/u01/app/oracle/oradata/${DUMP_NAME}_4.dbf' size 1024M autoextend on next 512M maxsize 30g;
create user $USER identified by "$PASSWD" default tablespace $DUMP_NAME temporary tablespace ${DUMP_NAME}_temp;
grant connect, resource, dba to $USER;

alter user system account lock;
exit;
EOL
su oracle -c "$CHARSET_MOD $ORACLE_HOME/bin/sqlplus -S / as sysdba @/tmp/user.sql"
