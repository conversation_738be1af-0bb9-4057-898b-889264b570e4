import got from 'got'
import log from './log.js'
import HttpsProxyAgent from 'https-proxy-agent'

let global_options = {
    //proxy setting
}

async function post(url, options = {}, extra = { _auto: true }) {
    extra = isExtra(options, extra)
    options.method = 'POST'

    return await _request(url, options, extra)
}

async function get(url, options = {}, extra = { _auto: true }) {
    extra = isExtra(options, extra)
    options.method = 'GET'
    return await _request(url, options, extra)
}
function isExtra(obj, extra) {
    let new_extra = {}
    let extra_keys = ['_retry', '_auto', '_proxy']
    for (const key of extra_keys) {
        if(key in extra) {
            new_extra[key] = extra[key]
        }
        if (key in obj) {
            new_extra[key] = obj[key]
            delete obj[key]
        }
    }
    return new_extra
}

async function request(url, options = {}, extra = { _auto: true }) {
    extra = isExtra(options, extra)
    return await _request(url, options, extra)
}


async function _request(url, options, extra = { _auto: true }) {
    if (!options.timeout) {
        options.timeout = {
            connect: 50000,
            socket: 50000,
            response: 50000
        }
    }
    if (!options.headers) {
        options.headers = {}
    }
    if (!('user-agent' in options.headers)) {
        options.headers['user-agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.0.0 Safari/537.36'
    }
    if(global_options.proxy) {
        let proxyAgent = new HttpsProxyAgent(global_options.proxy)
        proxyAgent.timeout = 3000
        options.agent = {
            https: proxyAgent,
            http: proxyAgent,
        }
    }
    if (extra._proxy) {
        let proxyAgent = new HttpsProxyAgent(extra._proxy)
        proxyAgent.timeout = 3000
        options.agent = {
            https: proxyAgent,
            http: proxyAgent,
        }
    }

    let condition = extra._retry && (extra._retry.condition || ((resp) => resp.statusCode === 200))
    let resp;
    try {
        resp = await retry_req(async () => {
            options.methodRewriting = true

            return await got(url, options);
        }, condition, (extra._retry && extra._retry.times) || 3)
        if (extra._auto) {
            try {
                resp = JSON.parse(resp.body)
            } catch (error) { }

        }
    } catch (error) {
        console.log(error.message)
        log.append(error.message)
        resp = error.response
        if (extra._auto && resp) {
            try {
                return JSON.parse(resp.body)
            } catch (e) { }
        }
    }
    return resp
}

async function retry_req(callback, condition, times = 3) {
    try {
        let result = await callback()
        if (times <= 1) return result
        if ((condition && condition(result)) || !result) {
            await wait(1000)
            return await retry_req(callback, condition, times - 1)
        }
        return result
    } catch (e) {
        console.log(e.message)
        log.append(e.message)
        if (times <= 1) return result
        if (e.code == 'ETIMEDOUT') {
            await wait(1000)
            return await retry_req(callback, condition, times - 1)
        }
        return
    }
}

async function wait(timeout) {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve()
        }, timeout)
    })
}

export default {
    global_options,
    post,
    get,
    request
}

// ;(async ()=>{
//     let resp = await get('https://www.baidu.com')
//     console.log(resp.body);
// })();