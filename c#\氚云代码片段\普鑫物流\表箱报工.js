

$.ListView.ActionPreDo = function (actionCode) {
    if (actionCode == 'custom_import') {
        let upload = new Upload("BoxReportTemplate")
        upload.load(process)
        return false
    }
}
function buildHeadMap(sheet) {
    let head = {}
    let row = sheet.getRow(2)
    row.eachCell((cell, index) => {
        let value = cell.value
        if (value) {
            head[cell.value] = index
        }
    })
    return head
}

window.FormInfo = {
    ReportSchemaCode: 'D282605Seibgfbqg1mf1id55ejcjoz6f3',
    ReportChildSchemaCode: 'D282605F9ca39816aecd46baba26c8004d2d8716'
}
function findForm(form, callback) {
    for (let item of form) {
        if (callback(item)) return item
        if (item.ChildControls && item.ChildControls.length > 0) {
            let result = findForm(item.ChildControls, callback)
            if (result) return result
        }
    }
}
async function validTemplate(sheet) {
    if (!sheet) throw new Error("无法读取模板")
    let resp = await h3yunApi.Load(FormInfo.ReportSchemaCode)
    if (!resp || !resp.Successful || !resp.ReturnData) throw new Error("模板校验失败")

    let { FormContent } = resp.ReturnData
    let form = JSON.parse(FormContent)

    let teamField = sheet.getCell('H1').value
    let team = findForm(form, e => e.Key == 'Team')
    if (!team) throw new Error("模板解析错误")
    if (team.Options.DisplayName != teamField) throw new Error(`模板描述项：[ ${team.Options.DisplayName} ] 不存在`)
    let reportDateField = sheet.getCell('J1').value
    let reportDate = findForm(form, e => e.Key == 'ReportDate')
    if (!reportDate) throw new Error("模板解析错误")
    if (reportDate.Options.DisplayName != reportDateField) throw new Error(`模板描述项：[ ${reportDate.Options.DisplayName} ]不存在`)

    let children = findForm(form, e => e.Key == FormInfo.ReportChildSchemaCode)
    if (!children || children.length == 0) throw new Error("模板解析错误")
    let row = sheet.getRow(2)
    for (let control of children.ChildControls) {
        if (control.Options.ControlKey == 'FormQuery' || control.Options.DisplayRule.Rule == 'TRUE') {
            continue
        }
        if (!hasInRow(row, control.Options.DisplayName)) {
            throw new Error(`模板非法[ ${control.Options.DisplayName} ]列不存在`)
        }
    }
    return form
}
function hasInRow(row, key) {
    for (let i = 1; i <= row.cellCount; i++) {
        if (row.getCell(i).value == key) return true
    }
    return false
}
async function process(file, upload) {
    try {
        let wb = await excelJSApi.readExcel(file)
        let sheet = wb.getWorksheet("报工")
        // 模板校验
        let form = await validTemplate(sheet)
        let teamField = findForm(form, e => e.Key == 'Team')
        let head = buildHeadMap(sheet)
        let team = sheet.getCell('I1').value
        if (!team) {
            throw new Error("请填写申报班组")
        }
        if (!teamField.Options.DefaultItems.includes(team)) {
            throw new Error("填写报工组不存在")
        }
        let reportDate = sheet.getCell('K1').value
        if (!reportDate) reportDate = new Date()

        let result = { 'Team': team, 'ReportDate': reportDate }
        result[FormInfo.ReportChildSchemaCode] = []
        let children = form.find(e => e.Key == FormInfo.ReportChildSchemaCode)
        let errors = []
        for (let i = 3; i <= sheet.rowCount; i++) {
            let row = sheet.getRow(i)
            let item = {}
            for (let control of children.ChildControls) {
                if (control.Options.ControlKey == 'FormQuery' || control.Options.DisplayRule.Rule == 'TRUE') {
                    continue
                }
                if (control.Options.DisplayName in head) {
                    let key = control.Key.substring(FormInfo.ReportChildSchemaCode.length + 1)
                    item[key] = row.getCell(head[control.Options.DisplayName]).value
                }
            }

            if (!item['OrderNo'] || !item['ProductName'] || !item["SizeModel"] || !item['DeclaredQuantity']) {
                errors.push({ "Row": `第 ${i} 行`, "Error": '[通知单编号,产品名称,规格,申报数量]存在未填项' })
                continue
            }
            try {
                parseInt(item['DeclaredQuantity'])
                if (item['DeclaredQuantity'] <= 0) {
                    errors.push({ "Row": `第 ${i} 行`, "Error": '申报数量非法' })
                    continue
                }
            } catch (e) {
                errors.push({ "Row": `第 ${i} 行`, "Error": '申报数量非数字' })
                continue
            }

            result[FormInfo.ReportChildSchemaCode].push(item)
        }
        if (errors && errors.length > 0) {
            upload.putError(errorTip.TableError({
                headers: [
                    { name: "行数", key: "Row" },
                    { name: "错误原因", key: "Error" },
                ]
            }, errors))
            return false
        }
        if (result[FormInfo.ReportChildSchemaCode].length == 0) {
            errorTip.ShowError('模板没有数据')
            return false
        }
        console.log('data', result)
        let resp = await h3yunApi.Submit(FormInfo.ReportSchemaCode, result)
        return processResp(resp, upload)
    } catch (error) {
        console.log(error)
        errorTip.ShowError(error.message || '模板校验错误')
        return false
    }
}

function processResp(resp, upload) {
    let { Successful, ErrorMessage, ReturnData } = resp

    if (Successful) {
        let StartFormResponse = ReturnData?.StartFormResponse

        if (StartFormResponse) {
            if (StartFormResponse.Errors && StartFormResponse.Errors.length > 0) {
                errorTip.ShowError(StartFormResponse.Errors[0])
                return false
            }
            let error = StartFormResponse.ReturnData?.Error
            if (error) {
                errorTip.ShowError(error)
                return false
            }
            let errors = StartFormResponse.ReturnData?.Errors
            if (errors) {
                let errors = JSON.parse(StartFormResponse.ReturnData?.Errors)
                upload.putError(errorTip.TableError({
                    headers: [
                        { name: "订单号", key: "OrderNo" },
                        { name: "产品名称", key: "ProductName" },
                        { name: "规格", key: "SizeModel" },
                        { name: "错误原因", key: "Error" },
                    ]
                }, errors))
            } else {
                window['$'].ListView.RefreshView()
                window['$'].IShowSuccess("上传成功")
                return true
            }
        }

    } else {
        $.IShowError(ErrorMessage || "上传失败")
    }
    return false
}