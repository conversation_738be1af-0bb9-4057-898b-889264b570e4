cd /d C:/develop/work/projects/universiade/schedule

git checkout dev
git pull

call  mvn -B -U -DskipTests clean compile package


@rem 开启ssh 隧道
ssh -CNfg -L 5550:**************:22 root@*************

scp -P 5550 ./target/*.jar root@127.0.0.1:/root/app/schedule

ssh root@127.0.0.1  -p 5550  "cd /root/app/schedule;sh ./restart.sh;exit"


for /f "tokens=2" %%i in ('tasklist^|findstr ssh') do (
    echo "ssh 隧道PID:" %%i
    taskkill /F /PID %%i
)

pause
