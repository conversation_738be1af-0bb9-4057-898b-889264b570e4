import req from './request.js'
import crypto from 'crypto'
function sha256(message, secret = '', encoding) {
    const hmac = crypto.createHmac('sha256', secret)
    return hmac.update(message).digest(encoding)
}
function getHash(message, encoding = 'base64') {
    const hash = crypto.createHash('sha256')
    return hash.update(message).digest(encoding)
}

export default class XunFly {
    constructor() {
        this.option = {
            hostUrl:
                "https://api.xf-yun.com/v1/private/s00b65163",
            host: "webapi.xfyun.cn",
            appId: '2defdef8',
            apiKey: '2586883be1cd40478350d48e8cde1ce1',
            apiSecret: 'YmMzY2FmMDA2NGI0ZDhiMTIyNTBhMjMz',
        }
    }
    async load() {
        let url = `http://xf.aka.today/v3/user_info.php?open_id=1cced4e925ab1f7be4f0436fcbedf132`

        let resp = await req.get(url)
        console.log(resp.data.all_share);
    }

    getApiUrl() {
        const d = this.option;
        let e = new URL(d.hostUrl)
        let t = e.hostname
        let n = e.pathname
        let a = new Date().toUTCString()
        //let a = 'Sun, 11 Dec 2022 13:55:02 GMT'
        let o = `host: ${t}\ndate: ${a}\nPOST ${n} HTTP/1.1`
        //let i = r.HmacSHA256(o, d.apiSecret)

        let i = sha256(o, d.apiSecret)
        let c = Buffer.from(i).toString('base64')
        let l = `api_key="${d.apiKey}", algorithm="hmac-sha256", headers="host date request-line", signature="${c}"`

        return `${d.hostUrl}?host=${t}&date=${encodeURI(a)}&authorization=${Buffer.from(l).toString('base64')}`
    }
    async req({
        img64
    }) {
        let api_url = this.getApiUrl()
        let api_id = this.option.app_id

        let body = { "header": { "app_id": api_id, "status": 3 }, "parameter": { "s00b65163": { "category": "mix0", "result": { "encoding": "utf8", "compress": "raw", "format": "json" } } }, "payload": { "s00b65163_data_1": { "encoding": "png", "image": img64, "status": 3 } } }
        console.log(api_url);
        let error = {
            success: false,
            message: '请求解析失败',
        }
        try {
            let resp = await req.post(api_url, {
                headers: {
                    'content-type': 'application/json',
                    'api_id': api_id
                },
                json: { "header": { "app_id": "2defdef8", "status": 3 }, "parameter": { "s00b65163": { "category": "mix0", "result": { "encoding": "utf8", "compress": "raw", "format": "json" } } }, "payload": { "s00b65163_data_1": { "encoding": "png", "image": img64, "status": 3 } } }
            })
            let text = resp?.payload?.result?.text
            if (resp && resp.header['code'] === 0 && text) {
                let result = JSON.parse(Buffer.from(text, 'base64').toString())
                let pages = result?.pages
                if (pages && pages.length > 0) {
                    let result = pages[0]?.lines[0]?.words[0]?.content
                    if (result) {
                        return {
                            success: true,
                            result
                        }
                    }

                }

            }

        } catch (e) {
        }
        return error

    }
}

; (async () => {
    let fly = new XunFly()
    //fly.load()
    let resp = await fly.req({
        "img64": `iVBORw0KGgoAAAANSUhEUgAAAMgAAABkCAMAAAD0WI85AAAAP1BMVEUAAAB1KElsH0CpXH1kFzhQAySdUHFlGDmCNVZoGzysX4BYCyy0Z4iGOVrCdZZPAiOJPF1PAiNXCiuxZIXDdpdHIGH7AAAAAXRSTlMAQObYZgAABItJREFUeJzsm41u6yoMgO2mUlStmib1/d/16iYBbGODk9Clh+EjHXVJcPzhHwxVYcgQt8zzfLUJTWSeOyEZIB8nvXAMqQsiXm1CE0HshGSAfJz0wjHk+XxebUITeT47IRkgHye9cAzpRRA6WH+nTjqiaZqwE49gFw7Z4uo0CF7uVMTzJEtsohWh9/v9hG7jfcqLEM7myDYRxnTc761J9PcUTNihd0PRZqo9iP6eBaEBCIT5kIpag6BubSko/LrX0SFVhDTimKYJosGaCWhGnVvI6ExTq1o2TYFEtZaAnCtbTGHCOluW09gNxCiy8Uq7VYBGV4jao9ppUq8gMROMR5uu7ZhSBmlF26lFGrVG1lqbsqqVPNIeBOnysle/Wc5DjS2BON7k234GN4cU2Q9CqrmmOruDNJwdb/JtpOmqgumSeHVhvLkkmUVWgDhIHByLrhtPyQwkwGpVJoaVqjmmXzYi/KFP0rfLdCovQLzdbtx0RF7LWCWQxto1NHDk/kUVinB87yV5vRaSzBoyiaQ1y31idCDbQFBJUHpfGXvEI1p2lzwibbITKNUmyAJVpTonSsFBzAoLguKT8nrDq4dlejMQNfTNdMSsupU1k0H0unx/Aw6t2JamGh2pGm6CvlooHm7Sc6nrXymFjeUsnwsCzAqwzDnVgoMgrovJDPmZtAWqgjxJLBAk/x8B0bohn0ukaboKDCWjgLLcXj8dcJCx4yw5m04f58jiib1GNL3kH6UxGn8HyE6HkJvhQ7IBUzawtJDtWF4jo1MOZow1Tj+LAnEclexPuUIgyQjuEcuKTZHyWHWlz0MboFS0+NMo+kWqjO/XrAZZcFhm1novS0N+DfWaj+LRLA/w8XjU9jjoSPAah7EhUjyiMkuPAA3KRfXjf8HSGRYeTovMEF+OlFzCdQJpFRcQQF1ltOEsiK2j5BGZJMUN+QoCZiFEJAWjPYl6jZV9MPvGOHZ9KHAYhxBpP2yVyfMgtVHBFLNNq1ZC4d2jFKxeGLMKtLEg9yEt4gqzNbtKqWjTwceeQp4OxHKUqkFwv9xh5C6x65PYKKor31ESba3CTJTF1/RIYe+L4rFGFLJJQoUD+EdS8lH0W8lYZ7w0xNCLanZJegQUFNml+F6839jCTWubk+KtuvlgTnCBHN1xqJWnjmIeVCbHQEp4Ug6KJgZ3Ln9s3wb5QSrTpMBUjo6F42jMVaaaPhK/19pBArHXd1i2UpePjo1YrAQW/yp3J0h+Thi3POm2KEoOpflZSDWupP69IEEJfTNtn2m22iRi8Y/BLqq0+X7tmOMAB50POY9iVbP7baGMFV3gyjOMOuouFtJ3xE7EkaFpMOuC+T2QSRYgAbT5agHDps+7xiqprTzBnAOkTDd0h/LWbSa9Q8i/eZ4djT7SBugdFHCs/0/OqP1gjXfOb6Q4KNEjjl/exf1Bk6P2d8n4CeGQIUP+inxdbUAj+frqhuRqA0z54GV7l7ynpb1CeuF4rzyuNqCRpG9XNPn5+flFW85JheMfIinIAPk46YXjU6SXlfRdx1y/Ln8X5L8AAAD//17DEBKSqfXuAAAAAElFTkSuQmCC`
    })
    console.log(resp);
})();
