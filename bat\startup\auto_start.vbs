Set ws = CreateObject("Wscript.Shell")

ws.run "netsh interface ip set dns local static *********** primary",vbhide
WScript.Sleep 1000
ws.Run("cmd /K rmdir C:\\ProgramData\\Kuwodata\\kwmusic2013\\ModuleData\\ModPlayList /S /Q && mkdir C:\\ProgramData\\Kuwodata\\kwmusic2013\\ModuleData\\ModPlayList"),vbhide
ws.run "D:\Software\System\Everything\Everything.exe -startup",vbhide
ws.run "D:\Software\System\Memreduct\memreduct.exe",vbhide
ws.run "D:\Software\System\TrafficMonitor\TrafficMonitor.exe",vbhide
'WScript.Sleep 20000

WScript.Sleep 2000
ws.run "netsh interface ip set dns local static ************* primary",vbhide
ws.run "C:\Users\<USER>\AppData\Local\Programs\utools\uTools.exe",vbhide

WScript.Sleep 2000
ws.run "D:\Software\Tool\PixPin\PixPin.exe",vbhide
ws.run "D:\Software\Tool\IDM\IDMan.exe /onboot",vbhide


Dim fso, ts, script,count
Set fso = CreateObject("Scripting.FileSystemObject")
Set ts = fso.OpenTextFile("ping_check.vbs", 1) ' 1 for ForReading
script = ts.ReadAll
ts.Close
' Execute the content of the other VBS file
ExecuteGlobal script


count = 30
Do Until count = 0
    result = PingCheck("*************")
    if result Then
        ws.run "mstsc D:\Data\Sync\Log\bat\startup\*************.rdp"
        Exit Do
    Else
        WScript.Sleep 2000
        count = count -1
    End If
Loop


WScript.Sleep 2000
ws.run "C:\Users\<USER>\AppData\Local\Programs\cursor\Cursor.exe"
ws.run "D:\Software\Develop\Chrome\chrome.exe"

WScript.Sleep 2000
ws.CurrentDirectory = "D:\Software\Tool\Proxifier"
ws.run "D:\Software\Tool\Proxifier\Proxifier.exe",vbhide