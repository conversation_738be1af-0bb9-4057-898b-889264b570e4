using System;
using System.Collections.Generic;
using System.Text;
using H3;

public class Sahj8417jpmkya2ndghgvorg83 : H3.SmartForm.SmartFormController
{
    public Sahj8417jpmkya2ndghgvorg83(H3.SmartForm.SmartFormRequest request) : base(request) { }

    protected override void OnLoad(H3.SmartForm.LoadSmartFormResponse response)
    {
        base.OnLoad(response);
    }

    protected override void OnSubmit(
        string actionName,
        H3.SmartForm.SmartFormPostValue postValue,
        H3.SmartForm.SubmitSmartFormResponse response
    )
    {
        base.OnSubmit(actionName, postValue, response);
        // if ((actionName == "Submit" || actionName == "Save") && response.Successful)
        // {
        //     //跟进记录修改或插入数据
        //     H3.DataModel.BizObject[] records = (H3.DataModel.BizObject[])this.Request.BizObject["F34685c8b5e494255b73f944d259ec9f9"];
        //     if (records != null && records.Length > 0)
        //     {
        //         FollowUpRecord followUpRecord = new FollowUpRecord(this.Engine, this.Request.UserContext.UserId);
        //         foreach (H3.DataModel.BizObject record in records)
        //         {
        //             followUpRecord.Create(record, this.Request.BizObject);
        //         }
        //     }
        // }

        Sahj8417jpmkya2ndghgvorg83_Timer timer = new Sahj8417jpmkya2ndghgvorg83_Timer();
        timer.exec(this.Engine);
    }
}

public class Sahj8417jpmkya2ndghgvorg83_Timer : H3.SmartForm.Timer
{
    //构造方法，跟类名保持一致，里面不必书写代码，但是必须存在
    public Sahj8417jpmkya2ndghgvorg83_Timer() { }

    public void exec(H3.IEngine engine)
    {
        this.OnWork(engine);
    }
    private H3.IEngine engine;
    private string userId;
    //重写定时器引擎执行的方法，必须存在，且方法名必须为OnWork
    //此方法每隔4小时调用一次
    protected override void OnWork(H3.IEngine engine)
    {
        this.userId = H3.Organization.User.SystemUserId;
        this.engine = engine;
        //查询客户信息 已跟进
        H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();
        H3.Data.Filter.And and = new H3.Data.Filter.And();
        and.Add(
            new H3.Data.Filter.ItemMatcher("********", H3.Data.ComparisonOperatorType.Equal, "已跟进")
        );
        filter.Matcher = and;
        H3.DataModel.BizObject[] customers = H3.DataModel.BizObject.GetList(
            engine,
            this.userId,
            engine.BizObjectManager.GetPublishedSchema("Sahj8417jpmkya2ndghgvorg83"),
            H3.DataModel.GetListScopeType.GlobalAll,
            filter
        );

        if (customers == null || customers.Length == 0)
        {
            return;
        }

        foreach (H3.DataModel.BizObject customer in customers)
        {
            DateTime latestTime = Convert.ToDateTime(customer["F0000039"]);
            TimeSpan timeSpan = DateTime.Now.Subtract(latestTime);
            H3.Organization.User user = (H3.Organization.User) this.engine.Organization.GetUnit(customer["OwnerId"] + string.Empty);

            if (timeSpan.Days >= 45 || user.State != H3.Organization.State.Active) //跟进天数超过45天 掉库
            {
                H3.DataModel.BizObjectSchema customerOfSeaSchema = engine.BizObjectManager.GetPublishedSchema("D278151Sw1hb0g4hq7znsg6yq5fcbzxi2");
                H3.DataModel.BizObject customerOfSea = new H3.DataModel.BizObject(engine, customerOfSeaSchema, this.userId);
                this.copyObj(customer, customerOfSea);
                customer.Update();
                customerOfSea["OwnerId"] = " ";
                customerOfSea["OwnerDeptId"] = " ";
                customerOfSea.Create();

            }
            else if (timeSpan.Days >= 7  && timeSpan.Days < 45) //未跟进变更
            {
                customer["********"] = "未跟进";
                customer.Update();
            }

            //string ownerId = customer["OwnerId"] + string.Empty;
            //H3.Organization.User owner = engine.Organization.GetUnit(ownerId) as H3.Organization.User;
        }
    }

    private H3.DataModel.BizObject copyObj(
        H3.DataModel.BizObject customer,
        H3.DataModel.BizObject customerOfSea)
    {
        H3.DataModel.BizObjectSchema customerOfSeaSchema = customerOfSea.Schema;
        Dictionary<string, object> table = customer.GetValueTable();
        foreach (KeyValuePair<string, object> entry in table)
        {
            string key = entry.Key;
            object val = entry.Value;

            if (key == "F34685c8b5e494255b73f944d259ec9f9") //跟进记录
            {
                customerOfSea["D278151Fcmuo73gd8w0ngettezqehgbn1"] = copyChild(
                    customerOfSeaSchema.GetChildSchema("D278151Fcmuo73gd8w0ngettezqehgbn1"),
                    key,
                    val
                );
                continue;
            }
            if (key == "Fmvl8idez8o6hkgrlq8wdhbl20") //联系人
            {
                customerOfSea["D278151Fwbdlojy2hbu0rdk0jni2sd7o1"] = copyChild(
                    customerOfSeaSchema.GetChildSchema("D278151Fwbdlojy2hbu0rdk0jni2sd7o1"),
                    key,
                    val
                );
                continue;
            }
            if (key == "********")
            {
                customer[key] = "未跟进"; //未跟进
                customerOfSea[key] = "未跟进"; //未跟进
                continue;
            }
            if (key.StartsWith("Owner"))
            {
                continue;
            }

            customerOfSea[key] = val;
        }
        return customerOfSea;
    }

    private H3.DataModel.BizObject[] copyChild(
        H3.DataModel.BizObjectSchema childSchema,
        string prefix,
        object val
    )
    {
        if (val == null)
        {
            return null;
        }
        H3.DataModel.BizObject[] src = (H3.DataModel.BizObject[])val;
        List<H3.DataModel.BizObject> list = new List<H3.DataModel.BizObject>();

        int startIndex = prefix.Length + 1;
        foreach (H3.DataModel.BizObject bo in src)
        {
            H3.DataModel.BizObject child = new H3.DataModel.BizObject(
                this.engine,
                childSchema,
                this.userId
            ); //子表对象
            Dictionary<string, object> table = bo.GetValueTable();
            foreach (KeyValuePair<string, object> entry in table)
            {
                string key = entry.Key.Substring(startIndex);
                object value = entry.Value;
                child[key] = value;
            }
            list.Add(child);
        }

        return list.ToArray();
    }
}
