import req  from '../util/request.js'
import Notify from '../util/notify.js'
const notify = new Notify();

let url = 'http://ttsqq.top'
    ; (async () => {
        let {body,headers} = await req.post(`${url}/wp-json/jwt-auth/v1/token`, {
            form: {
                'username': '18280045458',
                'password': 'mT9e1GJQL8Ho'
            }
        }, {_auto:false})
        body = JSON.parse(body)
        let resp= await req.post(`${url}/wp-json/b2/v1/userMission`, {'headers': {
            'Cookie': headers['set-cookie'],
            'Authorization': 'Bearer ' + body['token']
        }})
        let result = 'ttsqq.top 签到奖励\n'
        if(resp.credit) {
            result += '今日签到：' + resp.credit
            result += '\n总积分：' + resp.mission.my_credict
        } else {
            result += resp
        }

        notify.sendText(result)

    })();