/* 控件接口说明：
 * 1. 读取控件: this.***,*号输入控件编码;
 * 2. 读取控件的值： this.***.GetValue();
 * 3. 设置控件的值： this.***.SetValue(???);
 * 4. 绑定控件值变化事件： this.***.Bind<PERSON><PERSON><PERSON>(key,function(){})，key是定义唯一的方法名;
 * 5. 解除控件值变化事件： this.***.UnbindChange(key);
 * 6. CheckboxList、DropDownList、RadioButtonList: $.***.AddItem(value,text),$.***.ClearItems();
 */
/* 公共接口：
 * 1. ajax：$.SmartForm.PostForm(actionName,data,callBack,errorBack,async),
 *          actionName:提交的ActionName;data:提交后台的数据;callback:回调函数;errorBack:错误回调函数;async:是否异步;
 * 2. 打开表单：$.IShowForm(schemaCode, objectId, checkIsChange)，
 *          schemaCode:表单编码;objectId;表单数据Id;checkIsChange:关闭时，是否感知变化;
 * 3. 定位接口：$.ILocation();
 */
!( function( window ) {
  function DocumentApi() {
    this.doc = document
  }
  DocumentApi.prototype.findSelector = function( selector, element ) {
    if( !selector ) return
    if( !element ) element = this.doc.body
    if( element.className && ( element.className + "" ).indexOf( selector ) >= 0 )
      return element
    if( element.id == selector ) return element

    let children = element.children

    if( children && children.length > 0 ) {
      for( let child of children ) {
        let e = this.findSelector( selector, child )
        if( e ) return e
      }
    }
  }

  DocumentApi.prototype.insertHeadStyle = function( css, className = "" ) {
    if( this.findSelector( className, this.doc.head ) ) return
    let headFrag = this.doc.createDocumentFragment()
    headFrag.appendChild( this.createCSSNode( css, className ) )
    this.doc.head.appendChild( headFrag )
  }
  DocumentApi.prototype.insertScript = function( src ) {
    let script = this.doc.createElement( "script" )
    script.type = "text/javascript"
    script.src = src
    this.doc.body.appendChild( script )
  }

  DocumentApi.prototype.createCSSNode = function( css, className = "", initType = "text/css" ) {
    let cssNode = this.doc.createElement( "style" )
    if( className ) {
      cssNode.className = className
      const xclass = "." + className.split( " " ).join( "." )
      cssNode.dataset.xclass = xclass
    }

    cssNode.setAttribute( "type", initType )
    cssNode.appendChild( this.doc.createTextNode( css ) )
    return cssNode
  }

  DocumentApi.prototype.createElement = function( content ) {
    return this.doc.createElement( content )
  }

  DocumentApi.prototype.createElementHtml = function( content ) {
    let node = this.doc.createElement( "div" )
    node.innerHTML = content
    return node.children[ 0 ]
  }

  DocumentApi.prototype.download = function( url, saveName ) {
    if( typeof url == 'object' && url instanceof Blob ) {
      url = URL.createObjectURL( url ) // 创建blob地址
    }
    var aLink = this.doc.createElement( 'a' )
    aLink.href = url
    aLink.download = saveName || '' // HTML5新增的属性，指定保存文件名，可以不要后缀，注意，file:///模式下不会生效
    var event
    if( window.MouseEvent ) event = new MouseEvent( 'click' )
    else {
      event = document.createEvent( 'MouseEvents' )
      event.initMouseEvent( 'click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null )
    }
    aLink.dispatchEvent( event )
  }
  window.docApi = new DocumentApi()
  docApi.insertHeadStyle( ".code-check-warn {display:none} ", "code-check" )
})( window );
!( function( window ) {
  function ExcelJSApi() {
    if( !window[ "ExcelJS" ] ) {
      docApi.insertScript(
        "https://www.h3yun.com/Form/DoPreview/?AttachmentID=9d8b3475-35c9-4a45-9a86-2251513b68f5"
      )
    }
  }
  ExcelJSApi.prototype.loadTemplate = async function( template ) {
    let data = await this.getTemplate( template )
    let excel = await this.getExcel()
    let wb = new excel.Workbook()
    await wb.xlsx.load( data )
    this.workbook = wb
    return wb
  }

  ExcelJSApi.prototype.getTemplate = function( template ) {
    return new Promise(( resolve, reject ) => {
      window.$.ajax( {
        type: "POST",
        url: "/App/OnAction/",
        data: { PostData: JSON.stringify( { Command: "Download", ActionName: "DoAction", QueryCode: "D2826050ff2122070c84a41be2f42c1ac623b4a", ObjectId: template }) },
        dataType: "json",
        async: false,
        success: function( resp ) {
          if( resp && resp.Successful ) {
            let data = resp ?.ReturnData ?.Response ?.ReturnData
            if ( !data ) return resolve()
            window.axios.get( data[ "Url" ], { responseType: "blob" })
              .then(( resp ) => resolve( resp ) )
              .catch(( e ) => resolve() )
          } else {
            resolve()
          }
        }
      })
    })
  }
  ExcelJSApi.prototype.getExcel = function() {
    let count = 1
    function wait( resolve ) {
      if( window[ 'ExcelJS' ] ) resolve( window[ 'ExcelJS' ] )
      else {
        if( count > 50 ) resolve()
        else {
          count++
          setTimeout(() => {
            wait( resolve )
          }, 100 )
        }
      }
    }
    return new Promise(( resolve ) => {
      wait( resolve )
    })
  }
  ExcelJSApi.prototype.writeExcel = async function( filename ) {
    let wbout = await this.workbook.xlsx.writeBuffer()
    let blob = new Blob( [ wbout ], {
      type: "application/octet-stream"
    })

    docApi.download( blob, filename )
  }
  ExcelJSApi.prototype.removeRows = async function( sheet, from = 1, to ) {
    if( !to ) to = sheet.rowCount
    for( let i = from;i <= to;i++ ) {
      sheet.spliceRows( from, 1 )
    }
  }

  ExcelJSApi.prototype.readExcel = function( file ) {
    let _this = this
    return new Promise(( resolve ) => {
      let reader = new FileReader();
      reader.onload = async function( e ) {
        let excel = await _this.getExcel();

        if( excel ) {
          let wb = new excel.Workbook()
          await wb.xlsx.load( e.target.result )
          resolve( wb );
        } else {
          $.IShowError( "读取Excel失败" );
        }
      };
      reader.readAsArrayBuffer( file );
    });
  }

  window.excelJSApi = new ExcelJSApi()
})( window );

!( function( window ) {
  function ErrorTip() {
    docApi.insertHeadStyle( `
    .error-modal {}
    .error-table {
      display:flex;
      flex-direction: column;
      width: 100%;
      border: thin solid #e5e5e5;
      margin-top:10px;
    }
    .error-table .error-table-head{
      font-weight: bold;
    }
    .error-table .error-table-head,.error-table .error-table-body{
      display:flex;
      flex:1;
    }
    .error-table .error-table-body {
      flex-direction: column;
    }
    .error-table .error-table-head > div, .error-table .error-table-body .error-table-row > div{
      flex:1;
      padding: 5px;
      word-wrap: break-word;
      word-break: break-all;
    }
    .error-table .error-table-head > div:last-child, .error-table .error-table-body .error-table-row > div:last-child{
      flex:2;
    }
    .error-table .error-table-head > div:not(:last-child) {
      border-right: thin solid #e5e5e5;
    }
    .error-table .error-table-body .error-table-row {
      border-top: thin solid #e5e5e5;
    }
    .error-table .error-table-body .error-table-row > div:not(:last-child) {
      border-right: thin solid #e5e5e5;
    }
    .error-table .error-table-body .error-table-row {
      display:flex;
    }
    `, 'custom-error-tip' )
  }

  ErrorTip.prototype.TableError = function( config, data ) {
    if( !config || !config.headers ) return

    let $error = docApi.createElement( 'div' )
    $error.classList.add( 'error-table' )

    $error.innerHTML = ""
    $error.style.display = "block"

    let header = '<div class="error-table-head">'
    for( let head of config.headers ) {
      let style = head.head_style || head.style
      header += `<div ${ style ? 'style="' + style + '"' : '' }>${ head.name }</div>`
    }
    header += '</div>'

    let body = '<div class="error-table-body">'
    for( let item of data ) {
      let row = `<div class="error-table-row">`
      for( let head of config.headers ) {
        let style = head.row_style || head.style
        let value = item[ head.key ]
        if( head.func && typeof head.func == "function" ) {
          value = head.func( item[ head.key ] )
        }
        row += `<div ${ style ? 'style="' + style + '"' : '' }>${ value }</div>`
      }
      row += '</div>'
      body += row
    }
    body += '</div>'
    $error.innerHTML = header + body

    return $error
  }

  ErrorTip.prototype.ShowTableError = function( config, data ) {
    if(('dd' in window) && dd.android) {
      let message = '',tip = ''
       for( let head of config.headers ) {
         tip+= head.name + '_'
       }
       tip = tip.substr(0, tip.length - 1)
      for(let item of data) {
        let title = ''
        for(let head of config.headers) {
          title += item[head.key] + '_'
        }
        title = title.substr(0, title.length - 1)
        message +=title + '\n'
      }
      dd.device.notification.alert({
          message,
          title: "提示： " + tip,//可传空
          buttonName: "确认",
          onSuccess : function() {
          },
          onFail : function(err) {}
      });

      return
    }
    let $error = this.TableError( config, data )
    let $modal = window[ '$' ].IModal( {
      Title: config.title,
      Content: $error,
      ShowFooter: false,
      ToolButtons: [],
      Class: 'error-modal',
      Width: "80%"
    });
  }
  ErrorTip.prototype.ShowError = function( message ) {

    if('dd' in window) {
      if(dd.android) {
        dd.device.notification.alert({
          message: message,
          title: "提示",//可传空
          buttonName: "确认",
          onSuccess : function() {
              //onSuccess将在点击button之后回调
              /*回调*/
          },
          onFail : function(err) {}
       });
       return
      }
    }

    window[ '$' ].IShowError( message );
  }

  window.errorTip = new ErrorTip()
})( window );

!( function( window ) {
  function H3YunApi() { }

  H3YunApi.prototype.getTemplate = async function( template ) {
    return new Promise(( resolve, reject ) => {
      window.$.ajax( {
        type: "POST",
        url: "/App/OnAction/",
        data: { PostData: JSON.stringify( { Command: "Download", ActionName: "DoAction", QueryCode: "D2826050ff2122070c84a41be2f42c1ac623b4a", ObjectId: template }) },
        dataType: "json",
        async: false,
        success: function( resp ) {
          if( resp && resp.Successful ) {
            let data = resp ?.ReturnData ?.Response ?.ReturnData
                        if ( !data ) return resolve()
            window.axios.get( data[ "Url" ], { responseType: "blob" })
              .then(( resp ) => resolve( resp ) )
              .catch(( e ) => resolve() )
          } else {
            resolve()
          }
        }
      })
    })
  }

  H3YunApi.prototype.Submit = function( schemaCode, body ) {
    return new Promise(( resolve, reject ) => {
      let value = {
        "Command": "Submit",
        "Data": body
      }
      let PostData = JSON.stringify( {
        "ActionName": "DoAction",
        "Command": "Submit",
        "PostValue": JSON.stringify( value ),
        "SchemaCode": schemaCode,
        "Mode": "Create",
        "BizObjectId": '',
        "IsExternalForm": "False",
        "IsExternalShare": "False"
      })
      window.axios.post( '/form/OnAction', { PostData }).then( resp => resolve( resp ) )
        .catch(() => resolve( { ErrorMessage: '请求失败', Successful: false }) )
    })
  }

  H3YunApi.prototype.Load = async function( schemaCode ) {
    try {
      let PostData = JSON.stringify( {
        "ActionName": "Load",
        "SchemaCode": schemaCode,
        "BizObjectId": "",
        "SideModal": true, "WorkItemID": "",
        "IsExternalForm": false,
        "IsExternalShare": false,
        "ParentBizObjectId": "", "IsNewForm": true,
        "ddIngPid": "", "ddIngTid": "",
        "TimeStamp": -1
      })
      let resp = await window.axios.post( '/form/OnAction', { PostData })
      return resp
    } catch( e ) {
      return { ErrorMessage: e.message, Successful: false }
    }
  }

  H3YunApi.prototype.DoAction = async function( schemaCode, command, objectIds ) {
    try {
      let PostData = JSON.stringify( {
        "Command": command,
        "QueryCode": schemaCode,
        "ActionName": "DoAction",
        "ObjectIds": objectIds,
      })
      let resp = await window.axios.post( '/App/OnAction/', { PostData })
      return resp
    } catch( e ) {
      return { ErrorMessage: e.message, Successful: false }
    }
  }


  H3YunApi.prototype.DoReq = async function( command, objectIds ) {
    try {
      let PostData = JSON.stringify( {
        "Command": command,
        "QueryCode": 'D2826050efb644dfb80457989b99b4b70b6c35d',
        "ActionName": "DoAction",
        "ObjectId": JSON.stringify( objectIds ),
      })
      let resp = await window.axios.post( '/App/OnAction/', { PostData })
      if( !resp.Successful ) {
        throw new Error( resp.ErrorMessage )
      } else {
        let response = resp ?.ReturnData ?.Response
                if( response ) {
          if( !response.Successful ) {
            throw new Error( response.Errors[ 0 ] || '请求失败' )
          }

          return response
        }
        throw new Error( "请求接口失败" )
      }
    } catch( e ) {
      return { ErrorMessage: e.message || '请求接口失败', Successful: false }
    }
  }
  window.h3yunApi = new H3YunApi()

})( window )
async function debug() {
    docApi.insertScript(
        "https://cdn.bootcdn.net/ajax/libs/vConsole/3.15.1/vconsole.min.js"
    )
    let VConsole = await hasVariable('VConsole')
    console.log('--show console', VConsole, window)
    if(VConsole) {
      new VConsole()
    }

    async function hasVariable(name) {
      let count = 0, max = 50;
      while(count < max) {
        if(name in window) return window[name]
        await wait(0.5)
        count ++
      }
    }

    function wait(seconds) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve()
        },seconds * 1000)
      })
    }
}
function test() {
  try {
    console.log($h3.modal.toString())
     let jq = window[decodeURIComponent('%24')]()
     console.log('window', window)




  } catch(e) {
    console.log(e)
  }
}
window.FormInfo = {
  SchemaCode: 'D2826052468b3ca3f39406a80a5ee6055d35ec1',
  ChildSchemaCode: 'D282605Fd8a58da6e52d4efe942395b6048e06f0'
}
// 表单插件代码
$.extend( $.JForm, {
  // 加载事件
  OnLoad: function() {
     // debug()
    // test()
    let $this = this
    let child_control = this[ FormInfo.ChildSchemaCode ]
    let remain_table = {}
    child_control.BindChange( 'child-change', function( change_row ) {
      let change = change_row[ 0 ]
      let object_id = change[ 'ObjectId' ]

      let rows = child_control.GetValue()
      let row = rows.find( e => object_id == e[ 'ObjectId' ] )
      if( !row ) return

      let waiting_field = `${ FormInfo.ChildSchemaCode }.ReportPlan`
      if( change[ 'DataField' ] == waiting_field ) {

        let keys = Object.keys( child_control.GetValue()[ 0 ] )

        keys.forEach( key => {
          let manager = child_control.GetCellManager( object_id, `${ FormInfo.ChildSchemaCode }.${ key }` )
          manager && manager.SetReadonly( false )
        })
        let report_plan = change.GetValue()
        if( report_plan ) {
          let excludes = [ 'ObjectId', 'ReportPlan', 'DeclaredQuantity', 'ProcessCount', 'Process', 'Remark', 'Operator', 'UsageTime' ] //'ProductType'

          keys.forEach( key => {
            if( excludes.includes( key ) ) return
            let manager = child_control.GetCellManager( object_id, `${ FormInfo.ChildSchemaCode }.${ key }` )
            manager && manager.SetReadonly( true )
          })
          h3yunApi.DoReq( 'QueryTable', { SchemaCode: 'D282605F80a2dc5e16c74b21813e7f1b5ad3615b', "ParentObjectId": report_plan })
            .then( resp => {
              if( resp.Successful ) {
                let {ReturnData} = resp
                let data = ReturnData ? JSON.parse( ReturnData.Data ) : []
                remain_table[ report_plan ] = data
                console.log('----data', data)
                setRemain()
              }
            })

        }
      }
      let process_field = `${ FormInfo.ChildSchemaCode }.Process`
      if( change[ 'DataField' ] == process_field ) {
        let process = change.GetValue()
        let manager = child_control.GetCellManager( object_id, waiting_field )

        let report_plan = row[ 'ReportPlan' ]
        let exist = rows.find( e => e[ 'ReportPlan' ] == report_plan && e[ 'ObjectId' ] != object_id && e[ 'Process' ] == process )
        if( exist ) {
          change.SetValue('')
          manager.SetValue( null )
          errorTip.ShowError( "该工序明细的已存在，请勿选择重复工序报工明细" )
          return
        }
        setRemain()
      }
      let process_flow_field = `${ FormInfo.ChildSchemaCode }.ProcessFlow`
      if( change[ 'DataField' ] == process_flow_field ) {
        let process_control = child_control.GetCellManager( object_id, `${ FormInfo.ChildSchemaCode }.Process` )
        process_control.ClearItems();
        let process_flow = row[ 'ProcessFlow' ]
        if( process_flow ) {
          let process_items = process_flow.split( [ ';' ] )
          for( let item of process_items ) {
            process_control.AddItem( item )
          }
        }
      }
    })


    function setRemain() {
      let rows = child_control.GetValue()

      for( let row of rows ) {
        let process = row[ 'Process' ]
        let report_plan = row[ 'ReportPlan' ]
        let object_id = row[ "ObjectId" ]
        let remain = child_control.GetCellManager( object_id, `${ FormInfo.ChildSchemaCode }.Remain` )
        let count = child_control.GetCellManager( object_id, `${ FormInfo.ChildSchemaCode }.Count` )
        console.log('---plans', remain_table[ report_plan ])
        let plans = remain_table[ report_plan ]
        if( !plans || plans.length == 0 ) {
          remain.SetValue( count.GetValue() )
          continue
        }
        console.log('---group', toGroup(process))
        let plan = plans.find( e => e.ProcessName == toGroup(process) )

        if( plan ) {
          remain.SetValue( plan.Remain )
        } else {
          //remain.SetValue( count.GetValue() )
          remain.SetValue(0)
        }
      }
    }

    function toGroup(process) {
      let groups = {
        '钣金': [ "冲压", "剪板", "折弯", "激光"],
        '辊轧': [ "带冲", "辊轧"],
        '焊接': ['焊接'],
        '喷塑&打包': [ "喷塑", "打包"]
      }
      for(let key of Object.keys(groups)) {
        if(groups[key].includes(process)) {
          return key
        }
      }
      return ''
    }

  },

  // 按钮事件
  OnLoadActions: function( actions ) {
  },

  // 提交校验
  OnValidate: function( actionControl ) {
    return true;
  },

  // 提交前事件
  BeforeSubmit: function( action, postValue ) {
  },

  // 提交后事件
  AfterSubmit: function( action, responseValue ) {
    let {ReturnData} = responseValue
    if( ReturnData && ( "Error" in ReturnData || "Errors" in ReturnData ) ) {

      if( ReturnData.Error ) {
        errorTip.ShowError( ReturnData.Error );
        responseValue.Successful = false;
        return;
      }
      if( ReturnData.Errors ) {
        errorTip.ShowTableError( {
          headers: [
            {
              name: "位置", key: "Index", func: ( v ) => {
                try {
                  let num = parseInt( v )
                  let page = Math.floor( num / 10 ) + 1
                  let cursor = num % 10
                  return `第${ page }页，第${ cursor }行`
                } catch( e ) { return '' }
              }
            },
            { name: "订单号", key: "OrderNo" },
            { name: "产品名称", key: "MaterialName" },
            { name: "申报数量", key: "Quantity" },
            { name: "错误原因", key: "Error", style: "color: red" },
          ]
        }, JSON.parse( ReturnData.Errors ) )
        responseValue.Successful = false;
      }
    }

    console.log( "responseValue---", responseValue, action )
  }
});