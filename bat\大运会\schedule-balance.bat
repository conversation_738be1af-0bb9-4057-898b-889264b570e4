cd /d C:/develop/work/projects/universiade/schedule

git checkout master
git pull

call  mvn -B -U -DskipTests clean compile package

scp ./target/*.jar root@*************:/root/app/schedule

set balance_addr=root@**************

set deploy_cmd=cd /root/app/schedule;^
sh ./restart.sh;^
echo \"restart schedule\";

set cp_cmd=scp ./*.jar %balance_addr%:/root/app/schedule;echo \"copy jar to balance addr\";
set exit_cmd=exit;

set balance_cmd=ssh %balance_addr% \"%deploy_cmd%%exit_cmd%\";

ssh root@************* "%deploy_cmd%%cp_cmd%%balance_cmd%%exit_cmd%"
pause