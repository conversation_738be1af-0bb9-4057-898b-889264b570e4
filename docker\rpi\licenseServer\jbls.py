#!/usr/bin/python
# coding:utf-8

# 2018/2/5 9:52
# JetBrains License Server
# Support both Python 2 and Python 3
# Licensed under GPL v2.
# USE AT YOUR OWN RISK!!!

from __future__ import unicode_literals

__author__ = 'Benny <<EMAIL>>'
__version__ = '1.1.2'

import binascii
import socket
import sys

from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import padding
from flask import Flask, request

app = Flask(__name__)

# FAKE KEY: replace with your own key!!
KEY = b'''
******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'''


@app.route("/rpc/obtainTicket.action")
def obtain():
    """
    obtain ticket is the most important method for verification a JetBrains' product.
    :return: an hex value and XML.
    """
    salt = request.args.get('salt')
    user_name = request.args.get('userName')
    content = "<ObtainTicketResponse><message></message>" + \
              "<prolongationPeriod>607875500</prolongationPeriod><responseCode>OK</responseCode>" + \
              "<salt>" + salt + "</salt><ticketId>1</ticketId>" \
                                "<ticketProperties>licensee=" + user_name + "\tlicenseType=0\t</ticketProperties>" \
                                                                            "</ObtainTicketResponse>"

    verification_hex = hex_signature(content)
    return "<!-- " + verification_hex + " -->\n" + content


@app.route("/rpc/releaseTicket.action")
def release():
    salt = request.args.get('salt')
    content = "<ReleaseTicketResponse><message></message><responseCode>OK</responseCode>" \
              "<salt>" + salt + "</salt>" \
                                "</ReleaseTicketResponse>"
    verification_hex = hex_signature(content)
    return "<!-- " + verification_hex + " -->\n" + content


@app.route("/rpc/prolongTicket.action")
def prolong():
    salt = request.args.get('salt')
    content = "<ProlongTicketResponse><message></message><responseCode>OK</responseCode>" \
              "<salt>" + salt + "</salt><ticketId>1</ticketId></ProlongTicketResponse>"
    verification_hex = hex_signature(content)
    return "<!-- " + verification_hex + " -->\n" + content


@app.route("/rpc/ping.action")
def ping():
    salt = request.args.get('salt')
    content = "<PingResponse><message></message><responseCode>OK</responseCode>" \
              "<salt>" + salt + "</salt></PingResponse>"
    verification_hex = hex_signature(content)
    return "<!-- " + verification_hex + " -->\n" + content


@app.route("/")
def index():
    return """
    Welcome to JetBrains License Server. 
    Please use the following address for activation:
    <pre>http://%s:5000 </pre>
    
    Author: Benny <br>
    Email: <EMAIL> <br>
    GitHub: <a href='https://github.com/BennyThink/pyJetBrains_license_server'>pyJetBrains_license_server</a><br> 
    Website: <a href='https://www.bennythink.com/jbls.html'>土豆不好吃 - Remember me.</a>""" % get_host_ip()


def get_host_ip():
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    s.connect(('*******', 80))
    ip = s.getsockname()[0]
    s.close()
    return ip


def hex_signature(content):
    """
    sign the XML request string with private key.
    :param content: XML request string, the type is unicode(Python3)
    :return: hex value for the signature.
    """
    # load private key or read from file: f.read()
    private_key = serialization.load_pem_private_key(KEY, password=None, backend=default_backend())
    i = content.encode('utf-8')
    signature = private_key.sign(i, padding.PKCS1v15(), hashes.MD5())

    if sys.version > '3':
        return binascii.hexlify(signature).decode('utf-8')
    else:
        return binascii.hexlify(signature)


if __name__ == '__main__':
    app.run(host='0.0.0.0')
