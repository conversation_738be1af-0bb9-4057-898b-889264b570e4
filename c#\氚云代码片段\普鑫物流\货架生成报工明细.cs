
using System;
using System.Collections.Generic;
using System.Text;
using H3;
using H3.DataModel;
using System.Data;

public class D282605swgcbfrfszeetpkvlahrz_ListViewController: H3.SmartForm.ListViewController
{
    public D282605swgcbfrfszeetpkvlahrz_ListViewController(H3.SmartForm.ListViewRequest request): base(request)
    {
    }

    protected override void OnLoad(H3.SmartForm.LoadListViewResponse response)
    {
        base.OnLoad(response);
    }

    protected override void OnSubmit(string actionName, H3.SmartForm.ListViewPostValue postValue, H3.SmartForm.SubmitListViewResponse response)
    {

        if(actionName == "custom_import")
        {
            // json 反序列为当前工艺单
            DPuXinInventory inventory = new DPuXinInventory(this.Engine, this.Request.UserContext.UserId);
            Dictionary < string, List < BizObject >> reportDict = this.CreateReport(inventory);
            if(reportDict.Count == 0) return;


            // 校验数据，验证库存，以及上下级数量
            List < DPuXinValidateError > errors = inventory.Validate(reportDict);
            if(errors.Count > 0)
            {
                response.ReturnData = new Dictionary<string, object>();
                response.ReturnData.Add("Errors", this.Serialize(errors));
                return;
            }
            inventory.WriteInventory();
            inventory.SubmitPlans(reportDict, false);
            inventory.GenerateReport(reportDict);

            try
            {
                DPuXinCalcPrice calcPrice = new DPuXinCalcPrice(this.Engine, this.Request.UserContext.UserId);
                calcPrice.BatchCalcPrice(reportDict);
            }
            catch(Exception ex)
            {
            }

            return;
        }
        if(actionName == "calc_price")
        {
            Dictionary < string, string > queryParams = this.Deserialize<Dictionary<string, string>>(this.Request["ObjectId"]);

            List < BizObject > reports = QueryReports(queryParams);
            if(reports != null && reports.Count > 0)
            {
                DPuXinCalcPrice calcPrice = new DPuXinCalcPrice(this.Engine, this.Request.UserContext.UserId);
                calcPrice.BatchCalcPrice(reports);
            }
            foreach(BizObject obj in reports)
            {
                obj.Update();
            }
            return;
        }

        if(actionName == "report_back")
        {
            string[] objectIds = this.Deserialize<string[]>(this.Request["ObjectIds"]);

            DPuXinInventory inventory = new DPuXinInventory(this.Engine, this.Request.UserContext.UserId);

            Dictionary < string, List < BizObject >> reportDict = inventory.QueryReportDict(objectIds);

            List < DPuXinValidateError > errors = inventory.RollbackValidate(reportDict);
            if(errors.Count > 0)
            {
                response.ReturnData = new Dictionary<string, object>();
                response.ReturnData.Add("Errors", this.Serialize(errors));
                return;
            }
            inventory.WriteInventory();
            inventory.SubmitPlans(reportDict, true);
            inventory.DelReportDetails(objectIds);
            return;
        }

        base.OnSubmit(actionName, postValue, response);
    }

    private List < BizObject > QueryReports(Dictionary < string, string > queryParams)
    {
        // 加载规则
        H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();
        H3.Data.Filter.And andMatcher = new H3.Data.Filter.And();    //构造And匹配器
        foreach(KeyValuePair < string, string > pair in queryParams)
        {
            string key = pair.Key;
            string value = pair.Value;
            if(string.IsNullOrEmpty(value))
            {
                continue;
            }
            if(key == "StartDate")
            {
                andMatcher.Add(new H3.Data.Filter.ItemMatcher("ReportDate", H3.Data.ComparisonOperatorType.NotBelow, Convert.ToDateTime(value)));
                continue;
            }
            if(key == "EndDate")
            {
                andMatcher.Add(new H3.Data.Filter.ItemMatcher("ReportDate", H3.Data.ComparisonOperatorType.NotAbove, Convert.ToDateTime(value)));
                continue;
            }
            andMatcher.Add(new H3.Data.Filter.ItemMatcher(key, H3.Data.ComparisonOperatorType.Equal, value));
        }

        filter.Matcher = andMatcher;
        BizObjectSchema schema = this.Engine.BizObjectManager.GetPublishedSchema("D282605swgcbfrfszeetpkvlahrz");
        BizObject[] items = BizObject.GetList(this.Engine, this.Request.UserContext.UserId,
            schema, GetListScopeType.GlobalAll, filter);
        return new List<BizObject>(items);
    }

    private Dictionary < string, List < BizObject >> CreateReport(DPuXinInventory inventory)
    {
        Dictionary < string, List < BizObject >> reportDict = new Dictionary<string, List<BizObject>>();
        string[] objs = this.Deserialize<string[]>(this.Request["ObjectIds"]);
        foreach(string obj in objs)
        {
            BizObject item = this.ConvertToObj(obj);
            string orderNo = item["OrderNo"] + string.Empty;
            List < BizObject > plans = inventory.GetPlans(orderNo);
            BizObject plan = plans.Find(e => (e["OrderNo"] + string.Empty) == orderNo
                && (e["MaterialName"] + string.Empty) == (item["MaterialName"] + string.Empty));
            if(plan == null)
            {
                continue;
            } // 录入编码信息，做冗余方便校验库存
            item["MaterialNo"] = plan["MaterialNo"];
            item["ProcessLine"] = plan["ProcessLine"];
            item["ParentMaterialNo"] = plan["ParentMaterialNo"];
            if(reportDict.ContainsKey(orderNo))
            {
                List < BizObject > list = reportDict[orderNo];
                // 防止重复录入
                BizObject old = list.Find(e => (e["MaterialNo"] + string.Empty) == (item["MaterialNo"] + string.Empty)
                    && (e["Process"] + string.Empty) == (item["Process"] + string.Empty)
                    && (Convert.ToDateTime(item["ReportDate"]).CompareTo(Convert.ToDateTime(e["ReportDate"])) == 0));
                if(old != null)
                {
                    continue;
                }
                list.Add(item);
            }
            else
            {
                List < BizObject > list = new List<BizObject>();
                list.Add(item);
                reportDict.Add(orderNo, list);
            }
        }
        return reportDict;

    }
    private BizObject ConvertToObj(string str)
    {
        BizObjectSchema schema = this.Engine.BizObjectManager.GetPublishedSchema("D282605swgcbfrfszeetpkvlahrz");
        BizObject item = new BizObject(this.Engine, schema, this.Request.UserContext.UserId);

        Dictionary < string, object > dict = this.Deserialize<Dictionary<string, object>>(str);
        foreach(KeyValuePair < string, object > pair in dict)
        {
            item[pair.Key] = pair.Value;
        }
        return item;
    }
}

public class DPuXinProcessCard
{
    private string[] processItems;

    private List<DPuXinProcessGroup> groups;

    public DPuXinProcessCard(object processFlow)
    {
        string processLine = Convert.ToString(processFlow);
        if(processLine.EndsWith(";"))
        {
            processLine = processLine.Substring(0, processLine.Length - 1);
        }

        this.processItems = processLine.Split(';');
        this.groups = DPuXinProcessGroup.CreateGroups();
        //切分现有工序到组
        foreach(DPuXinProcessGroup group in groups)
        {
            group.PutAll(this.processItems);
        }
    }
    public bool HasProcess(string process)
    {
        for(int i = 0;i < this.processItems.Length; i++)
        {
            if(process == this.processItems[i])
            {
                return true;
            }
        }
        return false;
    }
    public string Last()
    {
        return processItems[processItems.Length - 1];
    }
    public bool IsFirst(string process)
    {
        return process == this.processItems[0];
    }
    public string First()
    {
        return processItems[0];
    }
    public string Before(string process)
    {
        for(int i = 0;i < this.processItems.Length; i++)
        {
            if(processItems[i] == process)
            {
                return i == 0 ? null : this.processItems[i - 1];
            }
        }
        return null;
    }

    public DPuXinProcessGroup Group(string process)
    {
        foreach(DPuXinProcessGroup group in groups)
        {
            if(group.InGroup(process))
            {
                return group;
            }
        }
        return null;
    }

    public List<DPuXinProcessGroup> EnableGroups()
    {
        List < DPuXinProcessGroup > list = new List<DPuXinProcessGroup>();

        foreach(string process in this.processItems)
        {
            if(string.IsNullOrEmpty(process))
            {
                continue;
            }
            //throw new Exception("验证点" + list.Count + "" + this.processItems.Length + "" + process);
            DPuXinProcessGroup group = Group(process);
            if(group == null)
            {
                continue;
            }

            DPuXinProcessGroup old = DPuXinProcessGroup.Find(list, group.GroupName());
            if(old == null)
            {
                list.Add(group);
            }

        }

        return list;
    }

}

public class DPuXinValidateError
{
    public string OrderNo;
    public string MaterialName;
    public string Quantity;
    public string Error;
    public DPuXinValidateError(string orderNo, string materialName, string quantity, string error)
    {
        this.OrderNo = orderNo;
        this.MaterialName = materialName;
        this.Quantity = quantity;
        this.Error = error;
    }
}

public class DPuXinInventory
{
    private IEngine Engine;
    private string UserId;
    private string MaterialSchemaCode = "D282605suten5clu70qwo9gkhtlp"; // 物料
    private string InventorySchemaCode = "D282605Swtn30nwzlozmh2upo2qsnvmp0"; // k库存
    private string PlanSchemaCode = "D282605Sbces9w53iq957t4gc8vulc6y2"; //货架主计划

    private string ProcessSheetSchemaCode = "D282605Sz5g9cj2b7tj1pr4ax1ao7prg4"; // 工艺单

    private string ReportSchemaCode = "D282605swgcbfrfszeetpkvlahrz"; // 报工明细
    private string ReportOrderSchemaCode = "D2826052468b3ca3f39406a80a5ee6055d35ec1"; // 报工单

    private Dictionary<string, List<BizObject>> planDict = new Dictionary<string, List<BizObject>>(); //计划表
private Dictionary < string, BizObject > inventoryDict = new Dictionary<string, BizObject>(); // 库存表
private Dictionary < string, List < BizObject >> processSheetDict = new Dictionary<string, List<BizObject>>(); // 工艺单表

public DPuXinInventory(IEngine Engine, string UserId)
{
    this.Engine = Engine;
    this.UserId = UserId;
}

public void WriteInventory()
{
    foreach(BizObject item in this.inventoryDict.Values)
    {
        item.Update();
    }
}
public void GenerateReport(Dictionary < string, List < BizObject >> reportDict)
{
    foreach(KeyValuePair < string, List < BizObject >> pair in reportDict)
    {
        string orderNo = pair.Key;
        List < BizObject > list = pair.Value;
        // 生成报工单
        BizObjectSchema reportSchema = this.Engine.BizObjectManager.GetPublishedSchema(this.ReportOrderSchemaCode);
        BizObject report = new BizObject(this.Engine, reportSchema, this.UserId);
        report["Team"] = list[0]["Team"];
        report["ReportDate"] = list[0]["ReportDate"];
        report.Status = BizObjectStatus.Effective; //设置状态生效
        string instanceId = System.Guid.NewGuid().ToString(); // 创建流程id
        report.WorkflowInstanceId = instanceId;
        report.Create();

        foreach(BizObject item in list)
        {
            item["WorkReport"] = report["ObjectId"];
            item.Status = BizObjectStatus.Effective; //设置状态生效
            item.Create();
        }
        //发起流程
        H3.Workflow.Instance.WorkflowInstance wfInstance = this.Engine.WorkflowInstanceManager.GetWorkflowInstance(report.WorkflowInstanceId);
        string workItemID = string.Empty;
        string errorMsg = string.Empty;
        H3.Workflow.Template.WorkflowTemplate wfTemp = this.Engine.WorkflowTemplateManager.GetDefaultWorkflow(report.Schema.SchemaCode);
        this.Engine.Interactor.OriginateInstance(this.UserId, report.Schema.SchemaCode,
            wfTemp.WorkflowVersion, report.ObjectId, report.WorkflowInstanceId, H3.Workflow.WorkItem.AccessMethod.Web,
            true, // 是否提交流程操作
            string.Empty, true, out workItemID, out errorMsg);
    }
}
public List < DPuXinValidateError > Validate(Dictionary < string, List < BizObject >> reportDict)
{
    List < DPuXinValidateError > errors = new List<DPuXinValidateError>();
    foreach(KeyValuePair < string, List < BizObject >> pair in reportDict)
    {
        string orderNo = pair.Key;
        List < BizObject > reports = pair.Value;
        List < BizObject > tree = new List<BizObject>(reports);
        List < BizObject > plans = this.GetPlans(orderNo);
        List < BizObject > sheets = this.GetProcessSheet(orderNo);

        DataRowCollection haveApply = this.QueryHaveApply(orderNo);

        while(tree.Count > 0)
        {
            BizObject root = tree[0]; // 取叶子节点， 再根据叶节点工序排序优先入库
            BizObject node = this.SearchLeaf(tree, root);
            if(node == null)
            {
                errors.Add(new DPuXinValidateError(orderNo, root["MaterialName"] + string.Empty, Convert.ToString(root["DeclaredQuantity"]), "工序不存在"));
                tree.Remove(root);
                continue;
            }
            tree.Remove(node);
            string materialNo = node["MaterialNo"] + string.Empty;
            string materialName = node["MaterialName"] + string.Empty;
            int waiting = this.ToInt(node["DeclaredQuantity"]);
            try
            {
                int haveApplyCount = this.FindCountOfHaveApply(haveApply, materialNo);
                BizObject plan = Find(plans, orderNo, materialNo);
                if(plan == null)
                {
                    throw new Exception("主计划表不存在该物料");
                }
                if(waiting + haveApplyCount > this.ToInt(plan["Count"]))
                {
                    throw new Exception("超过可以提交的订单数量：" + this.ToInt(plan["Count"]));
                }
                BizObject sheet = Find(sheets, orderNo, materialNo);
                if(sheet == null)
                {
                    throw new Exception("工艺单不存在该物料");
                }
                string process = node["Process"] + string.Empty;
                DPuXinProcessCard card = new DPuXinProcessCard(node["ProcessLine"] + string.Empty);

                if(!card.HasProcess(process)) continue;
                if(card.IsFirst(process))
                {
                    // 从工艺单中，取对应的子集，计划中是合并数据取不到关系了
                    int count = this.ToInt(sheet["Count"]);
                    List < BizObject > sub = sheets.FindAll(e => (e["ParentSerialNo"] + string.Empty) == (sheet["SerialNo"] + string.Empty));

                    if(sub.Count > 0)
                    {
                        for(int i = 0;i < sub.Count; i++)
                        {
                            BizObject item = sub[i];
                            string processFlow = item["ProcessFlow"] + string.Empty;
                            string[] processItems = processFlow.Split(';');
                            if(processItems.Length == 0)
                            {
                                throw new Exception("当前工艺单不存在工序");
                            }
                            string last = processItems[processItems.Length - 1];
                            BizObject inventory = this.GetProcessInvetory(item["MaterialNo"] + string.Empty, last, false);
                            int stock = 0;
                            string errorMsg = "子物料[{0}_{1}_{2}]库存不足,当前库存：{3}";
                            if(inventory == null)
                            {
                                throw new Exception(String.Format(errorMsg, Convert.ToString(item["OrderNo"]),
                                    Convert.ToString(item["MaterialName"]), last, stock));
                            }
                            stock = this.ToInt(inventory["Quantity"]); // 当前库存
                            int subCount = this.ToInt(item["Count"]);

                            if(stock < (waiting * Math.Round((decimal) subCount / count, 1)))
                            {
                                throw new Exception(String.Format(errorMsg + ",占比：{4}", Convert.ToString(item["OrderNo"]),
                                    Convert.ToString(item["MaterialName"]), last, stock, count + ":" + subCount));
                            }
                        }
                        for(int i = 0;i < sub.Count; i++)
                        {
                            BizObject item = sub[i];
                            string processFlow = item["ProcessFlow"] + string.Empty;
                            string[] processItems = processFlow.Split(';');
                            string last = processItems[processItems.Length - 1];
                            BizObject inventory = this.GetProcessInvetory(item["MaterialNo"] + string.Empty, last, false);

                            int subCount = this.ToInt(item["Count"]);

                            inventory["Quantity"] = this.ToInt(inventory["Quantity"]) - Math.Ceiling(waiting * Math.Round((decimal) subCount / count, 1));
                        }
                    }
                    BizObject cur = this.GetProcessInvetory(materialNo, process, true);
                    cur["Quantity"] = this.ToInt(cur["Quantity"]) + waiting; // 当前物料加库存
                }
                else
                {
                    string beforeProcess = card.Before(process);
                    BizObject before = this.GetProcessInvetory(materialNo, beforeProcess, false);
                    if(before != null)
                    {
                        int beforeStock = this.ToInt(before["Quantity"]);
                        BizObject cur = this.GetProcessInvetory(materialNo, process, true);
                        int diff = beforeStock - waiting;
                        if(diff >= 0)
                        {
                            before["Quantity"] = beforeStock - waiting; // 上个物料减库存
                            cur["Quantity"] = this.ToInt(cur["Quantity"]) + waiting; // 当前物料加库存
                        }
                        else
                        {
                            throw new Exception(String.Format("超过上个工序[{0}]物料库存, 库存：{1}", beforeProcess, beforeStock));
                        }
                    }
                    else
                    {
                        throw new Exception(String.Format("超过上个工序[{0}]物料库存, 库存：0", beforeProcess));
                    }
                }
            }
            catch(Exception ex)
            {
                errors.Add(new DPuXinValidateError(orderNo, materialName, waiting + "", ex.Message));
            }

        }
    }
    // 查询库存3
    // 查询计划，获取上下级关联关系，赋值当前报工明细
    return errors;
}

public List < DPuXinValidateError > RollbackValidate(Dictionary < string, List < BizObject >> reportDict)
{
    List < DPuXinValidateError > errors = new List<DPuXinValidateError>();
    foreach(KeyValuePair < string, List < BizObject >> pair in reportDict)
    {
        string orderNo = pair.Key;
        List < BizObject > reports = pair.Value;
        List < BizObject > tree = new List<BizObject>(reports);
        List < BizObject > plans = this.GetPlans(orderNo);
        List < BizObject > sheets = this.GetProcessSheet(orderNo);

        while(tree.Count > 0)
        {
            BizObject node = this.SearchRoot(tree, tree[0]);
            tree.Remove(node);

            string materialNo = node["MaterialNo"] + string.Empty;
            string materialName = node["MaterialName"] + string.Empty;
            int waiting = this.ToInt(node["DeclaredQuantity"]);
            try
            {

                BizObject sheet = Find(sheets, orderNo, materialNo);
                if(sheet == null)
                {
                    throw new Exception("工艺单不存在该物料");
                }
                string process = node["Process"] + string.Empty;
                DPuXinProcessCard card = new DPuXinProcessCard(node["ProcessLine"] + string.Empty);

                if(!card.HasProcess(process)) continue;
                if(card.IsFirst(process))
                {

                    BizObject cur = this.GetProcessInvetory(materialNo, process, false);
                    if(cur == null)
                    {
                        throw new Exception(String.Format("工序[{0}]物料库存不存在", process));
                    }
                    int curStock = this.ToInt(cur["Quantity"]);
                    if(curStock - waiting < 0)
                    {
                        throw new Exception(String.Format("工序[{0}]物料库存不足不能回滚, 库存：{1}", process, curStock));
                    }
                    cur["Quantity"] = curStock - waiting; // 当前物料加库存
                    int count = this.ToInt(sheet["Count"]);
                    List < BizObject > sub = sheets.FindAll(e => (e["ParentSerialNo"] + string.Empty) == (sheet["SerialNo"] + string.Empty));
                    if(sub.Count > 0)
                    {
                        for(int i = 0;i < sub.Count; i++)
                        {
                            BizObject item = sub[i];
                            string processFlow = item["ProcessFlow"] + string.Empty;
                            string[] processItems = processFlow.Split(';');
                            string last = processItems[processItems.Length - 1];
                            BizObject inventory = this.GetProcessInvetory(item["MaterialNo"] + string.Empty, last, true);
                            int subCount = this.ToInt(item["Count"]);
                            inventory["Quantity"] = this.ToInt(inventory["Quantity"]) + Math.Ceiling(waiting * Math.Round((decimal) subCount / count, 1));
                        }
                    }

                }
                else
                {
                    string beforeProcess = card.Before(process);
                    BizObject before = this.GetProcessInvetory(materialNo, beforeProcess, true);

                    BizObject cur = this.GetProcessInvetory(materialNo, process, false);
                    if(cur == null)
                    {
                        throw new Exception(String.Format("工序[{0}]物料库存不存在", process));
                    }
                    int curStock = this.ToInt(cur["Quantity"]);
                    int diff = curStock - waiting;
                    if(diff >= 0)
                    {
                        before["Quantity"] = this.ToInt(before["Quantity"]) + waiting; // 上个物料减库存
                        cur["Quantity"] = curStock - waiting; // 当前物料加库存
                    }
                    else
                    {
                        throw new Exception(String.Format("工序[{0}]物料库存不足不能回滚, 库存：{1}", process, curStock));
                    }
                }
            }
            catch(Exception ex)
            {
                errors.Add(new DPuXinValidateError(orderNo, materialName, waiting + "", ex.Message));
            }
        }

    }
    return errors;
}
private BizObject SearchRoot(List < BizObject > tree, BizObject node)
{
    string orderNo = node["OrderNo"] + string.Empty;
    string parentMaterialNo = node["ParentMaterialNo"] + string.Empty;

    BizObject current = null;
    if(!string.IsNullOrEmpty(parentMaterialNo))
    {
        current = tree.Find(e => (e["MaterialNo"] + string.Empty) == parentMaterialNo);
    }
    if(current == null)
    {
        string process = node["Process"] + string.Empty;
        string[] processItems = (node["ProcessLine"] + string.Empty).Substring(1).Split('/');
        Array.Reverse(processItems);
        foreach(string processItem in processItems)
        {
            if(processItem == process)
            {
                return node;
            }
            else
            {
                BizObject before = tree.Find(e => (e["Process"] + string.Empty) == processItem
                    && (e["MaterialNo"] + string.Empty) == (node["MaterialNo"] + string.Empty));
                if(before == null)
                {
                    continue;
                }
                return before;
            }
        }
    }
    return this.SearchRoot(tree, current);
}

private BizObject GetProcessInvetory(string materialNo, string process, bool isNew)
{
    BizObjectSchema schema = this.Engine.BizObjectManager.GetPublishedSchema(this.InventorySchemaCode);
    BizObject inventory = this.QueryInventory(materialNo); // 取库存
    BizObject[] balance = (BizObject[]) inventory["D282605F76c8c2580d3244c4b8fe0774cde6f917"];
    List < BizObject > list = new List<BizObject>();
    if(balance != null && balance.Length > 0)
    {
        list = new List<BizObject>(balance);
    }

    BizObject cur = list.Find(e => (e["Process"] + string.Empty) == process);
    if(cur == null && isNew)
    {
        cur = new BizObject(this.Engine,
            schema.GetChildSchema("D282605F76c8c2580d3244c4b8fe0774cde6f917"), this.UserId); // 半成品库存
        cur["Process"] = process;
        list.Add(cur);
        inventory["D282605F76c8c2580d3244c4b8fe0774cde6f917"] = list.ToArray(); // 重新设置子集
    }
    return cur;
}

private BizObject SearchLeaf(List < BizObject > tree, BizObject root)
{
    string orderNo = root["OrderNo"] + string.Empty;
    string materialNo = root["MaterialNo"] + string.Empty;
    List < BizObject > sheets = this.GetProcessSheet(orderNo);
    List < BizObject > child = sheets.FindAll(e => (e["ParentMaterialNo"] + string.Empty) == materialNo);
    BizObject node = null;
    foreach(BizObject childItem in child)
    {
        node = tree.Find(e => (e["MaterialNo"] + string.Empty) == (childItem["MaterialNo"] + string.Empty));
        if(node != null)
        {
            break;
        }
    }
    if(node == null)
    {
        string process = root["Process"] + string.Empty;
        string[] processItems = (root["ProcessLine"] + string.Empty).Substring(1).Split('/');
        foreach(string processItem in processItems)
        {
            if(processItem == process)
            {
                return root;
            }
            else
            {
                BizObject before = tree.Find(e => (e["Process"] + string.Empty) == processItem && (e["MaterialNo"] + string.Empty) == materialNo);
                if(before == null)
                {
                    continue;
                }
                return before;
            }
        }
        return null;
    };

    return this.SearchLeaf(tree, node);
}

private BizObject QueryInventory(string materialNo)
{
    if(inventoryDict.ContainsKey(materialNo))
    {
        return inventoryDict[materialNo];
    }
    H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();  //根据物资编码查询库存
    filter.Matcher = new H3.Data.Filter.ItemMatcher("MaterialNo", H3.Data.ComparisonOperatorType.Equal, materialNo);
    BizObjectSchema schema = this.Engine.BizObjectManager.GetPublishedSchema(this.InventorySchemaCode);
    BizObject[] stocks = BizObject.GetList(this.Engine, this.UserId,
        schema, GetListScopeType.GlobalAll, filter);
    if(stocks == null || stocks.Length == 0)
    {
        // 库存为空，新生成一条库存数据
        BizObject stock = new BizObject(this.Engine, schema, this.UserId);
        DataRow material = this.GetMaterialId(materialNo);
        stock["MaterialInfo"] = material["ObjectId"];
        stock["MaterialNo"] = material["MaterialNo"];
        stock["MaterialName"] = material["MaterialName"];
        stock["MaterialModel"] = material["MaterialModel"];
        stock.Status = BizObjectStatus.Effective; //设置状态生效
        stock.Create();
        inventoryDict.Add(materialNo, stock);
        return stock;
    }
    inventoryDict.Add(materialNo, stocks[0]);
    return stocks[0];
}

public List < BizObject > GetProcessSheet(string orderNo)
{
    if(this.processSheetDict.ContainsKey(orderNo))
    {
        return this.processSheetDict[orderNo];
    }
    H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();  //根据物资编码查询库存
    H3.Data.Filter.And andMatcher = new H3.Data.Filter.And();    //构造And匹配器
    andMatcher.Add(new H3.Data.Filter.ItemMatcher("OrderNo", H3.Data.ComparisonOperatorType.Equal, orderNo));
    filter.Matcher = andMatcher;
    BizObjectSchema schema = this.Engine.BizObjectManager.GetPublishedSchema(this.ProcessSheetSchemaCode);
    BizObject[] items = BizObject.GetList(this.Engine, this.UserId,
        schema, GetListScopeType.GlobalAll, filter);
    List < BizObject > sheet = new List<BizObject>(items);
    this.processSheetDict.Add(orderNo, sheet);
    return sheet;
}
public List < BizObject > GetPlans(string orderNo)
{
    if(this.planDict.ContainsKey(orderNo))
    {
        return this.planDict[orderNo];
    }
    H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();  //根据物资编码查询库存
    H3.Data.Filter.And andMatcher = new H3.Data.Filter.And();    //构造And匹配器
    andMatcher.Add(new H3.Data.Filter.ItemMatcher("OrderNo", H3.Data.ComparisonOperatorType.Equal, orderNo));
    filter.Matcher = andMatcher;
    BizObjectSchema schema = this.Engine.BizObjectManager.GetPublishedSchema(this.PlanSchemaCode);
    BizObject[] items = BizObject.GetList(this.Engine, this.UserId,
        schema, GetListScopeType.GlobalAll, filter);
    List < BizObject > plans = new List<BizObject>(items);
    this.planDict.Add(orderNo, plans);
    return plans;
}

public void SubmitPlans(Dictionary < string, List < BizObject >> reportDict, bool reverse)
{
    foreach(KeyValuePair < string, List < BizObject >> pair in reportDict)
    {
        string orderNo = pair.Key;
        List < BizObject > list = pair.Value;
        List < BizObject > plans = this.GetPlans(orderNo);
        BizObjectSchema schema = this.Engine.BizObjectManager.GetPublishedSchema(this.PlanSchemaCode);
        List < BizObject > items = new List<BizObject>(list);
        while(items.Count > 0)
        {
            BizObject item = items[0];
            items.Remove(item); // 取出元素
            BizObject plan = plans.Find(e => (e["OrderNo"] + string.Empty) == orderNo && (e["MaterialNo"] + string.Empty) == (item["MaterialNo"] + string.Empty));
            if(plan == null)
            {
                continue;
            }
            string process = item["Process"] + string.Empty;
            DPuXinProcessCard card = new DPuXinProcessCard(plan["ProcessLine"] + string.Empty);
            DPuXinProcessGroup group = card.Group(process);
            if(group == null || !group.IsValid())  // 不在组中的移除
            {
                continue;
            }
            string lastProcess = group.Last(); //最后工序
            BizObject last = items.Find(e => (Convert.ToString(e["MaterialNo"]) == Convert.ToString(item["MaterialNo"]))
                && (Convert.ToString(e["Process"]) == lastProcess));
            foreach(string g in group.Process())
            {
                BizObject exist = items.Find(e => (Convert.ToString(e["MaterialNo"]) == Convert.ToString(item["MaterialNo"]))
                    && (Convert.ToString(e["Process"]) == g));
                if(exist != null) items.Remove(exist);
            }
            if(last == null)
            {
                if(Convert.ToString(item["Process"]) == lastProcess)
                {
                    last = item;
                }
                else
                {
                    continue;
                }
            }

            DateTime date = Convert.ToDateTime(plan["DeliveryDate"]);
            BizObject[] progress = (BizObject[]) plan["D282605F80a2dc5e16c74b21813e7f1b5ad3615b"];
            List < BizObject > progressList = new List<BizObject>();
            if(progress != null && progress.Length > 0) progressList = new List<BizObject>(progress);
            string groupName = group.GroupName();
            BizObject obj = progressList.Find(e => (e["ProcessName"] + string.Empty) == groupName);

            if(obj == null)
            {
                obj = new BizObject(this.Engine,
                    schema.GetChildSchema("D282605F80a2dc5e16c74b21813e7f1b5ad3615b"), this.UserId); // 半成品库存
                obj["Finished"] = this.ToInt(last["DeclaredQuantity"]) * (reverse ? -1 : 1); //完成数
                obj["ProcessName"] = groupName;
                int diff = group.DayDiff();
                obj["PlanDate"] = date.AddDays(diff);
                progressList.Add(obj);
            }
            else
            {
                obj["Finished"] = this.ToInt(obj["Finished"]) + this.ToInt(last["DeclaredQuantity"]) * (reverse ? -1 : 1);
            }
            int finished = this.ToInt(obj["Finished"]);
            int total = this.ToInt(plan["Count"]);
            obj["Remain"] = total - finished;
            obj["Progress"] = Math.Round((decimal) finished / total, 2);
            if(finished == total)
            {
                obj["ActualDate"] = System.DateTime.Today;
                plan["ProcessStatus"] = "生产结案";
                if(!string.IsNullOrEmpty(obj["ActualDate"] + string.Empty)
                    && !string.IsNullOrEmpty(plan["DeliveryDate"] + string.Empty))
                {
                    DateTime actual = Convert.ToDateTime(obj["ActualDate"]);
                    DateTime delivery = Convert.ToDateTime(plan["DeliveryDate"]);
                    plan["QuasiDelivery"] = actual.CompareTo(delivery) <= 0 ? "按期完成" : "超期完成";
                }
            }
            else
            {
                obj["ActualDate"] = null;
                plan["ProcessStatus"] = "在制中";
                DateTime delivery = Convert.ToDateTime(plan["DeliveryDate"]);
                if(Convert.ToString(plan["QuasiDelivery"]) != "子件物料")
                {
                    plan["QuasiDelivery"] = delivery.CompareTo(System.DateTime.Today) < 0 ? "超期未完" : "交期未到";
                }
            }

            plan["D282605F80a2dc5e16c74b21813e7f1b5ad3615b"] = progressList.ToArray();
            plan.Update();
        }
    }

}

private DataRow GetMaterialId(string materialNo)
{
    string sql = String.Format("select ObjectId, MaterialNo, MaterialName, MaterialModel from i_{0} where MaterialNo = '{1}'", this.MaterialSchemaCode, materialNo);
    DataTable table = this.Engine.Query.QueryTable(sql, null);

    if(table != null && table.Rows.Count > 0)
    {
        return table.Rows[0];
    }
    return null;
}

public Dictionary < string, List < BizObject >> QueryReportDict(string[] objectIds)
{
    H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();  //根据物资编码查询库存
    H3.Data.Filter.And andMatcher = new H3.Data.Filter.And();    //构造And匹配器
    andMatcher.Add(new H3.Data.Filter.ItemMatcher("WorkReport", H3.Data.ComparisonOperatorType.In, objectIds));
    filter.Matcher = andMatcher;

    BizObjectSchema schema = this.Engine.BizObjectManager.GetPublishedSchema(this.ReportSchemaCode);
    BizObject[] items = BizObject.GetList(this.Engine, this.UserId,
        schema, GetListScopeType.GlobalAll, filter);
    Dictionary < string, List < BizObject >> report = new Dictionary<string, List<BizObject>>();
    if(items != null && items.Length > 0)
    {
        foreach(BizObject item in items)
        {
            string orderNo = item["OrderNo"] + string.Empty;
            if(report.ContainsKey(orderNo))
            {
                List < BizObject > list = report[orderNo];
                list.Add(item);
            }
            else
            {
                List < BizObject > list = new List<BizObject>();
                list.Add(item);
                report.Add(orderNo, list);
            }
        }
    }
    return report;
}
public BizObject[] QueryReportDetail(string reportId)
{
    H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();  //根据物资编码查询库存
    H3.Data.Filter.And andMatcher = new H3.Data.Filter.And();    //构造And匹配器
    andMatcher.Add(new H3.Data.Filter.ItemMatcher("WorkReport", H3.Data.ComparisonOperatorType.Equal, reportId));
    filter.Matcher = andMatcher;

    BizObjectSchema schema = this.Engine.BizObjectManager.GetPublishedSchema(this.ReportSchemaCode);
    BizObject[] items = BizObject.GetList(this.Engine, this.UserId,
        schema, GetListScopeType.GlobalAll, filter);

    return items;
}

public void DelReportDetails(string[] objectIds)
{
    if(objectIds == null || objectIds.Length == 0)
    {
        return;
    }
    string query = "";
    foreach(string objectId in objectIds)
    {
        query = query + string.Format("'{0}',", objectId);
    }
    query = query.Substring(0, query.Length - 1);
    string sql = string.Format("delete from i_{0} where WorkReport in ({1});", this.ReportSchemaCode, query);
    sql += string.Format("delete from i_{0} where ObjectId in ({1});", this.ReportOrderSchemaCode, query);
    this.Engine.EngineConfig.CreateCommand().ExecuteScalar(sql);
}
// 统计报已报工各工序结存
private DataRowCollection QueryHaveApply(string orderNo)
{
    string sql = String.Format( @"select p.OrderNo OrderNo, p.MaterialNo MaterialNo, sum(p.DeclaredQuantity) DeclaredQuantity from i_{0} p inner join i_{1} o on p.WorkReport = o.ObjectId
    inner join H_WorkflowInstance w on o.ObjectId = w.BizObjectId
    where w.State = '4' and p.OrderNo = '{2}' group by p.OrderNo, p.MaterialNo", this.ReportSchemaCode, this.ReportOrderSchemaCode, orderNo);

    DataTable dt = this.Engine.Query.QueryTable(sql, null);
    if(dt == null || dt.Rows == null || dt.Rows.Count == 0)
    {
        return null;
    }

    return dt.Rows;
}

private int FindCountOfHaveApply(DataRowCollection rows, string materialNo)
{
    if(rows == null)
    {
        return 0;
    }
    foreach(DataRow row in rows)
    {
        if((row["MaterialNo"] + string.Empty) == materialNo)
        {
            return this.ToInt(row["DeclaredQuantity"]);
        }
    }
    return 0;
}
private int ToInt(object obj)
{
    return Convert.ToInt32(String.IsNullOrEmpty(obj + "") ? "0" + obj : obj);
}
private BizObject Find(List < BizObject > list, string orderNo, string materialNo)
{
    foreach(BizObject obj in list)
    {

        if(Convert.ToString(obj["OrderNo"]) == orderNo && Convert.ToString(obj["MaterialNo"]) == materialNo)
        {
            return obj;
        }
    }
    return null;
}
}

public class DPuXinBasePrice
{
    protected IEngine Engine;
    protected string UserId;

    protected string SchemaCode;

    protected List<BizObject> Rules;
    public DPuXinBasePrice(IEngine engine, string userId)
    {
        this.Engine = engine;
        this.UserId = userId;
    }

    public List<BizObject> LoadRule()
    {
        if(this.Rules != null) return this.Rules;
        this.Rules = this.Query();
        return this.Rules;
    }

    public virtual List < BizObject > Query()
    {
        return null;
    }


    public virtual decimal Match(BizObject item)
    {
        return 0;
    }

    public virtual bool Apply(string process)
    {
        return false;
    }

    public virtual string CalcType(BizObject item)
    {
        return "Count";  // Count: "申报数量"， "Weight":"申报重量"
    }

    protected bool IsBlank(string str)
    {
        return String.IsNullOrEmpty(str);
    }

    protected string Field(BizObject item, string key)
    {
        try
        {
            return Convert.ToString(item[key]);
        }
        catch(Exception ex)
        {
            return "";
        }
    }

    protected bool KeyWordMatch(BizObject rule, string keyword)
    {
        if(String.IsNullOrEmpty(keyword)) return false;
        string masterKeyWord = Field(rule, "MasterKeyWord"); // Convert.ToString(rule["MasterKeyWord"]);
        string orKeyWord = Field(rule, "OrKeyWord");// Convert.ToString(rule["OrKeyWord"]);
        string andKeyWord = Field(rule, "AndKeyWord"); // Convert.ToString(rule["AndKeyWord"]);
        string notKeyWord = Field(rule, "NotKeyWord"); // Convert.ToString(rule["NotKeyWord"]);

        if(!IsBlank(masterKeyWord) && IsBlank(orKeyWord) && IsBlank(andKeyWord) && IsBlank(notKeyWord)) // 主
        {
            if(RuleMatch(keyword, masterKeyWord))
            {
                return true;
            }
        }
        if(!IsBlank(masterKeyWord) && !IsBlank(orKeyWord) && IsBlank(andKeyWord) && IsBlank(notKeyWord)) // 主或
        {
            if(RuleMatch(keyword, masterKeyWord) || keyword.Contains(orKeyWord))
            {
                return true;
            }
        }
        if(!IsBlank(masterKeyWord) && !IsBlank(orKeyWord) && !IsBlank(andKeyWord) && IsBlank(notKeyWord)) // 主或且
        {
            if((RuleMatch(keyword, masterKeyWord) || keyword.Contains(orKeyWord)) && keyword.Contains(andKeyWord))
            {
                return true;
            }
        }
        if(!IsBlank(masterKeyWord) && !IsBlank(orKeyWord) && !IsBlank(andKeyWord) && !IsBlank(notKeyWord)) // 主或且非
        {
            if((RuleMatch(keyword, masterKeyWord) || keyword.Contains(orKeyWord)) && keyword.Contains(andKeyWord) && !keyword.Contains(notKeyWord))
            {
                return true;
            }
        }

        if(!IsBlank(masterKeyWord) && IsBlank(orKeyWord) && !IsBlank(andKeyWord) && IsBlank(notKeyWord)) // 主且
        {
            if(RuleMatch(keyword, masterKeyWord) && keyword.Contains(andKeyWord))
            {
                return true;
            }
        }
        if(!IsBlank(masterKeyWord) && IsBlank(orKeyWord) && !IsBlank(andKeyWord) && !IsBlank(notKeyWord)) // 主且非
        {
            if(RuleMatch(keyword, masterKeyWord) && keyword.Contains(andKeyWord) && !keyword.Contains(notKeyWord))
            {
                return true;
            }
        }
        if(!IsBlank(masterKeyWord) && IsBlank(orKeyWord) && IsBlank(andKeyWord) && !IsBlank(notKeyWord)) // 主非
        {
            if(RuleMatch(keyword, masterKeyWord) && !keyword.Contains(notKeyWord))
            {
                return true;
            }
        }
        if(!IsBlank(masterKeyWord) && !IsBlank(orKeyWord) && IsBlank(andKeyWord) && !IsBlank(notKeyWord)) // 主或非
        {
            if((RuleMatch(keyword, masterKeyWord) || keyword.Contains(orKeyWord)) && !keyword.Contains(notKeyWord))
            {
                return true;
            }
        }
        return false;
    }

    private bool RuleMatch(string content, string pattern)
    {
        string[] items = pattern.Split('|');
        foreach(string item in items)
        {
            if(content.Contains(item) && !string.IsNullOrEmpty(item))
            {
                return true;
            }
        }
        return false;
    }

}

public class DPuXinCalcPrice
{
    private IEngine Engine;
    private string UserId;

    private List<DPuXinBasePrice> rules = new List<DPuXinBasePrice>();
    public DPuXinCalcPrice(IEngine engine, string userId)
    {
        this.Engine = engine;
        this.UserId = userId;

        this.rules.Add(new DPuXinCutOffPrice(engine, userId)); //切断
        this.rules.Add(new DPuXinShearPrice(engine, userId)); //剪板
        this.rules.Add(new DPuXinBendingPrice(engine, userId)); //折弯
        this.rules.Add(new DPuXinStampingPrice(engine, userId)); //冲压
        this.rules.Add(new DPuXinLaserPrice(engine, userId)); //激光
        this.rules.Add(new DPuXinWeldingPrice(engine, userId)); //焊接
        this.rules.Add(new DPuXinBeltPrice(engine, userId)); // 带冲
        this.rules.Add(new DPuXinRollPrice(engine, userId)); //辊轧
        this.rules.Add(new DPuXinSprayPrice(engine, userId)); //喷塑
        this.rules.Add(new DPuXinPackagePrice(engine, userId)); //装配&打包
        this.rules.Add(new DPuXinDrillHolePrice(engine, userId)); //钻孔
        this.rules.Add(new DPuXinDrawBendPrice(engine, userId)); //扳弯
        this.rules.Add(new DPuXinGrindingPrice(engine, userId)); //打磨
        this.rules.Add(new DPuXinSteelPlatformPrice(engine, userId)); // 钢平台
    }

    public void BatchCalcPrice(Dictionary < string, List < BizObject >> reportDict)
    {
        foreach(KeyValuePair < string, List < BizObject >> pair in reportDict)
        {
            this.BatchCalcPrice(pair.Value);
        }
    }

    public void BatchCalcPrice(List < BizObject > reports)
    {
        foreach(BizObject report in reports)
        {
            this.CalcPrice(report);
        }
    }
    public void CalcPrice(BizObject report)
    {
        string process = Convert.ToString(report["Process"]);
        int declaredQuantity = Convert.ToInt32(report["DeclaredQuantity"]);
        decimal currentSumWeight = Convert.ToDecimal(report["CurrentSumWeight"]);
        foreach(DPuXinBasePrice rule in this.rules)
        {
            if(rule.Apply(process))
            {
                try
                {
                    decimal price = rule.Match(report);
                    if(price > 0)
                    {
                        report["Price"] = price;
                        switch(rule.CalcType(report))
                        {
                            case "Count":
                                report["SumPrice"] = price * declaredQuantity;
                                break;
                            case "Weight":
                                report["SumPrice"] = price * currentSumWeight;
                                break;
                            default:
                                break;
                        }

                    }
                }
                catch(Exception ex)
                {
                }
            }
        }
    }
}