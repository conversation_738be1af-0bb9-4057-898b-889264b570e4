import req  from '../util/request.js'

export default class Juhe_News {
  constructor() {
    this.key = "e21bc564213dfba4aab746d90583afc2";
  }

  async whole_news(type = 'top') {
    let _news = await this.news(type);
    return this.text(_news['data']);
  }

  async news(type = 'top') {
    let url = `http://v.juhe.cn/toutiao/index?type=${type}&key=${this.key}&page_size=10`;
    let resp = await req.get(url)
    return resp['result']
  }


  text(items) {
    let cards = []
    if (items) {
        for (let item of items) {
          let card = {}
          card['title'] = item['title']
          card['messageURL'] = item['url']
          card['picURL'] = item['thumbnail_pic_s']
          cards.push(card)
        }
    }
    return cards;
  }
}
