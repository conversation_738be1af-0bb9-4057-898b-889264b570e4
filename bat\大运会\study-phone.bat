cd C:/Users/<USER>/IdeaProjects/study-phone

git checkout master
git pull

@rem npm package, build mode app.
call npm install
call npm run build -- --mode app

@rem win rar zip,-r is all sub dir, a is compress
@rem winrar a -r  study_app.zip ./dist

7z a study_app.zip ./dist/*

@rem scp zip to server
scp ./study_app.zip root@**************:/usr/share/nginx/html

echo "ssh nginx dir to unzip study, and rm zip"
ssh root@************** "cd /usr/share/nginx/html;rm -rf ./study_app;unzip -d study_app study_app.zip;rm -rf ./study_app.zip"

echo "del local study zip"
del /f /q "./study_*.zip"

pause