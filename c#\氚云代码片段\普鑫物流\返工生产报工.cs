
using System;
using System.Collections.Generic;
using System.Text;
using H3;
using H3.DataModel;
using System.Data;


public class D282605Spr8bymjy1zp6rol2sumj00cw4 : H3.SmartForm.SmartFormController
{
    public D282605Spr8bymjy1zp6rol2sumj00cw4(H3.SmartForm.SmartFormRequest request) : base(request)
    {
    }

    protected override void OnLoad(H3.SmartForm.LoadSmartFormResponse response)
    {
        base.OnLoad(response);
    }

    protected override void OnSubmit(string actionName, H3.SmartForm.SmartFormPostValue postValue, H3.SmartForm.SubmitSmartFormResponse response)
    {
        base.OnSubmit(actionName, postValue, response);
    }
}