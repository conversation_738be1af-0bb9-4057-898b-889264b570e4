import req  from '../util/request.js'
import Notify from '../util/notify.js'
const notify = new Notify();

let url = 'http://j01.tbcache.us'

let accounts = [
  {email: '<EMAIL>', passwd: 'jRAO486rv#'},
  {email: '<EMAIL>', passwd: '&3V2EU8pev'},
]


;(async () => {
  for (let account of accounts) {
    await sign(account)
  }
})();


async function sign(account) {

  let resp = await req.post(`${url}/signin?c=` + Math.random(), {
    json: account
  }, {_auto:false})
  let cookie = ''
  if (resp && resp.statusCode === 200) {
    let body = JSON.parse(resp.body)
    if(body.code === 200) {
      let cookies = resp.headers["set-cookie"];
      cookie = cookies.join(";")
    }
  } else {
    console.log(resp);
    return
  }
  if(!cookie) {
    console.log('cookie 获取失败')
    return
  }
  resp = await req.post(`${url}/user/checkin?c=` + Math.random(), {
    headers: {
     cookie: cookie
    }
  })
  console.log(resp)

  //notify.sendText(`几鸡签到: ${account.email}\n${resp}`)
}


