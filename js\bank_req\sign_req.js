
let crypto = require("crypto");
module.exports = function encrypt(param, _key, ignore) {
  let md5 = crypto.createHash("md5");

  let keys = Object.keys(param);

  let params = "";
  keys.forEach((key) => {
    if (key === "sign") return;
    if (ignore && ignore.includes(key)) return;
    params += key + "=" + param[key] + "&";
  });
  params = params.substr(0, params.length - 1) + _key
  console.log('签名原数据' + params)
  let result = md5.update(params).digest("hex");
  console.log('签名结果' + result)
  param['sign'] =  result.toUpperCase()
  return param;
}

function randomStr(length) {
  var result = "";
  var characters =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  var charactersLength = characters.length;
  for (var i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}
