
using System;
using System.Collections.Generic;
using System.Text;
using H3;

public class D149913e2a3fc9936094a4d9fb7c643558f78c3 : H3.SmartForm.SmartFormController
{
    public D149913e2a3fc9936094a4d9fb7c643558f78c3(H3.SmartForm.SmartFormRequest request) : base(request)
    {
    }

    protected override void OnLoad(H3.SmartForm.LoadSmartFormResponse response)
    {
        base.OnLoad(response);
    }

    protected override void OnSubmit(string actionName, H3.SmartForm.SmartFormPostValue postValue, H3.SmartForm.SubmitSmartFormResponse response)
    {
        base.OnSubmit(actionName, postValue, response);
    }
    protected override void OnWorkflowInstanceStateChanged(H3.Workflow.Instance.WorkflowInstanceState oldState, H3.Workflow.Instance.WorkflowInstanceState newState)
    {
        //流程审批结束,业务规则执行。还有个审批流程结束（finished状态下直接删除），就直接删除也会触发业务规则执行。在submit里面处理
        if (oldState == H3.Workflow.Instance.WorkflowInstanceState.Running && newState == H3.Workflow.Instance.WorkflowInstanceState.Finished)
        {
            H3.DataModel.BizObject plan = this.Request.BizObject;
            H3.DataModel.BizObject[] materialDetails = (H3.DataModel.BizObject[]) this.Request.BizObject["D149913aa630cf1629249aa8263f6959cf8336f"];

            if(materialDetails != null && materialDetails.Length > 0)
            {
                foreach(H3.DataModel.BizObject materialDetail in materialDetails)
                {

                    string productId = materialDetail["F0000005"] + string.Empty;
                    //取库存数量
                    string sqlpo = "select F0000006,F0000014 from i_D149913b779923207a14917a7e6b909ddcf3863 where F0000001 = '" + productId + "'";
                    System.Data.DataTable stockTable = this.Request.Engine.Query.QueryTable(sqlpo, null);
                    int stockNum = 0;
                    int warningNum = 0;
                    if(stockTable != null && stockTable.Rows.Count > 0)
                    {
                        stockNum = Convert.ToInt32(String.IsNullOrEmpty(stockTable.Rows[0]["F0000006"] + "") ? "0" + stockTable.Rows[0]["F0000006"] : stockTable.Rows[0]["F0000006"]);
                        warningNum = Convert.ToInt32(String.IsNullOrEmpty(stockTable.Rows[0]["F0000014"] + "") ? "0" + stockTable.Rows[0]["F0000014"] : stockTable.Rows[0]["F0000014"]);
                    }

                    int useNum = Convert.ToInt32(materialDetail["F0000006"]);
                    int avaiableNum = stockNum - warningNum;
                    //库存可用量 与待使用量比较
                    if(useNum > avaiableNum)
                    {
                        int purchaseNum = useNum - avaiableNum;
                        if(avaiableNum < 0)
                        { // 库存小于0 ， 则采购数为当前待使用数
                            purchaseNum = useNum;
                        }
                        H3.DataModel.BizObjectSchema purchasePlanSchema = this.Engine.BizObjectManager.GetPublishedSchema("D149913Sh59f0tsbpx9bnw37i9p1y5hk3");
                        H3.DataModel.BizObject purchasePlan = new H3.DataModel.BizObject(this.Engine, purchasePlanSchema, this.Request.UserContext.UserId);
                        purchasePlan["F0000016"] = "生产/材料计划"; //来源
                        purchasePlan["F0000017"] = plan["SeqNo"]; //来源单号
                        purchasePlan["F0000010"] = materialDetail["F0000024"]; //备注
                        purchasePlan["F0000018"] = productId; //产品信息
                        purchasePlan["F0000026"] = materialDetail["F0000019"];//品牌
                        purchasePlan["F0000021"] = materialDetail["F0000012"];//规格型号
                        purchasePlan["F0000022"] = materialDetail["F0000007"];//单位
                        purchasePlan["F0000023"] = purchaseNum; //需求数量
                        purchasePlan["F0000025"] = purchaseNum;//剩余采购数量
                        //purchasePlan["F0000028"] = materialDetail["F0000101"];//产品小类
                        purchasePlan["F0000029"] = plan["F0000017"];// 交货日期

                        purchasePlan["F0000011"] = plan["F0000004"]; //销售订单

                        purchasePlan.Status = H3.DataModel.BizObjectStatus.Effective; //设置状态生效
                        purchasePlan.Create(); //创建采购需求计划
                    }
                }
            }
        }
        base.OnWorkflowInstanceStateChanged(oldState, newState);
    }
}