
using System;
using System.Collections.Generic;
using System.Text;
using H3;
using H3.DataModel;
using System.Data;



using System;
using System.Collections.Generic;
using System.Text;
using H3;

public class D282605d63617ddbc1848fb8c93f4a3fa4e59c3 : H3.SmartForm.SmartFormController
{
    public D282605d63617ddbc1848fb8c93f4a3fa4e59c3(H3.SmartForm.SmartFormRequest request) : base(request)
    {
    }

    protected override void OnLoad(H3.SmartForm.LoadSmartFormResponse response)
    {
        base.OnLoad(response);
    }

    protected override void OnSubmit(string actionName, H3.SmartForm.SmartFormPostValue postValue, H3.SmartForm.SubmitSmartFormResponse response)
    {

        DPuXinNewProcessSheet sheet = new DPuXinNewProcessSheet(this.Engine, this.Request.UserContext.UserId);
        sheet.WriteToMaterial(this.Request.BizObject);
        base.OnSubmit(actionName, postValue, response);
    }

    protected override void OnWorkflowInstanceStateChanged(H3.Workflow.Instance.WorkflowInstanceState oldState, H3.Workflow.Instance.WorkflowInstanceState newState)
    {

        //流程审批结束,业务规则执行。还有个审批流程结束（finished状态下直接删除），就直接删除也会触发业务规则执行。在submit里面处理
        if (oldState == H3.Workflow.Instance.WorkflowInstanceState.Running && newState == H3.Workflow.Instance.WorkflowInstanceState.Finished)
        {

            DPuXinNewProcessSheet sheet = new DPuXinNewProcessSheet(this.Engine, this.Request.UserContext.UserId);
            //获取销售订单
            string objectId = this.Request.BizObjectId;
            sheet.WriteToPlanBySheet(objectId);
        }

        base.OnWorkflowInstanceStateChanged(oldState, newState);
    }
}



public class DPuXinNewProcessSheet
{
    IEngine Engine;
    string UserId;

    string processSheetSchemaCode = "D282605d63617ddbc1848fb8c93f4a3fa4e59c3";
    string processSheetChildSchemaCode = "D282605Ff555d078d0d54575aab7339874c31848";
    string processSheetDetailSchemaCode = "D282605Sz5g9cj2b7tj1pr4ax1ao7prg4";
    string planSchemaCode = "D282605Sbces9w53iq957t4gc8vulc6y2";
    string planChildSchemaCode = "D282605F80a2dc5e16c74b21813e7f1b5ad3615b";
    string materialSchemaCode = "D282605suten5clu70qwo9gkhtlp";
    public DPuXinNewProcessSheet(IEngine Engine, string UserId)
    {
        this.Engine = Engine;
        this.UserId = UserId;
    }
    private BizObject Load(string schema, string id)
    {
        return BizObject.Load(this.UserId,
            this.Engine, schema, id, false);
    }
    // 工艺单写入物料信息
    public void WriteToMaterial(BizObject sheet)
    {
        BizObject[] details = (BizObject[])sheet[this.processSheetChildSchemaCode];
        foreach (BizObject detail in details)
        {
            // 查询基础物料信息，是否已存在
            string materialNo = detail["MaterialNo"] + string.Empty;

            BizObject material = QueryMaterial(materialNo);
            if (material == null)
            {
                BizObjectSchema schema = this.Engine.BizObjectManager.GetPublishedSchema(this.materialSchemaCode);
                material = new BizObject(this.Engine, schema, this.UserId);
            }

            material["MaterialNo"] = detail["MaterialNo"];
            material["CreateOrg"] = "四川普鑫物流自动化设备工程有限公司";
            material["UseOrg"] = "四川普鑫物流自动化设备工程有限公司";
            material["Creater"] = this.Engine.Organization.GetName(this.UserId, H3.Organization.NameType.Name);
            material["CreateDate"] = System.DateTime.Now;
            material["MaterialName"] = detail["MaterialName"];
            material["MaterialModel"] = detail["MaterialModel"];
            material["Length"] = detail["Length"];
            material["Width"] = detail["Width"];
            material["Height"] = detail["Height"];
            material["Volume"] = detail["SprayingArea"];
            material["NetWeight"] = detail["NetWeight"];
            material.Status = BizObjectStatus.Effective; //设置状态生效
            material.Create();
            detail["Material"] = material["ObjectId"];
        }
    }

    //写入主计划
    public void WriteToPlanBySheet(string objectId)
    {
        //查询工艺单根据订单号
        BizObject sheet = Load(this.processSheetSchemaCode, objectId);
        BizObject[] details = (BizObject[])sheet[this.processSheetChildSchemaCode];

        List<BizObject> merges = new List<BizObject>();
        //数据合并
        foreach (BizObject detail in details)
        {
            BizObject exist = FindExist(merges, detail);
            if (exist == null)
            {
                merges.Add(detail);
            }
            else
            {
                exist["Count"] = this.ToInt(exist["Count"]) + this.ToInt(detail["Count"]);
                exist["SprayingSumArea"] = Convert.ToDecimal(exist["SprayingSumArea"]) + Convert.ToDecimal(detail["SprayingSumArea"]);
                exist["NetSumWeight"] = Convert.ToDecimal(exist["NetSumWeight"]) + Convert.ToDecimal(detail["NetSumWeight"]);
            }
            SaveToDetail(detail, sheet);
        }
        // 批量写入计划
        foreach (BizObject order in merges)
        {
            this.WriteToPlan(order, sheet);
        }
    }
    private BizObject SaveToDetail(BizObject obj, BizObject root)
    {
        string[] excludes = { "ParentObjectId", "State" };
        Dictionary<string, object> table = obj.GetValueTable();

        BizObject detail = Load(this.processSheetDetailSchemaCode, obj["ObjectId"] + string.Empty);
        bool isCreate = false;
        if (detail == null)
        {
            BizObjectSchema detailSchema = this.Engine.BizObjectManager.GetPublishedSchema(this.processSheetDetailSchemaCode);
            detail = new BizObject(this.Engine, detailSchema, this.UserId);
            detail.Status = BizObjectStatus.Effective; //设置状态生效
            isCreate = true;
        }
        detail["Customer"] = root["Customer"];
        detail["ProjectName"] = root["ProjectName"];
        detail["OrderNo"] = root["OrderNo"];
        detail["DeliveryDate"] = root["DeliveryDate"];
        foreach (KeyValuePair<string, object> pair in table)
        {
            string key = pair.Key;
            if (key.Contains("."))
            {
                key = key.Substring(key.IndexOf(".") + 1);
            }
            object value = pair.Value;

            if (Exist(excludes, key))
            {
                continue;
            }
            detail[key] = value;
        }

        if (isCreate)
        {
            detail.Create();
        }
        else
        {
            detail.Update();
        }
        return detail;
    }
    private bool Exist(string[] array, string key)
    {
        foreach (string a in array)
        {
            if (a == key)
            {
                return true;
            }
        }
        return false;
    }
    private BizObject FindExist(List<BizObject> list, BizObject order)
    {
        foreach (BizObject item in list)
        {
            if ((item["MaterialNo"] + string.Empty) == (order["MaterialNo"] + string.Empty))
            {
                return item;
            }
        }
        return null;
    }
    // 生成计划
    public void WriteToPlan(BizObject order, BizObject root)
    {
        // 查询计划中是否已存在该物料
        string orderNo = root["OrderNo"] + string.Empty;
        string materialNo = order["MaterialNo"] + string.Empty;
        if (this.CheckInPlan(orderNo, materialNo)) return;

        BizObjectSchema schema = this.Engine.BizObjectManager.GetPublishedSchema(this.planSchemaCode);
        BizObject plan = new BizObject(this.Engine, schema, this.UserId);

        plan["Customer"] = root["Customer"];
        plan["ProjectName"] = root["ProjectName"];
        plan["OrderNo"] = orderNo;
        plan["DrawSuffix"] = order["DrawSuffix"];
        plan["MaterialNo"] = order["MaterialNo"];
        plan["ParentMaterialNo"] = order["ParentMaterialNo"];
        plan["MaterialName"] = order["MaterialName"];
        plan["MaterialModel"] = order["MaterialModel"];
        plan["Length"] = order["Length"];
        plan["Width"] = order["Width"];
        plan["Height"] = order["Height"];
        plan["Count"] = order["Count"];
        plan["Weight"] = order["NetWeight"];
        plan["SumWeight"] = order["NetSumWeight"];
        plan["SprayColor"] = order["SprayColor"];
        plan["DeliveryDate"] = root["DeliveryDate"];
        plan["ProcessFlow"] = order["ProcessFlow"];

        string processFlow = order["ProcessFlow"] + string.Empty;
        if (processFlow.EndsWith(";"))
        {
            processFlow = processFlow.Substring(0, processFlow.Length - 1);
        }
        string[] processItems = processFlow.Split(';');
        if (processItems == null || processItems.Length == 0) return;
        string processLine = "/" + string.Join("/", processItems);
        DPuXinProcessCard card = new DPuXinProcessCard(processLine);

        List<DPuXinProcessGroup> groups = card.EnableGroups();
        if (groups.Count == 0) return;
        List<BizObject> sub = new List<BizObject>();
        foreach (DPuXinProcessGroup group in groups)
        {
            BizObject progress = new BizObject(this.Engine,
                schema.GetChildSchema(this.planChildSchemaCode), this.UserId);//子表对象 进度

            progress["ProcessName"] = group.GroupName();
            DateTime date = Convert.ToDateTime(root["DeliveryDate"]);
            progress["PlanDate"] = date.AddDays(group.DayDiff());
            sub.Add(progress);
        }
        plan["ReceiptDate"] = System.DateTime.Today;
        plan["ProcessLine"] = processLine;
        plan[this.planChildSchemaCode] = sub.ToArray();
        DPuXinQuasiState.ConfirmState(plan);
        plan.Status = BizObjectStatus.Effective; //设置状态生效
        plan.Create();
    }

    // 查询工艺单是否已存在
    private bool CheckInProcessSheet(string serialNo, string orderNo, string materialNo)
    {
        if (string.IsNullOrEmpty(materialNo) || string.IsNullOrEmpty(orderNo)) return false;

        string sql = String.Format("select SerialNo, OrderNo, MaterialNo from i_{0} where SerialNo = '{1}' and OrderNo = '{2}' and MaterialNo = '{3}'",
            this.processSheetSchemaCode, serialNo, orderNo, materialNo);
        DataTable table = this.Engine.Query.QueryTable(sql, null);

        if (table != null && table.Rows.Count > 0)
        {
            return true;
        }
        return false;
    }
    // 查询物料是否已存在
    private BizObject QueryMaterial(string materialNo)
    {
        H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();
        filter.Matcher = new H3.Data.Filter.ItemMatcher("MaterialNo", H3.Data.ComparisonOperatorType.Equal, materialNo);

        BizObject[] materials = BizObject.GetList(this.Engine, this.UserId,
            this.Engine.BizObjectManager.GetPublishedSchema(this.materialSchemaCode),
            GetListScopeType.GlobalAll, filter);

        if (materials == null || materials.Length == 0)
        {
            return null;
        }
        return materials[0];
    }
    // 查询计划是否已存在
    private bool CheckInPlan(string orderNo, string materialNo)
    {
        if (string.IsNullOrEmpty(materialNo) || string.IsNullOrEmpty(orderNo)) return false;
        string sql = String.Format("select OrderNo, MaterialNo from i_{0} where OrderNo = '{1}' and MaterialNo = '{2}'", this.planSchemaCode, orderNo, materialNo);
        DataTable table = this.Engine.Query.QueryTable(sql, null);

        if (table != null && table.Rows.Count > 0)
        {
            return true;
        }
        return false;
    }

    private int ToInt(object obj)
    {
        return Convert.ToInt32(String.IsNullOrEmpty(obj + "") ? "0" + obj : obj);
    }
}