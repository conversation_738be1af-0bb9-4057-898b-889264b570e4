mklink /d "C:/Users/<USER>/AppData/Roaming/uTools" "D:/Data/uTools"
mklink /d "C:/Users/<USER>/AppData/Local/Google/Chrome/User Data" "D:/Software/Develop/google/User Data"
mklink /d "C:/Users/<USER>/AppData/Local/Netease/CloudMusic" "D:/Data/CloudMusic"
mklink /d "C:/Users/<USER>/AppData/Roaming/Tencent" "D:/Data/Tencent"
mklink /d "C:\Users\<USER>\AppData\Roaming\DingTalk" "D:\Data\DingTalk"
mklink /d "C:\Users\<USER>\AppData\Roaming\Code" "C:\Users\<USER>\Develop\vscode\data"