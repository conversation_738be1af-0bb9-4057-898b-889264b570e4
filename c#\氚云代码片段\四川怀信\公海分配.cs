
using System;
using System.Collections.Generic;
using System.Text;
using H3;

public class D2781517a1b8ea1d27a4be5bca9ed15b415c59f : H3.SmartForm.SmartFormController
{
    public D2781517a1b8ea1d27a4be5bca9ed15b415c59f(H3.SmartForm.SmartFormRequest request) : base(request)
    {
    }

    protected override void OnLoad(H3.SmartForm.LoadSmartFormResponse response)
    {
        base.OnLoad(response);
    }

    protected override void OnSubmit(string actionName, H3.SmartForm.SmartFormPostValue postValue, H3.SmartForm.SubmitSmartFormResponse response)
    {
        string accountManager = this.Request.BizObject["AccountManager"] + string.Empty; //客户经理
        object merbers = this.Request.BizObject["Members"];
        H3.DataModel.BizObject[] upBos = (H3.DataModel.BizObject[])this.Request.BizObject["D278151F234ec9517cd94092b45c94e852f7068e"]; //客户信息id列表

        if (string.IsNullOrWhiteSpace(accountManager))
        {
            response.Message = "操作完成";
            return;
        }

        if (upBos == null || upBos.Length == 0)
        {
            response.Errors.Add("没有需要更新的表单");
            return;
        }
        foreach (H3.DataModel.BizObject upbo in upBos)
        {
            string objectId = upbo["F0000001"] + string.Empty;
            H3.DataModel.BizObject customerOfSea = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId,
                this.Engine, "D278151Sw1hb0g4hq7znsg6yq5fcbzxi2", objectId, false);     //加载客户公海信息
            if (customerOfSea == null)
            {
                response.Errors.Add("客户信息不存在");
                return;
            }

            //查询客户是否存在
            H3.DataModel.BizObject customer = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId,
                this.Engine, "Sahj8417jpmkya2ndghgvorg83", objectId, false);     //加载客户信息
            bool isCreate = false;
            if (customer == null)
            {
                H3.DataModel.BizObjectSchema customerSchema = this.Engine.BizObjectManager.GetPublishedSchema("Sahj8417jpmkya2ndghgvorg83");
                customer = new H3.DataModel.BizObject(this.Engine, customerSchema, this.Request.UserContext.UserId);
                isCreate = true;
            }

            copyObj(customer, customerOfSea, accountManager, merbers);
            customer.Status = H3.DataModel.BizObjectStatus.Effective; // 设置状态 ，触发业务规则

            customerOfSea.Remove();
            H3.ErrorCode code = isCreate ? customer.Create() : customer.Update();

            //跟进记录修改或插入数据
            // H3.DataModel.BizObject[] records = (H3.DataModel.BizObject[])customer["F34685c8b5e494255b73f944d259ec9f9"];
            // if (records != null && records.Length > 0)
            // {
            //     FollowUpRecord followUpRecord = new FollowUpRecord(this.Engine, this.Request.UserContext.UserId);
            //     foreach (H3.DataModel.BizObject record in records)
            //     {
            //         followUpRecord.Create(record, customer);
            //     }
            // }

            //修改销售订单拥有者
            string sql = "update I_Swl2tuysgxtp4164gsqyouwo25 set OwnerId='" + accountManager + "' where F0000002='" + objectId + "'";
            // 修改跟进记录
            sql += ";update I_Svkx5tb4uzms1bljuj6dq763d3 set OwnerId='" + accountManager + "' where F0000003='" + objectId + "'";
            this.Engine.EngineConfig.CreateCommand().ExecuteScalar(sql);
        }

        response.Message = "操作成功";
        response.ClosePage = true;
        response.Refresh = true;
        //base.OnSubmit(actionName, postValue, response);
    }

    private H3.DataModel.BizObject copyObj(H3.DataModel.BizObject customer, H3.DataModel.BizObject customerOfSea, string accountManager, object merbers)
    {
        H3.DataModel.BizObjectSchema customerSchema = customer.Schema;
        Dictionary<string, object> table = customerOfSea.GetValueTable();
        foreach (KeyValuePair<string, object> entry in table)
        {
            string key = entry.Key;
            object val = entry.Value;
            if (key == "OwnerId")
            {
                customer[key] = accountManager;
                customerOfSea[key] = accountManager;
                continue;
            }
            if (key == "OwnerDeptId")
            {
                H3.Organization.User user = this.Engine.Organization.GetUnit(accountManager) as H3.Organization.User;
                //customer[key] = user["ParentId"];//所属部门id
                continue;
            }
            if (key == "********" && merbers != null)
            {
                customer[key] = merbers;
                customerOfSea[key] = merbers;
                continue;
            }
            if (key == "D278151Fcmuo73gd8w0ngettezqehgbn1") //跟进记录
            {
                customer["F34685c8b5e494255b73f944d259ec9f9"] = copyChild(customerSchema.GetChildSchema("F34685c8b5e494255b73f944d259ec9f9"), key, val);
                continue;
            }
            if (key == "D278151Fwbdlojy2hbu0rdk0jni2sd7o1") //联系人
            {
                customer["Fmvl8idez8o6hkgrlq8wdhbl20"] = copyChild(customerSchema.GetChildSchema("Fmvl8idez8o6hkgrlq8wdhbl20"), key, val);
                continue;
            }
            if (key == "********")
            {
                customer[key] = "已跟进";//跟进状态
                customerOfSea[key] = "已跟进";//已跟进
                continue;
            }
            if (key == "********") //跟进时间
            {
                DateTime time = Convert.ToDateTime(customerOfSea[key]);
                H3.DataModel.BizObject[] records = (H3.DataModel.BizObject[])customerOfSea["D278151Fcmuo73gd8w0ngettezqehgbn1"];//取跟进记录
                if(records !=null && records.Length >0) {
                    Array.Sort(records, (p1, p2) => Convert.ToDateTime(p1["********"]).CompareTo(Convert.ToDateTime(p2["********"])));
                    H3.DataModel.BizObject last = records[records.Length - 1];
                    customer[key] = last["********"];
                    customerOfSea[key] = last["********"];
                    continue;
                }
            }
            customer[key] = val;
        }
        return customer;
    }
    private H3.DataModel.BizObject[] copyChild(H3.DataModel.BizObjectSchema childSchema, string prefix, object val)
    {
        if (val == null)
        {
            return null;
        }
        H3.DataModel.BizObject[] src = (H3.DataModel.BizObject[])val;
        List<H3.DataModel.BizObject> list = new List<H3.DataModel.BizObject>();

        int startIndex = prefix.Length + 1;
        foreach (H3.DataModel.BizObject bo in src)
        {
            H3.DataModel.BizObject child = new H3.DataModel.BizObject(this.Engine,
                childSchema, this.Request.UserContext.UserId);//子表对象
            Dictionary<string, object> table = bo.GetValueTable();
            foreach (KeyValuePair<string, object> entry in table)
            {

                string key = entry.Key.Substring(startIndex);
                object value = entry.Value;
                child[key] = value;
            }
            list.Add(child);
        }

        return list.ToArray();
    }
}