public class DPuXinResApi : H3.SmartForm.RestApiController
{
    //此构造方法必须有，但是方法体内不需要写任何代码

    public DPuXinResApi(H3.SmartForm.RestApiRequest request) : base(request) { }

    //第三方请求本自定义接口时，会触发本事件

    protected override void OnInvoke(string actionName, H3.SmartForm.RestApiResponse response)
    {
        try
        {
            if (actionName == "CreateOneData")
            {
                //从传入的参数里获取 key为“para1” 的值，若未传，则得到默认值“defaultValue”，若传的值无法转换成string类型，则报错
                string stringValue = this.Request.GetValue<string>("para1", "defaultValue");
                //从传入的参数里获取 key为“para2” 的值，若未传，则得到默认值0，若传的值无法转换成int类型，则报错
                int intValue = this.Request.GetValue<int>("para2", 0);
                //当找到了对应的actionName时，回复给第三方请求一个 key为“result”值为“success” 的结果
                response.ReturnData.Add("result", "success");
                //当找到了对应的actionName时，回复给第三方请求一个 key为“message”值为空字符串 的结果
                response.ReturnData.Add("message", string.Empty);
                //以下代码演示在自定义接口中查询业务对象的范例，如实际用不上请删除
                H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();
                H3.Data.Filter.And andMatcher = new H3.Data.Filter.And();
                andMatcher.Add(new H3.Data.Filter.ItemMatcher("Status", H3.Data.ComparisonOperatorType.Equal, 1));//筛选出数据状态为生效的数据
                filter.Matcher = andMatcher;
                H3.DataModel.BizObjectSchema schema = this.Request.Engine.BizObjectManager.GetPublishedSchema("D00021MyApiTest");
                //查询“D00021MyApiTest”表单里所有符合筛选条件的业务对象，得到一个业务对象数组
                //注：H3.Organization.User.SystemUserId   为系统默认用户Id，在定时器中、自定义接口中由于没有当前登录人，所以用这个代替this.Request.UserContext.UserId
                H3.DataModel.BizObject[] arr = H3.DataModel.BizObject.GetList(this.Request.Engine, H3.Organization.User.SystemUserId,
                    schema, H3.DataModel.GetListScopeType.GlobalAll, filter);
                Dictionary<string, object> dic = new Dictionary<string, object>();
                //从第三方传入参数里拿出传回给第三方
                dic.Add("StringValue", stringValue);
                //传回给第三方一个小数
                dic.Add("DoubleValue", 5.24343);
                //从第三方传入参数里拿出传回给第三方
                dic.Add("ArrayValue", new int[4] { intValue, intValue + 1, intValue - 2, intValue + 3 });
                //回复给第三方请求一个 key为“data”值为对象 的结果
                response.ReturnData.Add("data", dic);
            }
            else
            {
                //当传入一个未找到的actionName时，回复给第三方请求一个 key为“result”值为“error” 的结果
                response.ReturnData.Add("result", "error");
                //当传入一个未找到的actionName时，回复给第三方请求一个 key为“message”值为字符串 的异常提示
                response.ReturnData.Add("message", "无法处理actionName为“" + actionName + "”的请求！");
            }
        }
        catch (Exception ex)
        {
            //当执行的代码发生异常时，回复给第三方请求一个 key为“result”值为“error”   的结果
            response.ReturnData.Add("result", "error");
            //当执行的代码发生异常时，回复给第三方请求一个 key为“message”值为捕获到的异常原因
            response.ReturnData.Add("message", ex.Message);
        }
    }
}