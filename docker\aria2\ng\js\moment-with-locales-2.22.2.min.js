!function(e,a){"object"==typeof exports&&"undefined"!=typeof module?module.exports=a():"function"==typeof define&&define.amd?define(a):e.moment=a()}(this,function(){"use strict";function e(){return Ze.apply(null,arguments)}function a(e){return e instanceof Array||"[object Array]"===Object.prototype.toString.call(e)}function i(e){return null!=e&&"[object Object]"===Object.prototype.toString.call(e)}function t(e){return void 0===e}function r(e){return"number"==typeof e||"[object Number]"===Object.prototype.toString.call(e)}function n(e){return e instanceof Date||"[object Date]"===Object.prototype.toString.call(e)}function s(e,a){var i,t=[];for(i=0;i<e.length;++i)t.push(a(e[i],i));return t}function o(e,a){return Object.prototype.hasOwnProperty.call(e,a)}function c(e,a){for(var i in a)o(a,i)&&(e[i]=a[i]);return o(a,"toString")&&(e.toString=a.toString),o(a,"valueOf")&&(e.valueOf=a.valueOf),e}function u(e,a,i,t){return Ae(e,a,i,t,!0).utc()}function l(e){return null==e._pf&&(e._pf={empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],meridiem:null,rfc2822:!1,weekdayMismatch:!1}),e._pf}function d(e){if(null==e._isValid){var a=l(e),i=Xe.call(a.parsedDateParts,function(e){return null!=e}),t=!isNaN(e._d.getTime())&&a.overflow<0&&!a.empty&&!a.invalidMonth&&!a.invalidWeekday&&!a.weekdayMismatch&&!a.nullInput&&!a.invalidFormat&&!a.userInvalidated&&(!a.meridiem||a.meridiem&&i);if(e._strict&&(t=t&&0===a.charsLeftOver&&0===a.unusedTokens.length&&void 0===a.bigHour),null!=Object.isFrozen&&Object.isFrozen(e))return t;e._isValid=t}return e._isValid}function h(e){var a=u(NaN);return null!=e?c(l(a),e):l(a).userInvalidated=!0,a}function f(e,a){var i,r,n;if(t(a._isAMomentObject)||(e._isAMomentObject=a._isAMomentObject),t(a._i)||(e._i=a._i),t(a._f)||(e._f=a._f),t(a._l)||(e._l=a._l),t(a._strict)||(e._strict=a._strict),t(a._tzm)||(e._tzm=a._tzm),t(a._isUTC)||(e._isUTC=a._isUTC),t(a._offset)||(e._offset=a._offset),t(a._pf)||(e._pf=l(a)),t(a._locale)||(e._locale=a._locale),0<Qe.length)for(i=0;i<Qe.length;i++)t(n=a[r=Qe[i]])||(e[r]=n);return e}function m(a){f(this,a),this._d=new Date(null!=a._d?a._d.getTime():NaN),this.isValid()||(this._d=new Date(NaN)),!1===$e&&($e=!0,e.updateOffset(this),$e=!1)}function A(e){return e instanceof m||null!=e&&null!=e._isAMomentObject}function _(e){return e<0?Math.ceil(e)||0:Math.floor(e)}function p(e){var a=+e,i=0;return 0!==a&&isFinite(a)&&(i=_(a)),i}function M(e,a,i){var t,r=Math.min(e.length,a.length),n=Math.abs(e.length-a.length),s=0;for(t=0;t<r;t++)(i&&e[t]!==a[t]||!i&&p(e[t])!==p(a[t]))&&s++;return s+n}function g(a){!1===e.suppressDeprecationWarnings&&"undefined"!=typeof console&&console.warn&&console.warn("Deprecation warning: "+a)}function y(a,i){var t=!0;return c(function(){if(null!=e.deprecationHandler&&e.deprecationHandler(null,a),t){for(var r,n=[],s=0;s<arguments.length;s++){if(r="","object"==typeof arguments[s]){for(var o in r+="\n["+s+"] ",arguments[0])r+=o+": "+arguments[0][o]+", ";r=r.slice(0,-2)}else r=arguments[s];n.push(r)}g(a+"\nArguments: "+Array.prototype.slice.call(n).join("")+"\n"+(new Error).stack),t=!1}return i.apply(this,arguments)},i)}function b(a,i){null!=e.deprecationHandler&&e.deprecationHandler(a,i),ea[a]||(g(i),ea[a]=!0)}function S(e){return e instanceof Function||"[object Function]"===Object.prototype.toString.call(e)}function k(e,a){var t,r=c({},e);for(t in a)o(a,t)&&(i(e[t])&&i(a[t])?(r[t]={},c(r[t],e[t]),c(r[t],a[t])):null!=a[t]?r[t]=a[t]:delete r[t]);for(t in e)o(e,t)&&!o(a,t)&&i(e[t])&&(r[t]=c({},r[t]));return r}function T(e){null!=e&&this.set(e)}function D(e,a){var i=e.toLowerCase();aa[i]=aa[i+"s"]=aa[a]=e}function w(e){return"string"==typeof e?aa[e]||aa[e.toLowerCase()]:void 0}function v(e){var a,i,t={};for(i in e)o(e,i)&&(a=w(i))&&(t[a]=e[i]);return t}function Y(e,a){ia[e]=a}function E(e,a,i){var t=""+Math.abs(e),r=a-t.length;return(0<=e?i?"+":"":"-")+Math.pow(10,Math.max(0,r)).toString().substr(1)+t}function P(e,a,i,t){var r=t;"string"==typeof t&&(r=function(){return this[t]()}),e&&(sa[e]=r),a&&(sa[a[0]]=function(){return E(r.apply(this,arguments),a[1],a[2])}),i&&(sa[i]=function(){return this.localeData().ordinal(r.apply(this,arguments),e)})}function z(e,a){return e.isValid()?(a=O(a,e.localeData()),na[a]=na[a]||function(e){var a,i,t,r=e.match(ta);for(a=0,i=r.length;a<i;a++)sa[r[a]]?r[a]=sa[r[a]]:r[a]=(t=r[a]).match(/\[[\s\S]/)?t.replace(/^\[|\]$/g,""):t.replace(/\\/g,"");return function(a){var t,n="";for(t=0;t<i;t++)n+=S(r[t])?r[t].call(a,e):r[t];return n}}(a),na[a](e)):e.localeData().invalidDate()}function O(e,a){function i(e){return a.longDateFormat(e)||e}var t=5;for(ra.lastIndex=0;0<=t&&ra.test(e);)e=e.replace(ra,i),ra.lastIndex=0,t-=1;return e}function L(e,a,i){ka[e]=S(a)?a:function(e,t){return e&&i?i:a}}function C(e,a){return o(ka,e)?ka[e](a._strict,a._locale):new RegExp(N(e.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,function(e,a,i,t,r){return a||i||t||r})))}function N(e){return e.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}function G(e,a){var i,t=a;for("string"==typeof e&&(e=[e]),r(a)&&(t=function(e,i){i[a]=p(e)}),i=0;i<e.length;i++)Ta[e[i]]=t}function W(e,a){G(e,function(e,i,t,r){t._w=t._w||{},a(e,t._w,t,r)})}function x(e){return H(e)?366:365}function H(e){return e%4==0&&e%100!=0||e%400==0}function R(a,i){return function(t){return null!=t?(F(this,a,t),e.updateOffset(this,i),this):B(this,a)}}function B(e,a){return e.isValid()?e._d["get"+(e._isUTC?"UTC":"")+a]():NaN}function F(e,a,i){e.isValid()&&!isNaN(i)&&("FullYear"===a&&H(e.year())&&1===e.month()&&29===e.date()?e._d["set"+(e._isUTC?"UTC":"")+a](i,e.month(),q(i,e.month())):e._d["set"+(e._isUTC?"UTC":"")+a](i))}function q(e,a){if(isNaN(e)||isNaN(a))return NaN;var i,t=(a%(i=12)+i)%i;return e+=(a-t)/12,1===t?H(e)?29:28:31-t%7%2}function I(e,a){var i;if(!e.isValid())return e;if("string"==typeof a)if(/^\d+$/.test(a))a=p(a);else if(!r(a=e.localeData().monthsParse(a)))return e;return i=Math.min(e.date(),q(e.year(),a)),e._d["set"+(e._isUTC?"UTC":"")+"Month"](a,i),e}function U(a){return null!=a?(I(this,a),e.updateOffset(this,!0),this):B(this,"Month")}function j(){function e(e,a){return a.length-e.length}var a,i,t=[],r=[],n=[];for(a=0;a<12;a++)i=u([2e3,a]),t.push(this.monthsShort(i,"")),r.push(this.months(i,"")),n.push(this.months(i,"")),n.push(this.monthsShort(i,""));for(t.sort(e),r.sort(e),n.sort(e),a=0;a<12;a++)t[a]=N(t[a]),r[a]=N(r[a]);for(a=0;a<24;a++)n[a]=N(n[a]);this._monthsRegex=new RegExp("^("+n.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+r.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+t.join("|")+")","i")}function V(e){var a=new Date(Date.UTC.apply(null,arguments));return e<100&&0<=e&&isFinite(a.getUTCFullYear())&&a.setUTCFullYear(e),a}function K(e,a,i){var t=7+a-i;return-(7+V(e,0,t).getUTCDay()-a)%7+t-1}function Z(e,a,i,t,r){var n,s,o=1+7*(a-1)+(7+i-t)%7+K(e,t,r);return o<=0?s=x(n=e-1)+o:o>x(e)?(n=e+1,s=o-x(e)):(n=e,s=o),{year:n,dayOfYear:s}}function X(e,a,i){var t,r,n=K(e.year(),a,i),s=Math.floor((e.dayOfYear()-n-1)/7)+1;return s<1?t=s+J(r=e.year()-1,a,i):s>J(e.year(),a,i)?(t=s-J(e.year(),a,i),r=e.year()+1):(r=e.year(),t=s),{week:t,year:r}}function J(e,a,i){var t=K(e,a,i),r=K(e+1,a,i);return(x(e)-t+r)/7}function Q(){function e(e,a){return a.length-e.length}var a,i,t,r,n,s=[],o=[],c=[],l=[];for(a=0;a<7;a++)i=u([2e3,1]).day(a),t=this.weekdaysMin(i,""),r=this.weekdaysShort(i,""),n=this.weekdays(i,""),s.push(t),o.push(r),c.push(n),l.push(t),l.push(r),l.push(n);for(s.sort(e),o.sort(e),c.sort(e),l.sort(e),a=0;a<7;a++)o[a]=N(o[a]),c[a]=N(c[a]),l[a]=N(l[a]);this._weekdaysRegex=new RegExp("^("+l.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+c.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+o.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+s.join("|")+")","i")}function $(){return this.hours()%12||12}function ee(e,a){P(e,0,0,function(){return this.localeData().meridiem(this.hours(),this.minutes(),a)})}function ae(e,a){return a._meridiemParse}function ie(e){return e?e.toLowerCase().replace("_","-"):e}function te(e){var a=null;if(!Xa[e]&&"undefined"!=typeof module&&module&&module.exports)try{a=Va._abbr,require("./locale/"+e),re(a)}catch(e){}return Xa[e]}function re(e,a){var i;return e&&((i=t(a)?se(e):ne(e,a))?Va=i:"undefined"!=typeof console&&console.warn&&console.warn("Locale "+e+" not found. Did you forget to load it?")),Va._abbr}function ne(e,a){if(null!==a){var i,t=Za;if(a.abbr=e,null!=Xa[e])b("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),t=Xa[e]._config;else if(null!=a.parentLocale)if(null!=Xa[a.parentLocale])t=Xa[a.parentLocale]._config;else{if(null==(i=te(a.parentLocale)))return Ja[a.parentLocale]||(Ja[a.parentLocale]=[]),Ja[a.parentLocale].push({name:e,config:a}),null;t=i._config}return Xa[e]=new T(k(t,a)),Ja[e]&&Ja[e].forEach(function(e){ne(e.name,e.config)}),re(e),Xa[e]}return delete Xa[e],null}function se(e){var i;if(e&&e._locale&&e._locale._abbr&&(e=e._locale._abbr),!e)return Va;if(!a(e)){if(i=te(e))return i;e=[e]}return function(e){for(var a,i,t,r,n=0;n<e.length;){for(a=(r=ie(e[n]).split("-")).length,i=(i=ie(e[n+1]))?i.split("-"):null;0<a;){if(t=te(r.slice(0,a).join("-")))return t;if(i&&i.length>=a&&M(r,i,!0)>=a-1)break;a--}n++}return Va}(e)}function oe(e){var a,i=e._a;return i&&-2===l(e).overflow&&(a=i[wa]<0||11<i[wa]?wa:i[va]<1||i[va]>q(i[Da],i[wa])?va:i[Ya]<0||24<i[Ya]||24===i[Ya]&&(0!==i[Ea]||0!==i[Pa]||0!==i[za])?Ya:i[Ea]<0||59<i[Ea]?Ea:i[Pa]<0||59<i[Pa]?Pa:i[za]<0||999<i[za]?za:-1,l(e)._overflowDayOfYear&&(a<Da||va<a)&&(a=va),l(e)._overflowWeeks&&-1===a&&(a=Oa),l(e)._overflowWeekday&&-1===a&&(a=La),l(e).overflow=a),e}function ce(e,a,i){return null!=e?e:null!=a?a:i}function ue(a){var i,t,r,n,s,o=[];if(!a._d){var c,u;for(c=a,u=new Date(e.now()),r=c._useUTC?[u.getUTCFullYear(),u.getUTCMonth(),u.getUTCDate()]:[u.getFullYear(),u.getMonth(),u.getDate()],a._w&&null==a._a[va]&&null==a._a[wa]&&function(e){var a,i,t,r,n,s,o,c;if(null!=(a=e._w).GG||null!=a.W||null!=a.E)n=1,s=4,i=ce(a.GG,e._a[Da],X(_e(),1,4).year),t=ce(a.W,1),((r=ce(a.E,1))<1||7<r)&&(c=!0);else{n=e._locale._week.dow,s=e._locale._week.doy;var u=X(_e(),n,s);i=ce(a.gg,e._a[Da],u.year),t=ce(a.w,u.week),null!=a.d?((r=a.d)<0||6<r)&&(c=!0):null!=a.e?(r=a.e+n,(a.e<0||6<a.e)&&(c=!0)):r=n}t<1||t>J(i,n,s)?l(e)._overflowWeeks=!0:null!=c?l(e)._overflowWeekday=!0:(o=Z(i,t,r,n,s),e._a[Da]=o.year,e._dayOfYear=o.dayOfYear)}(a),null!=a._dayOfYear&&(s=ce(a._a[Da],r[Da]),(a._dayOfYear>x(s)||0===a._dayOfYear)&&(l(a)._overflowDayOfYear=!0),t=V(s,0,a._dayOfYear),a._a[wa]=t.getUTCMonth(),a._a[va]=t.getUTCDate()),i=0;i<3&&null==a._a[i];++i)a._a[i]=o[i]=r[i];for(;i<7;i++)a._a[i]=o[i]=null==a._a[i]?2===i?1:0:a._a[i];24===a._a[Ya]&&0===a._a[Ea]&&0===a._a[Pa]&&0===a._a[za]&&(a._nextDay=!0,a._a[Ya]=0),a._d=(a._useUTC?V:function(e,a,i,t,r,n,s){var o=new Date(e,a,i,t,r,n,s);return e<100&&0<=e&&isFinite(o.getFullYear())&&o.setFullYear(e),o}).apply(null,o),n=a._useUTC?a._d.getUTCDay():a._d.getDay(),null!=a._tzm&&a._d.setUTCMinutes(a._d.getUTCMinutes()-a._tzm),a._nextDay&&(a._a[Ya]=24),a._w&&void 0!==a._w.d&&a._w.d!==n&&(l(a).weekdayMismatch=!0)}}function le(e){var a,i,t,r,n,s,o=e._i,c=Qa.exec(o)||$a.exec(o);if(c){for(l(e).iso=!0,a=0,i=ai.length;a<i;a++)if(ai[a][1].exec(c[1])){r=ai[a][0],t=!1!==ai[a][2];break}if(null==r)return void(e._isValid=!1);if(c[3]){for(a=0,i=ii.length;a<i;a++)if(ii[a][1].exec(c[3])){n=(c[2]||" ")+ii[a][0];break}if(null==n)return void(e._isValid=!1)}if(!t&&null!=n)return void(e._isValid=!1);if(c[4]){if(!ei.exec(c[4]))return void(e._isValid=!1);s="Z"}e._f=r+(n||"")+(s||""),fe(e)}else e._isValid=!1}function de(e,a,i,t,r,n){var s=[function(e){var a=parseInt(e,10);return a<=49?2e3+a:a<=999?1900+a:a}(e),xa.indexOf(a),parseInt(i,10),parseInt(t,10),parseInt(r,10)];return n&&s.push(parseInt(n,10)),s}function he(e){var a,i,t,r=ri.exec(e._i.replace(/\([^)]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,""));if(r){var n=de(r[4],r[3],r[2],r[5],r[6],r[7]);if(a=r[1],i=n,t=e,a&&Fa.indexOf(a)!==new Date(i[0],i[1],i[2]).getDay()&&(l(t).weekdayMismatch=!0,!(t._isValid=!1)))return;e._a=n,e._tzm=function(e,a,i){if(e)return ni[e];if(a)return 0;var t=parseInt(i,10),r=t%100;return(t-r)/100*60+r}(r[8],r[9],r[10]),e._d=V.apply(null,e._a),e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),l(e).rfc2822=!0}else e._isValid=!1}function fe(a){if(a._f!==e.ISO_8601)if(a._f!==e.RFC_2822){a._a=[],l(a).empty=!0;var i,t,r,n,s,c,u,d,h=""+a._i,f=h.length,m=0;for(r=O(a._f,a._locale).match(ta)||[],i=0;i<r.length;i++)n=r[i],(t=(h.match(C(n,a))||[])[0])&&(0<(s=h.substr(0,h.indexOf(t))).length&&l(a).unusedInput.push(s),h=h.slice(h.indexOf(t)+t.length),m+=t.length),sa[n]?(t?l(a).empty=!1:l(a).unusedTokens.push(n),c=n,d=a,null!=(u=t)&&o(Ta,c)&&Ta[c](u,d._a,d,c)):a._strict&&!t&&l(a).unusedTokens.push(n);l(a).charsLeftOver=f-m,0<h.length&&l(a).unusedInput.push(h),a._a[Ya]<=12&&!0===l(a).bigHour&&0<a._a[Ya]&&(l(a).bigHour=void 0),l(a).parsedDateParts=a._a.slice(0),l(a).meridiem=a._meridiem,a._a[Ya]=function(e,a,i){var t;return null==i?a:null!=e.meridiemHour?e.meridiemHour(a,i):(null!=e.isPM&&((t=e.isPM(i))&&a<12&&(a+=12),t||12!==a||(a=0)),a)}(a._locale,a._a[Ya],a._meridiem),ue(a),oe(a)}else he(a);else le(a)}function me(o){var u,_,p,M,g=o._i,y=o._f;return o._locale=o._locale||se(o._l),null===g||void 0===y&&""===g?h({nullInput:!0}):("string"==typeof g&&(o._i=g=o._locale.preparse(g)),A(g)?new m(oe(g)):(n(g)?o._d=g:a(y)?function(e){var a,i,t,r,n;if(0===e._f.length)return l(e).invalidFormat=!0,e._d=new Date(NaN);for(r=0;r<e._f.length;r++)n=0,a=f({},e),null!=e._useUTC&&(a._useUTC=e._useUTC),a._f=e._f[r],fe(a),d(a)&&(n+=l(a).charsLeftOver,n+=10*l(a).unusedTokens.length,l(a).score=n,(null==t||n<t)&&(t=n,i=a));c(e,i||a)}(o):y?fe(o):t(_=(u=o)._i)?u._d=new Date(e.now()):n(_)?u._d=new Date(_.valueOf()):"string"==typeof _?(p=u,null===(M=ti.exec(p._i))?(le(p),!1===p._isValid&&(delete p._isValid,he(p),!1===p._isValid&&(delete p._isValid,e.createFromInputFallback(p)))):p._d=new Date(+M[1])):a(_)?(u._a=s(_.slice(0),function(e){return parseInt(e,10)}),ue(u)):i(_)?function(e){if(!e._d){var a=v(e._i);e._a=s([a.year,a.month,a.day||a.date,a.hour,a.minute,a.second,a.millisecond],function(e){return e&&parseInt(e,10)}),ue(e)}}(u):r(_)?u._d=new Date(_):e.createFromInputFallback(u),d(o)||(o._d=null),o))}function Ae(e,t,r,n,s){var o,c={};return!0!==r&&!1!==r||(n=r,r=void 0),(i(e)&&function(e){if(Object.getOwnPropertyNames)return 0===Object.getOwnPropertyNames(e).length;var a;for(a in e)if(e.hasOwnProperty(a))return!1;return!0}(e)||a(e)&&0===e.length)&&(e=void 0),c._isAMomentObject=!0,c._useUTC=c._isUTC=s,c._l=r,c._i=e,c._f=t,c._strict=n,(o=new m(oe(me(c))))._nextDay&&(o.add(1,"d"),o._nextDay=void 0),o}function _e(e,a,i,t){return Ae(e,a,i,t,!1)}function pe(e,i){var t,r;if(1===i.length&&a(i[0])&&(i=i[0]),!i.length)return _e();for(t=i[0],r=1;r<i.length;++r)i[r].isValid()&&!i[r][e](t)||(t=i[r]);return t}function Me(e){var a=v(e),i=a.year||0,t=a.quarter||0,r=a.month||0,n=a.week||0,s=a.day||0,o=a.hour||0,c=a.minute||0,u=a.second||0,l=a.millisecond||0;this._isValid=function(e){for(var a in e)if(-1===Ca.call(ci,a)||null!=e[a]&&isNaN(e[a]))return!1;for(var i=!1,t=0;t<ci.length;++t)if(e[ci[t]]){if(i)return!1;parseFloat(e[ci[t]])!==p(e[ci[t]])&&(i=!0)}return!0}(a),this._milliseconds=+l+1e3*u+6e4*c+1e3*o*60*60,this._days=+s+7*n,this._months=+r+3*t+12*i,this._data={},this._locale=se(),this._bubble()}function ge(e){return e instanceof Me}function ye(e){return e<0?-1*Math.round(-1*e):Math.round(e)}function be(e,a){P(e,0,0,function(){var e=this.utcOffset(),i="+";return e<0&&(e=-e,i="-"),i+E(~~(e/60),2)+a+E(~~e%60,2)})}function Se(e,a){var i=(a||"").match(e);if(null===i)return null;var t=((i[i.length-1]||[])+"").match(ui)||["-",0,0],r=60*t[1]+p(t[2]);return 0===r?0:"+"===t[0]?r:-r}function ke(a,i){var t,r;return i._isUTC?(t=i.clone(),r=(A(a)||n(a)?a.valueOf():_e(a).valueOf())-t.valueOf(),t._d.setTime(t._d.valueOf()+r),e.updateOffset(t,!1),t):_e(a).local()}function Te(e){return 15*-Math.round(e._d.getTimezoneOffset()/15)}function De(){return!!this.isValid()&&this._isUTC&&0===this._offset}function we(e,a){var i,t,n,s=e,c=null;return ge(e)?s={ms:e._milliseconds,d:e._days,M:e._months}:r(e)?(s={},a?s[a]=e:s.milliseconds=e):(c=li.exec(e))?(i="-"===c[1]?-1:1,s={y:0,d:p(c[va])*i,h:p(c[Ya])*i,m:p(c[Ea])*i,s:p(c[Pa])*i,ms:p(ye(1e3*c[za]))*i}):(c=di.exec(e))?(i="-"===c[1]?-1:(c[1],1),s={y:ve(c[2],i),M:ve(c[3],i),w:ve(c[4],i),d:ve(c[5],i),h:ve(c[6],i),m:ve(c[7],i),s:ve(c[8],i)}):null==s?s={}:"object"==typeof s&&("from"in s||"to"in s)&&(n=function(e,a){var i;return e.isValid()&&a.isValid()?(a=ke(a,e),e.isBefore(a)?i=Ye(e,a):((i=Ye(a,e)).milliseconds=-i.milliseconds,i.months=-i.months),i):{milliseconds:0,months:0}}(_e(s.from),_e(s.to)),(s={}).ms=n.milliseconds,s.M=n.months),t=new Me(s),ge(e)&&o(e,"_locale")&&(t._locale=e._locale),t}function ve(e,a){var i=e&&parseFloat(e.replace(",","."));return(isNaN(i)?0:i)*a}function Ye(e,a){var i={milliseconds:0,months:0};return i.months=a.month()-e.month()+12*(a.year()-e.year()),e.clone().add(i.months,"M").isAfter(a)&&--i.months,i.milliseconds=+a-+e.clone().add(i.months,"M"),i}function Ee(e,a){return function(i,t){var r;return null===t||isNaN(+t)||(b(a,"moment()."+a+"(period, number) is deprecated. Please use moment()."+a+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),r=i,i=t,t=r),Pe(this,we(i="string"==typeof i?+i:i,t),e),this}}function Pe(a,i,t,r){var n=i._milliseconds,s=ye(i._days),o=ye(i._months);a.isValid()&&(r=null==r||r,o&&I(a,B(a,"Month")+o*t),s&&F(a,"Date",B(a,"Date")+s*t),n&&a._d.setTime(a._d.valueOf()+n*t),r&&e.updateOffset(a,s||o))}function ze(e,a){var i=12*(a.year()-e.year())+(a.month()-e.month()),t=e.clone().add(i,"months");return-(i+(a-t<0?(a-t)/(t-e.clone().add(i-1,"months")):(a-t)/(e.clone().add(i+1,"months")-t)))||0}function Oe(e){var a;return void 0===e?this._locale._abbr:(null!=(a=se(e))&&(this._locale=a),this)}function Le(){return this._locale}function Ce(e,a){P(0,[e,e.length],0,a)}function Ne(e,a,i,t,r){var n;return null==e?X(this,t,r).year:((n=J(e,t,r))<a&&(a=n),function(e,a,i,t,r){var n=Z(e,a,i,t,r),s=V(n.year,0,n.dayOfYear);return this.year(s.getUTCFullYear()),this.month(s.getUTCMonth()),this.date(s.getUTCDate()),this}.call(this,e,a,i,t,r))}function Ge(e,a){a[za]=p(1e3*("0."+e))}function We(e){return e}function xe(e,a,i,t){var r=se(),n=u().set(t,a);return r[i](n,e)}function He(e,a,i){if(r(e)&&(a=e,e=void 0),e=e||"",null!=a)return xe(e,a,i,"month");var t,n=[];for(t=0;t<12;t++)n[t]=xe(e,t,i,"month");return n}function Re(e,a,i,t){"boolean"==typeof e?r(a)&&(i=a,a=void 0):(a=e,e=!1,r(i=a)&&(i=a,a=void 0)),a=a||"";var n,s=se(),o=e?s._week.dow:0;if(null!=i)return xe(a,(i+o)%7,t,"day");var c=[];for(n=0;n<7;n++)c[n]=xe(a,(n+o)%7,t,"day");return c}function Be(e,a,i,t){var r=we(a,i);return e._milliseconds+=t*r._milliseconds,e._days+=t*r._days,e._months+=t*r._months,e._bubble()}function Fe(e){return e<0?Math.floor(e):Math.ceil(e)}function qe(e){return 4800*e/146097}function Ie(e){return 146097*e/4800}function Ue(e){return function(){return this.as(e)}}function je(e){return function(){return this.isValid()?this._data[e]:NaN}}function Ve(e){return(0<e)-(e<0)||+e}function Ke(){if(!this.isValid())return this.localeData().invalidDate();var e,a,i=Ri(this._milliseconds)/1e3,t=Ri(this._days),r=Ri(this._months);a=_((e=_(i/60))/60),i%=60,e%=60;var n=_(r/12),s=r%=12,o=t,c=a,u=e,l=i?i.toFixed(3).replace(/\.?0+$/,""):"",d=this.asSeconds();if(!d)return"P0D";var h=d<0?"-":"",f=Ve(this._months)!==Ve(d)?"-":"",m=Ve(this._days)!==Ve(d)?"-":"",A=Ve(this._milliseconds)!==Ve(d)?"-":"";return h+"P"+(n?f+n+"Y":"")+(s?f+s+"M":"")+(o?m+o+"D":"")+(c||u||l?"T":"")+(c?A+c+"H":"")+(u?A+u+"M":"")+(l?A+l+"S":"")}var Ze,Xe;Xe=Array.prototype.some?Array.prototype.some:function(e){for(var a=Object(this),i=a.length>>>0,t=0;t<i;t++)if(t in a&&e.call(this,a[t],t,a))return!0;return!1};var Je,Qe=e.momentProperties=[],$e=!1,ea={};e.suppressDeprecationWarnings=!1,e.deprecationHandler=null,Je=Object.keys?Object.keys:function(e){var a,i=[];for(a in e)o(e,a)&&i.push(a);return i};var aa={},ia={},ta=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|YYYYYY|YYYYY|YYYY|YY|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,ra=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,na={},sa={},oa=/\d/,ca=/\d\d/,ua=/\d{3}/,la=/\d{4}/,da=/[+-]?\d{6}/,ha=/\d\d?/,fa=/\d\d\d\d?/,ma=/\d\d\d\d\d\d?/,Aa=/\d{1,3}/,_a=/\d{1,4}/,pa=/[+-]?\d{1,6}/,Ma=/\d+/,ga=/[+-]?\d+/,ya=/Z|[+-]\d\d:?\d\d/gi,ba=/Z|[+-]\d\d(?::?\d\d)?/gi,Sa=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i,ka={},Ta={},Da=0,wa=1,va=2,Ya=3,Ea=4,Pa=5,za=6,Oa=7,La=8;P("Y",0,0,function(){var e=this.year();return e<=9999?""+e:"+"+e}),P(0,["YY",2],0,function(){return this.year()%100}),P(0,["YYYY",4],0,"year"),P(0,["YYYYY",5],0,"year"),P(0,["YYYYYY",6,!0],0,"year"),D("year","y"),Y("year",1),L("Y",ga),L("YY",ha,ca),L("YYYY",_a,la),L("YYYYY",pa,da),L("YYYYYY",pa,da),G(["YYYYY","YYYYYY"],Da),G("YYYY",function(a,i){i[Da]=2===a.length?e.parseTwoDigitYear(a):p(a)}),G("YY",function(a,i){i[Da]=e.parseTwoDigitYear(a)}),G("Y",function(e,a){a[Da]=parseInt(e,10)}),e.parseTwoDigitYear=function(e){return p(e)+(68<p(e)?1900:2e3)};var Ca,Na=R("FullYear",!0);Ca=Array.prototype.indexOf?Array.prototype.indexOf:function(e){var a;for(a=0;a<this.length;++a)if(this[a]===e)return a;return-1},P("M",["MM",2],"Mo",function(){return this.month()+1}),P("MMM",0,0,function(e){return this.localeData().monthsShort(this,e)}),P("MMMM",0,0,function(e){return this.localeData().months(this,e)}),D("month","M"),Y("month",8),L("M",ha),L("MM",ha,ca),L("MMM",function(e,a){return a.monthsShortRegex(e)}),L("MMMM",function(e,a){return a.monthsRegex(e)}),G(["M","MM"],function(e,a){a[wa]=p(e)-1}),G(["MMM","MMMM"],function(e,a,i,t){var r=i._locale.monthsParse(e,t,i._strict);null!=r?a[wa]=r:l(i).invalidMonth=e});var Ga=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/,Wa="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),xa="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),Ha=Sa,Ra=Sa;P("w",["ww",2],"wo","week"),P("W",["WW",2],"Wo","isoWeek"),D("week","w"),D("isoWeek","W"),Y("week",5),Y("isoWeek",5),L("w",ha),L("ww",ha,ca),L("W",ha),L("WW",ha,ca),W(["w","ww","W","WW"],function(e,a,i,t){a[t.substr(0,1)]=p(e)}),P("d",0,"do","day"),P("dd",0,0,function(e){return this.localeData().weekdaysMin(this,e)}),P("ddd",0,0,function(e){return this.localeData().weekdaysShort(this,e)}),P("dddd",0,0,function(e){return this.localeData().weekdays(this,e)}),P("e",0,0,"weekday"),P("E",0,0,"isoWeekday"),D("day","d"),D("weekday","e"),D("isoWeekday","E"),Y("day",11),Y("weekday",11),Y("isoWeekday",11),L("d",ha),L("e",ha),L("E",ha),L("dd",function(e,a){return a.weekdaysMinRegex(e)}),L("ddd",function(e,a){return a.weekdaysShortRegex(e)}),L("dddd",function(e,a){return a.weekdaysRegex(e)}),W(["dd","ddd","dddd"],function(e,a,i,t){var r=i._locale.weekdaysParse(e,t,i._strict);null!=r?a.d=r:l(i).invalidWeekday=e}),W(["d","e","E"],function(e,a,i,t){a[t]=p(e)});var Ba="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),Fa="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),qa="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),Ia=Sa,Ua=Sa,ja=Sa;P("H",["HH",2],0,"hour"),P("h",["hh",2],0,$),P("k",["kk",2],0,function(){return this.hours()||24}),P("hmm",0,0,function(){return""+$.apply(this)+E(this.minutes(),2)}),P("hmmss",0,0,function(){return""+$.apply(this)+E(this.minutes(),2)+E(this.seconds(),2)}),P("Hmm",0,0,function(){return""+this.hours()+E(this.minutes(),2)}),P("Hmmss",0,0,function(){return""+this.hours()+E(this.minutes(),2)+E(this.seconds(),2)}),ee("a",!0),ee("A",!1),D("hour","h"),Y("hour",13),L("a",ae),L("A",ae),L("H",ha),L("h",ha),L("k",ha),L("HH",ha,ca),L("hh",ha,ca),L("kk",ha,ca),L("hmm",fa),L("hmmss",ma),L("Hmm",fa),L("Hmmss",ma),G(["H","HH"],Ya),G(["k","kk"],function(e,a,i){var t=p(e);a[Ya]=24===t?0:t}),G(["a","A"],function(e,a,i){i._isPm=i._locale.isPM(e),i._meridiem=e}),G(["h","hh"],function(e,a,i){a[Ya]=p(e),l(i).bigHour=!0}),G("hmm",function(e,a,i){var t=e.length-2;a[Ya]=p(e.substr(0,t)),a[Ea]=p(e.substr(t)),l(i).bigHour=!0}),G("hmmss",function(e,a,i){var t=e.length-4,r=e.length-2;a[Ya]=p(e.substr(0,t)),a[Ea]=p(e.substr(t,2)),a[Pa]=p(e.substr(r)),l(i).bigHour=!0}),G("Hmm",function(e,a,i){var t=e.length-2;a[Ya]=p(e.substr(0,t)),a[Ea]=p(e.substr(t))}),G("Hmmss",function(e,a,i){var t=e.length-4,r=e.length-2;a[Ya]=p(e.substr(0,t)),a[Ea]=p(e.substr(t,2)),a[Pa]=p(e.substr(r))});var Va,Ka=R("Hours",!0),Za={calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},longDateFormat:{LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},invalidDate:"Invalid date",ordinal:"%d",dayOfMonthOrdinalParse:/\d{1,2}/,relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},months:Wa,monthsShort:xa,week:{dow:0,doy:6},weekdays:Ba,weekdaysMin:qa,weekdaysShort:Fa,meridiemParse:/[ap]\.?m?\.?/i},Xa={},Ja={},Qa=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?$/,$a=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?$/,ei=/Z|[+-]\d\d(?::?\d\d)?/,ai=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/]],ii=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],ti=/^\/?Date\((\-?\d+)/i,ri=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/,ni={UT:0,GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};e.createFromInputFallback=y("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged and will be removed in an upcoming major release. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",function(e){e._d=new Date(e._i+(e._useUTC?" UTC":""))}),e.ISO_8601=function(){},e.RFC_2822=function(){};var si=y("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var e=_e.apply(null,arguments);return this.isValid()&&e.isValid()?e<this?this:e:h()}),oi=y("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var e=_e.apply(null,arguments);return this.isValid()&&e.isValid()?this<e?this:e:h()}),ci=["year","quarter","month","week","day","hour","minute","second","millisecond"];be("Z",":"),be("ZZ",""),L("Z",ba),L("ZZ",ba),G(["Z","ZZ"],function(e,a,i){i._useUTC=!0,i._tzm=Se(ba,e)});var ui=/([\+\-]|\d\d)/gi;e.updateOffset=function(){};var li=/^(\-|\+)?(?:(\d*)[. ])?(\d+)\:(\d+)(?:\:(\d+)(\.\d*)?)?$/,di=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;we.fn=Me.prototype,we.invalid=function(){return we(NaN)};var hi=Ee(1,"add"),fi=Ee(-1,"subtract");e.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",e.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]";var mi=y("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",function(e){return void 0===e?this.localeData():this.locale(e)});P(0,["gg",2],0,function(){return this.weekYear()%100}),P(0,["GG",2],0,function(){return this.isoWeekYear()%100}),Ce("gggg","weekYear"),Ce("ggggg","weekYear"),Ce("GGGG","isoWeekYear"),Ce("GGGGG","isoWeekYear"),D("weekYear","gg"),D("isoWeekYear","GG"),Y("weekYear",1),Y("isoWeekYear",1),L("G",ga),L("g",ga),L("GG",ha,ca),L("gg",ha,ca),L("GGGG",_a,la),L("gggg",_a,la),L("GGGGG",pa,da),L("ggggg",pa,da),W(["gggg","ggggg","GGGG","GGGGG"],function(e,a,i,t){a[t.substr(0,2)]=p(e)}),W(["gg","GG"],function(a,i,t,r){i[r]=e.parseTwoDigitYear(a)}),P("Q",0,"Qo","quarter"),D("quarter","Q"),Y("quarter",7),L("Q",oa),G("Q",function(e,a){a[wa]=3*(p(e)-1)}),P("D",["DD",2],"Do","date"),D("date","D"),Y("date",9),L("D",ha),L("DD",ha,ca),L("Do",function(e,a){return e?a._dayOfMonthOrdinalParse||a._ordinalParse:a._dayOfMonthOrdinalParseLenient}),G(["D","DD"],va),G("Do",function(e,a){a[va]=p(e.match(ha)[0])});var Ai=R("Date",!0);P("DDD",["DDDD",3],"DDDo","dayOfYear"),D("dayOfYear","DDD"),Y("dayOfYear",4),L("DDD",Aa),L("DDDD",ua),G(["DDD","DDDD"],function(e,a,i){i._dayOfYear=p(e)}),P("m",["mm",2],0,"minute"),D("minute","m"),Y("minute",14),L("m",ha),L("mm",ha,ca),G(["m","mm"],Ea);var _i=R("Minutes",!1);P("s",["ss",2],0,"second"),D("second","s"),Y("second",15),L("s",ha),L("ss",ha,ca),G(["s","ss"],Pa);var pi,Mi=R("Seconds",!1);for(P("S",0,0,function(){return~~(this.millisecond()/100)}),P(0,["SS",2],0,function(){return~~(this.millisecond()/10)}),P(0,["SSS",3],0,"millisecond"),P(0,["SSSS",4],0,function(){return 10*this.millisecond()}),P(0,["SSSSS",5],0,function(){return 100*this.millisecond()}),P(0,["SSSSSS",6],0,function(){return 1e3*this.millisecond()}),P(0,["SSSSSSS",7],0,function(){return 1e4*this.millisecond()}),P(0,["SSSSSSSS",8],0,function(){return 1e5*this.millisecond()}),P(0,["SSSSSSSSS",9],0,function(){return 1e6*this.millisecond()}),D("millisecond","ms"),Y("millisecond",16),L("S",Aa,oa),L("SS",Aa,ca),L("SSS",Aa,ua),pi="SSSS";pi.length<=9;pi+="S")L(pi,Ma);for(pi="S";pi.length<=9;pi+="S")G(pi,Ge);var gi=R("Milliseconds",!1);P("z",0,0,"zoneAbbr"),P("zz",0,0,"zoneName");var yi=m.prototype;yi.add=hi,yi.calendar=function(a,i){var t=a||_e(),r=ke(t,this).startOf("day"),n=e.calendarFormat(this,r)||"sameElse",s=i&&(S(i[n])?i[n].call(this,t):i[n]);return this.format(s||this.localeData().calendar(n,this,_e(t)))},yi.clone=function(){return new m(this)},yi.diff=function(e,a,i){var t,r,n;if(!this.isValid())return NaN;if(!(t=ke(e,this)).isValid())return NaN;switch(r=6e4*(t.utcOffset()-this.utcOffset()),a=w(a)){case"year":n=ze(this,t)/12;break;case"month":n=ze(this,t);break;case"quarter":n=ze(this,t)/3;break;case"second":n=(this-t)/1e3;break;case"minute":n=(this-t)/6e4;break;case"hour":n=(this-t)/36e5;break;case"day":n=(this-t-r)/864e5;break;case"week":n=(this-t-r)/6048e5;break;default:n=this-t}return i?n:_(n)},yi.endOf=function(e){return void 0===(e=w(e))||"millisecond"===e?this:("date"===e&&(e="day"),this.startOf(e).add(1,"isoWeek"===e?"week":e).subtract(1,"ms"))},yi.format=function(a){a||(a=this.isUtc()?e.defaultFormatUtc:e.defaultFormat);var i=z(this,a);return this.localeData().postformat(i)},yi.from=function(e,a){return this.isValid()&&(A(e)&&e.isValid()||_e(e).isValid())?we({to:this,from:e
}).locale(this.locale()).humanize(!a):this.localeData().invalidDate()},yi.fromNow=function(e){return this.from(_e(),e)},yi.to=function(e,a){return this.isValid()&&(A(e)&&e.isValid()||_e(e).isValid())?we({from:this,to:e}).locale(this.locale()).humanize(!a):this.localeData().invalidDate()},yi.toNow=function(e){return this.to(_e(),e)},yi.get=function(e){return S(this[e=w(e)])?this[e]():this},yi.invalidAt=function(){return l(this).overflow},yi.isAfter=function(e,a){var i=A(e)?e:_e(e);return!(!this.isValid()||!i.isValid())&&("millisecond"===(a=w(t(a)?"millisecond":a))?this.valueOf()>i.valueOf():i.valueOf()<this.clone().startOf(a).valueOf())},yi.isBefore=function(e,a){var i=A(e)?e:_e(e);return!(!this.isValid()||!i.isValid())&&("millisecond"===(a=w(t(a)?"millisecond":a))?this.valueOf()<i.valueOf():this.clone().endOf(a).valueOf()<i.valueOf())},yi.isBetween=function(e,a,i,t){return("("===(t=t||"()")[0]?this.isAfter(e,i):!this.isBefore(e,i))&&(")"===t[1]?this.isBefore(a,i):!this.isAfter(a,i))},yi.isSame=function(e,a){var i,t=A(e)?e:_e(e);return!(!this.isValid()||!t.isValid())&&("millisecond"===(a=w(a||"millisecond"))?this.valueOf()===t.valueOf():(i=t.valueOf(),this.clone().startOf(a).valueOf()<=i&&i<=this.clone().endOf(a).valueOf()))},yi.isSameOrAfter=function(e,a){return this.isSame(e,a)||this.isAfter(e,a)},yi.isSameOrBefore=function(e,a){return this.isSame(e,a)||this.isBefore(e,a)},yi.isValid=function(){return d(this)},yi.lang=mi,yi.locale=Oe,yi.localeData=Le,yi.max=oi,yi.min=si,yi.parsingFlags=function(){return c({},l(this))},yi.set=function(e,a){if("object"==typeof e)for(var i=function(e){var a=[];for(var i in e)a.push({unit:i,priority:ia[i]});return a.sort(function(e,a){return e.priority-a.priority}),a}(e=v(e)),t=0;t<i.length;t++)this[i[t].unit](e[i[t].unit]);else if(S(this[e=w(e)]))return this[e](a);return this},yi.startOf=function(e){switch(e=w(e)){case"year":this.month(0);case"quarter":case"month":this.date(1);case"week":case"isoWeek":case"day":case"date":this.hours(0);case"hour":this.minutes(0);case"minute":this.seconds(0);case"second":this.milliseconds(0)}return"week"===e&&this.weekday(0),"isoWeek"===e&&this.isoWeekday(1),"quarter"===e&&this.month(3*Math.floor(this.month()/3)),this},yi.subtract=fi,yi.toArray=function(){var e=this;return[e.year(),e.month(),e.date(),e.hour(),e.minute(),e.second(),e.millisecond()]},yi.toObject=function(){var e=this;return{years:e.year(),months:e.month(),date:e.date(),hours:e.hours(),minutes:e.minutes(),seconds:e.seconds(),milliseconds:e.milliseconds()}},yi.toDate=function(){return new Date(this.valueOf())},yi.toISOString=function(e){if(!this.isValid())return null;var a=!0!==e,i=a?this.clone().utc():this;return i.year()<0||9999<i.year()?z(i,a?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"):S(Date.prototype.toISOString)?a?this.toDate().toISOString():new Date(this.valueOf()+60*this.utcOffset()*1e3).toISOString().replace("Z",z(i,"Z")):z(i,a?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ")},yi.inspect=function(){if(!this.isValid())return"moment.invalid(/* "+this._i+" */)";var e="moment",a="";this.isLocal()||(e=0===this.utcOffset()?"moment.utc":"moment.parseZone",a="Z");var i="["+e+'("]',t=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY",r=a+'[")]';return this.format(i+t+"-MM-DD[T]HH:mm:ss.SSS"+r)},yi.toJSON=function(){return this.isValid()?this.toISOString():null},yi.toString=function(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")},yi.unix=function(){return Math.floor(this.valueOf()/1e3)},yi.valueOf=function(){return this._d.valueOf()-6e4*(this._offset||0)},yi.creationData=function(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}},yi.year=Na,yi.isLeapYear=function(){return H(this.year())},yi.weekYear=function(e){return Ne.call(this,e,this.week(),this.weekday(),this.localeData()._week.dow,this.localeData()._week.doy)},yi.isoWeekYear=function(e){return Ne.call(this,e,this.isoWeek(),this.isoWeekday(),1,4)},yi.quarter=yi.quarters=function(e){return null==e?Math.ceil((this.month()+1)/3):this.month(3*(e-1)+this.month()%3)},yi.month=U,yi.daysInMonth=function(){return q(this.year(),this.month())},yi.week=yi.weeks=function(e){var a=this.localeData().week(this);return null==e?a:this.add(7*(e-a),"d")},yi.isoWeek=yi.isoWeeks=function(e){var a=X(this,1,4).week;return null==e?a:this.add(7*(e-a),"d")},yi.weeksInYear=function(){var e=this.localeData()._week;return J(this.year(),e.dow,e.doy)},yi.isoWeeksInYear=function(){return J(this.year(),1,4)},yi.date=Ai,yi.day=yi.days=function(e){if(!this.isValid())return null!=e?this:NaN;var a,i,t=this._isUTC?this._d.getUTCDay():this._d.getDay();return null!=e?(a=e,i=this.localeData(),e="string"!=typeof a?a:isNaN(a)?"number"==typeof(a=i.weekdaysParse(a))?a:null:parseInt(a,10),this.add(e-t,"d")):t},yi.weekday=function(e){if(!this.isValid())return null!=e?this:NaN;var a=(this.day()+7-this.localeData()._week.dow)%7;return null==e?a:this.add(e-a,"d")},yi.isoWeekday=function(e){if(!this.isValid())return null!=e?this:NaN;if(null!=e){var a=(i=e,t=this.localeData(),"string"==typeof i?t.weekdaysParse(i)%7||7:isNaN(i)?null:i);return this.day(this.day()%7?a:a-7)}return this.day()||7;var i,t},yi.dayOfYear=function(e){var a=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return null==e?a:this.add(e-a,"d")},yi.hour=yi.hours=Ka,yi.minute=yi.minutes=_i,yi.second=yi.seconds=Mi,yi.millisecond=yi.milliseconds=gi,yi.utcOffset=function(a,i,t){var r,n=this._offset||0;if(!this.isValid())return null!=a?this:NaN;if(null!=a){if("string"==typeof a){if(null===(a=Se(ba,a)))return this}else Math.abs(a)<16&&!t&&(a*=60);return!this._isUTC&&i&&(r=Te(this)),this._offset=a,this._isUTC=!0,null!=r&&this.add(r,"m"),n!==a&&(!i||this._changeInProgress?Pe(this,we(a-n,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,e.updateOffset(this,!0),this._changeInProgress=null)),this}return this._isUTC?n:Te(this)},yi.utc=function(e){return this.utcOffset(0,e)},yi.local=function(e){return this._isUTC&&(this.utcOffset(0,e),this._isUTC=!1,e&&this.subtract(Te(this),"m")),this},yi.parseZone=function(){if(null!=this._tzm)this.utcOffset(this._tzm,!1,!0);else if("string"==typeof this._i){var e=Se(ya,this._i);null!=e?this.utcOffset(e):this.utcOffset(0,!0)}return this},yi.hasAlignedHourOffset=function(e){return!!this.isValid()&&(e=e?_e(e).utcOffset():0,(this.utcOffset()-e)%60==0)},yi.isDST=function(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()},yi.isLocal=function(){return!!this.isValid()&&!this._isUTC},yi.isUtcOffset=function(){return!!this.isValid()&&this._isUTC},yi.isUtc=De,yi.isUTC=De,yi.zoneAbbr=function(){return this._isUTC?"UTC":""},yi.zoneName=function(){return this._isUTC?"Coordinated Universal Time":""},yi.dates=y("dates accessor is deprecated. Use date instead.",Ai),yi.months=y("months accessor is deprecated. Use month instead",U),yi.years=y("years accessor is deprecated. Use year instead",Na),yi.zone=y("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",function(e,a){return null!=e?("string"!=typeof e&&(e=-e),this.utcOffset(e,a),this):-this.utcOffset()}),yi.isDSTShifted=y("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",function(){if(!t(this._isDSTShifted))return this._isDSTShifted;var e={};if(f(e,this),(e=me(e))._a){var a=e._isUTC?u(e._a):_e(e._a);this._isDSTShifted=this.isValid()&&0<M(e._a,a.toArray())}else this._isDSTShifted=!1;return this._isDSTShifted});var bi=T.prototype;bi.calendar=function(e,a,i){var t=this._calendar[e]||this._calendar.sameElse;return S(t)?t.call(a,i):t},bi.longDateFormat=function(e){var a=this._longDateFormat[e],i=this._longDateFormat[e.toUpperCase()];return a||!i?a:(this._longDateFormat[e]=i.replace(/MMMM|MM|DD|dddd/g,function(e){return e.slice(1)}),this._longDateFormat[e])},bi.invalidDate=function(){return this._invalidDate},bi.ordinal=function(e){return this._ordinal.replace("%d",e)},bi.preparse=We,bi.postformat=We,bi.relativeTime=function(e,a,i,t){var r=this._relativeTime[i];return S(r)?r(e,a,i,t):r.replace(/%d/i,e)},bi.pastFuture=function(e,a){var i=this._relativeTime[0<e?"future":"past"];return S(i)?i(a):i.replace(/%s/i,a)},bi.set=function(e){var a,i;for(i in e)S(a=e[i])?this[i]=a:this["_"+i]=a;this._config=e,this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)},bi.months=function(e,i){return e?a(this._months)?this._months[e.month()]:this._months[(this._months.isFormat||Ga).test(i)?"format":"standalone"][e.month()]:a(this._months)?this._months:this._months.standalone},bi.monthsShort=function(e,i){return e?a(this._monthsShort)?this._monthsShort[e.month()]:this._monthsShort[Ga.test(i)?"format":"standalone"][e.month()]:a(this._monthsShort)?this._monthsShort:this._monthsShort.standalone},bi.monthsParse=function(e,a,i){var t,r,n;if(this._monthsParseExact)return function(e,a,i){var t,r,n,s=e.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],t=0;t<12;++t)n=u([2e3,t]),this._shortMonthsParse[t]=this.monthsShort(n,"").toLocaleLowerCase(),this._longMonthsParse[t]=this.months(n,"").toLocaleLowerCase();return i?"MMM"===a?-1!==(r=Ca.call(this._shortMonthsParse,s))?r:null:-1!==(r=Ca.call(this._longMonthsParse,s))?r:null:"MMM"===a?-1!==(r=Ca.call(this._shortMonthsParse,s))?r:-1!==(r=Ca.call(this._longMonthsParse,s))?r:null:-1!==(r=Ca.call(this._longMonthsParse,s))?r:-1!==(r=Ca.call(this._shortMonthsParse,s))?r:null}.call(this,e,a,i);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),t=0;t<12;t++){if(r=u([2e3,t]),i&&!this._longMonthsParse[t]&&(this._longMonthsParse[t]=new RegExp("^"+this.months(r,"").replace(".","")+"$","i"),this._shortMonthsParse[t]=new RegExp("^"+this.monthsShort(r,"").replace(".","")+"$","i")),i||this._monthsParse[t]||(n="^"+this.months(r,"")+"|^"+this.monthsShort(r,""),this._monthsParse[t]=new RegExp(n.replace(".",""),"i")),i&&"MMMM"===a&&this._longMonthsParse[t].test(e))return t;if(i&&"MMM"===a&&this._shortMonthsParse[t].test(e))return t;if(!i&&this._monthsParse[t].test(e))return t}},bi.monthsRegex=function(e){return this._monthsParseExact?(o(this,"_monthsRegex")||j.call(this),e?this._monthsStrictRegex:this._monthsRegex):(o(this,"_monthsRegex")||(this._monthsRegex=Ra),this._monthsStrictRegex&&e?this._monthsStrictRegex:this._monthsRegex)},bi.monthsShortRegex=function(e){return this._monthsParseExact?(o(this,"_monthsRegex")||j.call(this),e?this._monthsShortStrictRegex:this._monthsShortRegex):(o(this,"_monthsShortRegex")||(this._monthsShortRegex=Ha),this._monthsShortStrictRegex&&e?this._monthsShortStrictRegex:this._monthsShortRegex)},bi.week=function(e){return X(e,this._week.dow,this._week.doy).week},bi.firstDayOfYear=function(){return this._week.doy},bi.firstDayOfWeek=function(){return this._week.dow},bi.weekdays=function(e,i){return e?a(this._weekdays)?this._weekdays[e.day()]:this._weekdays[this._weekdays.isFormat.test(i)?"format":"standalone"][e.day()]:a(this._weekdays)?this._weekdays:this._weekdays.standalone},bi.weekdaysMin=function(e){return e?this._weekdaysMin[e.day()]:this._weekdaysMin},bi.weekdaysShort=function(e){return e?this._weekdaysShort[e.day()]:this._weekdaysShort},bi.weekdaysParse=function(e,a,i){var t,r,n;if(this._weekdaysParseExact)return function(e,a,i){var t,r,n,s=e.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],t=0;t<7;++t)n=u([2e3,1]).day(t),this._minWeekdaysParse[t]=this.weekdaysMin(n,"").toLocaleLowerCase(),this._shortWeekdaysParse[t]=this.weekdaysShort(n,"").toLocaleLowerCase(),this._weekdaysParse[t]=this.weekdays(n,"").toLocaleLowerCase();return i?"dddd"===a?-1!==(r=Ca.call(this._weekdaysParse,s))?r:null:"ddd"===a?-1!==(r=Ca.call(this._shortWeekdaysParse,s))?r:null:-1!==(r=Ca.call(this._minWeekdaysParse,s))?r:null:"dddd"===a?-1!==(r=Ca.call(this._weekdaysParse,s))?r:-1!==(r=Ca.call(this._shortWeekdaysParse,s))?r:-1!==(r=Ca.call(this._minWeekdaysParse,s))?r:null:"ddd"===a?-1!==(r=Ca.call(this._shortWeekdaysParse,s))?r:-1!==(r=Ca.call(this._weekdaysParse,s))?r:-1!==(r=Ca.call(this._minWeekdaysParse,s))?r:null:-1!==(r=Ca.call(this._minWeekdaysParse,s))?r:-1!==(r=Ca.call(this._weekdaysParse,s))?r:-1!==(r=Ca.call(this._shortWeekdaysParse,s))?r:null}.call(this,e,a,i);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),t=0;t<7;t++){if(r=u([2e3,1]).day(t),i&&!this._fullWeekdaysParse[t]&&(this._fullWeekdaysParse[t]=new RegExp("^"+this.weekdays(r,"").replace(".","\\.?")+"$","i"),this._shortWeekdaysParse[t]=new RegExp("^"+this.weekdaysShort(r,"").replace(".","\\.?")+"$","i"),this._minWeekdaysParse[t]=new RegExp("^"+this.weekdaysMin(r,"").replace(".","\\.?")+"$","i")),this._weekdaysParse[t]||(n="^"+this.weekdays(r,"")+"|^"+this.weekdaysShort(r,"")+"|^"+this.weekdaysMin(r,""),this._weekdaysParse[t]=new RegExp(n.replace(".",""),"i")),i&&"dddd"===a&&this._fullWeekdaysParse[t].test(e))return t;if(i&&"ddd"===a&&this._shortWeekdaysParse[t].test(e))return t;if(i&&"dd"===a&&this._minWeekdaysParse[t].test(e))return t;if(!i&&this._weekdaysParse[t].test(e))return t}},bi.weekdaysRegex=function(e){return this._weekdaysParseExact?(o(this,"_weekdaysRegex")||Q.call(this),e?this._weekdaysStrictRegex:this._weekdaysRegex):(o(this,"_weekdaysRegex")||(this._weekdaysRegex=Ia),this._weekdaysStrictRegex&&e?this._weekdaysStrictRegex:this._weekdaysRegex)},bi.weekdaysShortRegex=function(e){return this._weekdaysParseExact?(o(this,"_weekdaysRegex")||Q.call(this),e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(o(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=Ua),this._weekdaysShortStrictRegex&&e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)},bi.weekdaysMinRegex=function(e){return this._weekdaysParseExact?(o(this,"_weekdaysRegex")||Q.call(this),e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(o(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=ja),this._weekdaysMinStrictRegex&&e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)},bi.isPM=function(e){return"p"===(e+"").toLowerCase().charAt(0)},bi.meridiem=function(e,a,i){return 11<e?i?"pm":"PM":i?"am":"AM"},re("en",{dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(e){var a=e%10;return e+(1===p(e%100/10)?"th":1===a?"st":2===a?"nd":3===a?"rd":"th")}}),e.lang=y("moment.lang is deprecated. Use moment.locale instead.",re),e.langData=y("moment.langData is deprecated. Use moment.localeData instead.",se);var Si=Math.abs,ki=Ue("ms"),Ti=Ue("s"),Di=Ue("m"),wi=Ue("h"),vi=Ue("d"),Yi=Ue("w"),Ei=Ue("M"),Pi=Ue("y"),zi=je("milliseconds"),Oi=je("seconds"),Li=je("minutes"),Ci=je("hours"),Ni=je("days"),Gi=je("months"),Wi=je("years"),xi=Math.round,Hi={ss:44,s:45,m:45,h:22,d:26,M:11},Ri=Math.abs,Bi=Me.prototype;return Bi.isValid=function(){return this._isValid},Bi.abs=function(){var e=this._data;return this._milliseconds=Si(this._milliseconds),this._days=Si(this._days),this._months=Si(this._months),e.milliseconds=Si(e.milliseconds),e.seconds=Si(e.seconds),e.minutes=Si(e.minutes),e.hours=Si(e.hours),e.months=Si(e.months),e.years=Si(e.years),this},Bi.add=function(e,a){return Be(this,e,a,1)},Bi.subtract=function(e,a){return Be(this,e,a,-1)},Bi.as=function(e){if(!this.isValid())return NaN;var a,i,t=this._milliseconds;if("month"===(e=w(e))||"year"===e)return a=this._days+t/864e5,i=this._months+qe(a),"month"===e?i:i/12;switch(a=this._days+Math.round(Ie(this._months)),e){case"week":return a/7+t/6048e5;case"day":return a+t/864e5;case"hour":return 24*a+t/36e5;case"minute":return 1440*a+t/6e4;case"second":return 86400*a+t/1e3;case"millisecond":return Math.floor(864e5*a)+t;default:throw new Error("Unknown unit "+e)}},Bi.asMilliseconds=ki,Bi.asSeconds=Ti,Bi.asMinutes=Di,Bi.asHours=wi,Bi.asDays=vi,Bi.asWeeks=Yi,Bi.asMonths=Ei,Bi.asYears=Pi,Bi.valueOf=function(){return this.isValid()?this._milliseconds+864e5*this._days+this._months%12*2592e6+31536e6*p(this._months/12):NaN},Bi._bubble=function(){var e,a,i,t,r,n=this._milliseconds,s=this._days,o=this._months,c=this._data;return 0<=n&&0<=s&&0<=o||n<=0&&s<=0&&o<=0||(n+=864e5*Fe(Ie(o)+s),o=s=0),c.milliseconds=n%1e3,e=_(n/1e3),c.seconds=e%60,a=_(e/60),c.minutes=a%60,i=_(a/60),c.hours=i%24,o+=r=_(qe(s+=_(i/24))),s-=Fe(Ie(r)),t=_(o/12),o%=12,c.days=s,c.months=o,c.years=t,this},Bi.clone=function(){return we(this)},Bi.get=function(e){return e=w(e),this.isValid()?this[e+"s"]():NaN},Bi.milliseconds=zi,Bi.seconds=Oi,Bi.minutes=Li,Bi.hours=Ci,Bi.days=Ni,Bi.weeks=function(){return _(this.days()/7)},Bi.months=Gi,Bi.years=Wi,Bi.humanize=function(e){if(!this.isValid())return this.localeData().invalidDate();var a,i,t,r,n,s,o,c,u,l,d,h=this.localeData(),f=(i=!e,t=h,r=we(a=this).abs(),n=xi(r.as("s")),s=xi(r.as("m")),o=xi(r.as("h")),c=xi(r.as("d")),u=xi(r.as("M")),l=xi(r.as("y")),(d=n<=Hi.ss&&["s",n]||n<Hi.s&&["ss",n]||s<=1&&["m"]||s<Hi.m&&["mm",s]||o<=1&&["h"]||o<Hi.h&&["hh",o]||c<=1&&["d"]||c<Hi.d&&["dd",c]||u<=1&&["M"]||u<Hi.M&&["MM",u]||l<=1&&["y"]||["yy",l])[2]=i,d[3]=0<+a,d[4]=t,function(e,a,i,t,r){return r.relativeTime(a||1,!!i,e,t)}.apply(null,d));return e&&(f=h.pastFuture(+this,f)),h.postformat(f)},Bi.toISOString=Ke,Bi.toString=Ke,Bi.toJSON=Ke,Bi.locale=Oe,Bi.localeData=Le,Bi.toIsoString=y("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",Ke),Bi.lang=mi,P("X",0,0,"unix"),P("x",0,0,"valueOf"),L("x",ga),L("X",/[+-]?\d+(\.\d{1,3})?/),G("X",function(e,a,i){i._d=new Date(1e3*parseFloat(e,10))}),G("x",function(e,a,i){i._d=new Date(p(e))}),e.version="2.22.2",Ze=_e,e.fn=yi,e.min=function(){return pe("isBefore",[].slice.call(arguments,0))},e.max=function(){return pe("isAfter",[].slice.call(arguments,0))},e.now=function(){return Date.now?Date.now():+new Date},e.utc=u,e.unix=function(e){return _e(1e3*e)},e.months=function(e,a){return He(e,a,"months")},e.isDate=n,e.locale=re,e.invalid=h,e.duration=we,e.isMoment=A,e.weekdays=function(e,a,i){return Re(e,a,i,"weekdays")},e.parseZone=function(){return _e.apply(null,arguments).parseZone()},e.localeData=se,e.isDuration=ge,e.monthsShort=function(e,a){return He(e,a,"monthsShort")},e.weekdaysMin=function(e,a,i){return Re(e,a,i,"weekdaysMin")},e.defineLocale=ne,e.updateLocale=function(e,a){if(null!=a){var i,t,r=Za;null!=(t=te(e))&&(r=t._config),(i=new T(a=k(r,a))).parentLocale=Xa[e],Xa[e]=i,re(e)}else null!=Xa[e]&&(null!=Xa[e].parentLocale?Xa[e]=Xa[e].parentLocale:null!=Xa[e]&&delete Xa[e]);return Xa[e]},e.locales=function(){return Je(Xa)},e.weekdaysShort=function(e,a,i){return Re(e,a,i,"weekdaysShort")},e.normalizeUnits=w,e.relativeTimeRounding=function(e){return void 0===e?xi:"function"==typeof e&&(xi=e,!0)},e.relativeTimeThreshold=function(e,a){return void 0!==Hi[e]&&(void 0===a?Hi[e]:(Hi[e]=a,"s"===e&&(Hi.ss=a-1),!0))},e.calendarFormat=function(e,a){var i=e.diff(a,"days",!0);return i<-6?"sameElse":i<-1?"lastWeek":i<0?"lastDay":i<1?"sameDay":i<2?"nextDay":i<7?"nextWeek":"sameElse"},e.prototype=yi,e.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"YYYY-[W]WW",MONTH:"YYYY-MM"},e}),function(e,a){"object"==typeof exports&&"undefined"!=typeof module&&"function"==typeof require?a(require("../moment")):"function"==typeof define&&define.amd?define(["../moment"],a):a(e.moment)}(this,function(e){"use strict";return e.defineLocale("zh-cn",{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"周日_周一_周二_周三_周四_周五_周六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日Ah点mm分",LLLL:"YYYY年M月D日ddddAh点mm分",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},meridiemParse:/凌晨|早上|上午|中午|下午|晚上/,meridiemHour:function(e,a){return 12===e&&(e=0),"凌晨"===a||"早上"===a||"上午"===a?e:"下午"===a||"晚上"===a?e+12:e>=11?e:e+12},meridiem:function(e,a,i){var t=100*e+a;return t<600?"凌晨":t<900?"早上":t<1130?"上午":t<1230?"中午":t<1800?"下午":"晚上"},calendar:{sameDay:"[今天]LT",nextDay:"[明天]LT",nextWeek:"[下]ddddLT",lastDay:"[昨天]LT",lastWeek:"[上]ddddLT",sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}(日|月|周)/,ordinal:function(e,a){switch(a){case"d":case"D":case"DDD":return e+"日";case"M":return e+"月";case"w":case"W":return e+"周";default:return e}},relativeTime:{future:"%s内",past:"%s前",s:"几秒",ss:"%d 秒",m:"1 分钟",mm:"%d 分钟",h:"1 小时",hh:"%d 小时",d:"1 天",dd:"%d 天",M:"1 个月",MM:"%d 个月",y:"1 年",yy:"%d 年"},week:{dow:1,doy:4}})}),function(e,a){"object"==typeof exports&&"undefined"!=typeof module&&"function"==typeof require?a(require("../moment")):"function"==typeof define&&define.amd?define(["../moment"],a):a(e.moment)}(this,function(e){"use strict";return e.defineLocale("zh-tw",{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"週日_週一_週二_週三_週四_週五_週六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日 HH:mm",LLLL:"YYYY年M月D日dddd HH:mm",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},meridiemParse:/凌晨|早上|上午|中午|下午|晚上/,meridiemHour:function(e,a){return 12===e&&(e=0),"凌晨"===a||"早上"===a||"上午"===a?e:"中午"===a?e>=11?e:e+12:"下午"===a||"晚上"===a?e+12:void 0},meridiem:function(e,a,i){var t=100*e+a;return t<600?"凌晨":t<900?"早上":t<1130?"上午":t<1230?"中午":t<1800?"下午":"晚上"},calendar:{sameDay:"[今天] LT",nextDay:"[明天] LT",nextWeek:"[下]dddd LT",lastDay:"[昨天] LT",lastWeek:"[上]dddd LT",sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}(日|月|週)/,ordinal:function(e,a){switch(a){case"d":case"D":case"DDD":return e+"日";case"M":return e+"月";case"w":case"W":return e+"週";default:return e}},relativeTime:{future:"%s內",past:"%s前",s:"幾秒",ss:"%d 秒",m:"1 分鐘",mm:"%d 分鐘",h:"1 小時",hh:"%d 小時",d:"1 天",dd:"%d 天",M:"1 個月",MM:"%d 個月",y:"1 年",yy:"%d 年"}})}),function(e,a){"use strict";"function"==typeof define&&define.amd?define(["moment"],a):"object"==typeof module&&module.exports?module.exports=a(require("moment")):a(e.moment)}(this,function(e){"use strict";function a(e){return 96<e?e-87:64<e?e-29:e-48}function i(e){var i=0,t=e.split("."),r=t[0],n=t[1]||"",s=1,o=0,c=1;for(45===e.charCodeAt(0)&&(c=-(i=1));i<r.length;i++)o=60*o+a(r.charCodeAt(i));for(i=0;i<n.length;i++)s/=60,o+=a(n.charCodeAt(i))*s;return o*c}function t(e){for(var a=0;a<e.length;a++)e[a]=i(e[a])}function r(e,a){var i,t=[];for(i=0;i<a.length;i++)t[i]=e[a[i]];return t}function n(e){var a=e.split("|"),i=a[2].split(" "),n=a[3].split(""),s=a[4].split(" ");return t(i),t(n),t(s),function(e,a){for(var i=0;i<a;i++)e[i]=Math.round((e[i-1]||0)+6e4*e[i]);e[a-1]=1/0}(s,n.length),{name:a[0],abbrs:r(a[1].split(" "),n),offsets:r(i,n),untils:s,population:0|a[5]}}function s(e){e&&this._set(n(e))}function o(e){var a=e.toTimeString(),i=a.match(/\([a-z ]+\)/i);"GMT"===(i=i&&i[0]?(i=i[0].match(/[A-Z]/g))?i.join(""):void 0:(i=a.match(/[A-Z]{3,5}/g))?i[0]:void 0)&&(i=void 0),this.at=+e,this.abbr=i,this.offset=e.getTimezoneOffset()}function c(e){this.zone=e,this.offsetScore=0,this.abbrScore=0}function u(e,a){for(var i,t;t=6e4*((a.at-e.at)/12e4|0);)(i=new o(new Date(e.at+t))).offset===e.offset?e=i:a=i;return e}function l(e,a){return e.offsetScore!==a.offsetScore?e.offsetScore-a.offsetScore:e.abbrScore!==a.abbrScore?e.abbrScore-a.abbrScore:a.zone.population-e.zone.population}function d(e,a){var i,r;for(t(a),i=0;i<a.length;i++)r=a[i],w[r]=w[r]||{},w[r][e]=!0}function h(){try{var e=Intl.DateTimeFormat().resolvedOptions().timeZone;if(e&&3<e.length){var a=D[f(e)];if(a)return a;g("Moment Timezone found "+e+" from the Intl api, but did not have that data loaded.")}}catch(e){}var i,t,r,n=function(){var e,a,i,t=(new Date).getFullYear()-2,r=new o(new Date(t,0,1)),n=[r];for(i=1;i<48;i++)(a=new o(new Date(t,i,1))).offset!==r.offset&&(e=u(r,a),n.push(e),n.push(new o(new Date(e.at+6e4)))),r=a;for(i=0;i<4;i++)n.push(new o(new Date(t+i,0,1))),n.push(new o(new Date(t+i,6,1)));return n}(),s=n.length,d=function(e){var a,i,t,r=e.length,n={},s=[];for(a=0;a<r;a++)for(i in t=w[e[a].offset]||{})t.hasOwnProperty(i)&&(n[i]=!0);for(a in n)n.hasOwnProperty(a)&&s.push(D[a]);return s}(n),h=[];for(t=0;t<d.length;t++){for(i=new c(A(d[t]),s),r=0;r<s;r++)i.scoreOffsetAt(n[r]);h.push(i)}return h.sort(l),0<h.length?h[0].zone.name:void 0}function f(e){return(e||"").toLowerCase().replace(/\//g,"_")}function m(e){var a,i,t,r;for("string"==typeof e&&(e=[e]),a=0;a<e.length;a++)r=f(i=(t=e[a].split("|"))[0]),k[r]=e[a],D[r]=i,d(r,t[2].split(" "))}function A(e,a){e=f(e);var i,t=k[e];return t instanceof s?t:"string"==typeof t?(t=new s(t),k[e]=t):T[e]&&a!==A&&(i=A(T[e],A))?((t=k[e]=new s)._set(i),t.name=D[e],t):null}function _(e){var a,i,t,r;for("string"==typeof e&&(e=[e]),a=0;a<e.length;a++)t=f((i=e[a].split("|"))[0]),r=f(i[1]),T[t]=r,D[t]=i[0],T[r]=t,D[r]=i[1]}function p(e){m(e.zones),_(e.links),y.dataVersion=e.version}function M(e){var a="X"===e._f||"x"===e._f;return!(!e._a||void 0!==e._tzm||a)}function g(e){"undefined"!=typeof console&&"function"==typeof console.error&&console.error(e)}function y(a){var i=Array.prototype.slice.call(arguments,0,-1),t=arguments[arguments.length-1],r=A(t),n=e.utc.apply(null,i);return r&&!e.isMoment(a)&&M(n)&&n.add(r.parse(n),"minutes"),n.tz(t),n}function b(e){return function(){return this._z?this._z.abbr(this):e.call(this)}}var S,k={},T={},D={},w={},v=e.version.split("."),Y=+v[0],E=+v[1];(Y<2||2===Y&&E<6)&&g("Moment Timezone requires Moment.js >= 2.6.0. You are using Moment.js "+e.version+". See momentjs.com"),s.prototype={_set:function(e){this.name=e.name,this.abbrs=e.abbrs,this.untils=e.untils,this.offsets=e.offsets,this.population=e.population},_index:function(e){var a,i=+e,t=this.untils;for(a=0;a<t.length;a++)if(i<t[a])return a},parse:function(e){var a,i,t,r,n=+e,s=this.offsets,o=this.untils,c=o.length-1;for(r=0;r<c;r++)if(a=s[r],i=s[r+1],t=s[r?r-1:r],a<i&&y.moveAmbiguousForward?a=i:t<a&&y.moveInvalidForward&&(a=t),n<o[r]-6e4*a)return s[r];return s[c]},abbr:function(e){return this.abbrs[this._index(e)]},offset:function(e){return g("zone.offset has been deprecated in favor of zone.utcOffset"),this.offsets[this._index(e)]},utcOffset:function(e){return this.offsets[this._index(e)]}},c.prototype.scoreOffsetAt=function(e){this.offsetScore+=Math.abs(this.zone.utcOffset(e.at)-e.offset),this.zone.abbr(e.at).replace(/[^A-Z]/g,"")!==e.abbr&&this.abbrScore++},y.version="0.5.17",y.dataVersion="",y._zones=k,y._links=T,y._names=D,y.add=m,y.link=_,y.load=p,y.zone=A,y.zoneExists=function e(a){return e.didShowError||(e.didShowError=!0,g("moment.tz.zoneExists('"+a+"') has been deprecated in favor of !moment.tz.zone('"+a+"')")),!!A(a)},y.guess=function(e){return S&&!e||(S=h()),S},y.names=function(){var e,a=[];for(e in D)D.hasOwnProperty(e)&&(k[e]||k[T[e]])&&D[e]&&a.push(D[e]);return a.sort()},y.Zone=s,y.unpack=n,y.unpackBase60=i,y.needsOffset=M,y.moveInvalidForward=!0,y.moveAmbiguousForward=!1;var P,z=e.fn;e.tz=y,e.defaultZone=null,e.updateOffset=function(a,i){var t,r=e.defaultZone;void 0===a._z&&(r&&M(a)&&!a._isUTC&&(a._d=e.utc(a._a)._d,a.utc().add(r.parse(a),"minutes")),a._z=r),a._z&&(t=a._z.utcOffset(a),Math.abs(t)<16&&(t/=60),void 0!==a.utcOffset?a.utcOffset(-t,i):a.zone(t,i))},z.tz=function(a,i){return a?(this._z=A(a),this._z?e.updateOffset(this,i):g("Moment Timezone has no data for "+a+". See http://momentjs.com/timezone/docs/#/data-loading/."),this):this._z?this._z.name:void 0},z.zoneName=b(z.zoneName),z.zoneAbbr=b(z.zoneAbbr),z.utc=(P=z.utc,function(){return this._z=null,P.apply(this,arguments)}),e.tz.setDefault=function(a){return(Y<2||2===Y&&E<9)&&g("Moment Timezone setDefault() requires Moment.js >= 2.9.0. You are using Moment.js "+e.version+"."),e.defaultZone=a?A(a):null,e};var O=e.momentProperties;return"[object Array]"===Object.prototype.toString.call(O)?(O.push("_z"),O.push("_a")):O&&(O._z=null),p({version:"2018e",
zones:["Africa/Abidjan|GMT|0|0||48e5","Africa/Nairobi|EAT|-30|0||47e5","Africa/Algiers|CET|-10|0||26e5","Africa/Lagos|WAT|-10|0||17e6","Africa/Maputo|CAT|-20|0||26e5","Africa/Cairo|EET EEST|-20 -30|01010|1M2m0 gL0 e10 mn0|15e6","Africa/Casablanca|WET WEST|0 -10|0101010101010101010101010101010101010101010|1H3C0 wM0 co0 go0 1o00 s00 dA0 vc0 11A0 A00 e00 y00 11A0 uM0 e00 Dc0 11A0 s00 e00 IM0 WM0 mo0 gM0 LA0 WM0 jA0 e00 Rc0 11A0 e00 e00 U00 11A0 8o0 e00 11A0 11A0 5A0 e00 17c0 1fA0 1a00|32e5","Europe/Paris|CET CEST|-10 -20|01010101010101010101010|1GNB0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0|11e6","Africa/Johannesburg|SAST|-20|0||84e5","Africa/Khartoum|EAT CAT|-30 -20|01|1Usl0|51e5","Africa/Sao_Tome|GMT WAT|0 -10|01|1UQN0","Africa/Tripoli|EET CET CEST|-20 -10 -20|0120|1IlA0 TA0 1o00|11e5","Africa/Windhoek|CAT WAT|-20 -10|0101010101010|1GQo0 11B0 1qL0 WN0 1qL0 11B0 1nX0 11B0 1nX0 11B0 1nX0 11B0|32e4","America/Adak|HST HDT|a0 90|01010101010101010101010|1GIc0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0|326","America/Anchorage|AKST AKDT|90 80|01010101010101010101010|1GIb0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0|30e4","America/Santo_Domingo|AST|40|0||29e5","America/Araguaina|-03 -02|30 20|010|1IdD0 Lz0|14e4","America/Fortaleza|-03|30|0||34e5","America/Asuncion|-03 -04|30 40|01010101010101010101010|1GTf0 1cN0 17b0 1ip0 17b0 1ip0 17b0 1ip0 19X0 1fB0 19X0 1fB0 19X0 1ip0 17b0 1ip0 17b0 1ip0 19X0 1fB0 19X0 1fB0|28e5","America/Panama|EST|50|0||15e5","America/Mexico_City|CST CDT|60 50|01010101010101010101010|1GQw0 1nX0 14p0 1lb0 14p0 1lb0 14p0 1lb0 14p0 1nX0 11B0 1nX0 11B0 1nX0 14p0 1lb0 14p0 1lb0 14p0 1nX0 11B0 1nX0|20e6","America/Bahia|-02 -03|20 30|01|1GCq0|27e5","America/Managua|CST|60|0||22e5","America/La_Paz|-04|40|0||19e5","America/Lima|-05|50|0||11e6","America/Denver|MST MDT|70 60|01010101010101010101010|1GI90 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0|26e5","America/Campo_Grande|-03 -04|30 40|01010101010101010101010|1GCr0 1zd0 Lz0 1C10 Lz0 1C10 On0 1zd0 On0 1zd0 On0 1zd0 On0 1HB0 FX0 1HB0 FX0 1HB0 IL0 1HB0 FX0 1HB0|77e4","America/Cancun|CST CDT EST|60 50 50|01010102|1GQw0 1nX0 14p0 1lb0 14p0 1lb0 Dd0|63e4","America/Caracas|-0430 -04|4u 40|01|1QMT0|29e5","America/Chicago|CST CDT|60 50|01010101010101010101010|1GI80 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0|92e5","America/Chihuahua|MST MDT|70 60|01010101010101010101010|1GQx0 1nX0 14p0 1lb0 14p0 1lb0 14p0 1lb0 14p0 1nX0 11B0 1nX0 11B0 1nX0 14p0 1lb0 14p0 1lb0 14p0 1nX0 11B0 1nX0|81e4","America/Phoenix|MST|70|0||42e5","America/Los_Angeles|PST PDT|80 70|01010101010101010101010|1GIa0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0|15e6","America/New_York|EST EDT|50 40|01010101010101010101010|1GI70 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0|21e6","America/Rio_Branco|-04 -05|40 50|01|1KLE0|31e4","America/Fort_Nelson|PST PDT MST|80 70 70|01010102|1GIa0 1zb0 Op0 1zb0 Op0 1zb0 Op0|39e2","America/Halifax|AST ADT|40 30|01010101010101010101010|1GI60 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0|39e4","America/Godthab|-03 -02|30 20|01010101010101010101010|1GNB0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0|17e3","America/Grand_Turk|EST EDT AST|50 40 40|0101010121010101010|1GI70 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 5Ip0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0|37e2","America/Havana|CST CDT|50 40|01010101010101010101010|1GQt0 1qM0 Oo0 1zc0 Oo0 1zc0 Oo0 1zc0 Rc0 1zc0 Oo0 1zc0 Oo0 1zc0 Oo0 1zc0 Oo0 1zc0 Rc0 1zc0 Oo0 1zc0|21e5","America/Metlakatla|PST AKST AKDT|80 90 80|0121212121212121|1PAa0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0|14e2","America/Miquelon|-03 -02|30 20|01010101010101010101010|1GI50 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0|61e2","America/Montevideo|-02 -03|20 30|01010101|1GI40 1o10 11z0 1o10 11z0 1o10 11z0|17e5","America/Noronha|-02|20|0||30e2","America/Port-au-Prince|EST EDT|50 40|010101010101010101010|1GI70 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 3iN0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0|23e5","Antarctica/Palmer|-03 -04|30 40|010101010|1H3D0 Op0 1zb0 Rd0 1wn0 Rd0 46n0 Ap0|40","America/Santiago|-03 -04|30 40|010101010101010101010|1H3D0 Op0 1zb0 Rd0 1wn0 Rd0 46n0 Ap0 1Nb0 Ap0 1Nb0 Ap0 1Nb0 Ap0 1Nb0 Ap0 1Nb0 Dd0 1Nb0 Ap0|62e5","America/Sao_Paulo|-02 -03|20 30|01010101010101010101010|1GCq0 1zd0 Lz0 1C10 Lz0 1C10 On0 1zd0 On0 1zd0 On0 1zd0 On0 1HB0 FX0 1HB0 FX0 1HB0 IL0 1HB0 FX0 1HB0|20e6","Atlantic/Azores|-01 +00|10 0|01010101010101010101010|1GNB0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0|25e4","America/St_Johns|NST NDT|3u 2u|01010101010101010101010|1GI5u 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0|11e4","Antarctica/Casey|+11 +08|-b0 -80|0101|1GAF0 blz0 3m10|10","Antarctica/Davis|+05 +07|-50 -70|01|1GAI0|70","Pacific/Port_Moresby|+10|-a0|0||25e4","Pacific/Guadalcanal|+11|-b0|0||11e4","Asia/Tashkent|+05|-50|0||23e5","Pacific/Auckland|NZDT NZST|-d0 -c0|01010101010101010101010|1GQe0 1cM0 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1cM0 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1a00|14e5","Asia/Baghdad|+03|-30|0||66e5","Antarctica/Troll|+00 +02|0 -20|01010101010101010101010|1GNB0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0|40","Asia/Dhaka|+06|-60|0||16e6","Asia/Amman|EET EEST|-20 -30|010101010101010101010|1GPy0 4bX0 Dd0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 11A0 1o00|25e5","Asia/Kamchatka|+12|-c0|0||18e4","Asia/Baku|+04 +05|-40 -50|010101010|1GNA0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00|27e5","Asia/Bangkok|+07|-70|0||15e6","Asia/Barnaul|+07 +06|-70 -60|010|1N7v0 3rd0","Asia/Beirut|EET EEST|-20 -30|01010101010101010101010|1GNy0 1qL0 11B0 1nX0 11B0 1nX0 11B0 1nX0 11B0 1qL0 WN0 1qL0 WN0 1qL0 11B0 1nX0 11B0 1nX0 11B0 1qL0 WN0 1qL0|22e5","Asia/Manila|+08|-80|0||24e6","Asia/Kolkata|IST|-5u|0||15e6","Asia/Chita|+10 +08 +09|-a0 -80 -90|012|1N7s0 3re0|33e4","Asia/Ulaanbaatar|+08 +09|-80 -90|01010|1O8G0 1cJ0 1cP0 1cJ0|12e5","Asia/Shanghai|CST|-80|0||23e6","Asia/Colombo|+0530|-5u|0||22e5","Asia/Damascus|EET EEST|-20 -30|01010101010101010101010|1GPy0 1nX0 11B0 1nX0 11B0 1qL0 WN0 1qL0 WN0 1qL0 11B0 1nX0 11B0 1nX0 11B0 1nX0 11B0 1qL0 WN0 1qL0 WN0 1qL0|26e5","Asia/Dili|+09|-90|0||19e4","Asia/Dubai|+04|-40|0||39e5","Asia/Famagusta|EET EEST +03|-20 -30 -30|0101010101201010101010|1GNB0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 15U0 2Ks0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0","Asia/Gaza|EET EEST|-20 -30|01010101010101010101010|1GPy0 1a00 1fA0 1cL0 1cN0 1nX0 1210 1nz0 1220 1qL0 WN0 1qL0 WN0 1qL0 WN0 1qL0 11B0 1qL0 WN0 1qL0 WN0 1qL0|18e5","Asia/Hong_Kong|HKT|-80|0||73e5","Asia/Hovd|+07 +08|-70 -80|01010|1O8H0 1cJ0 1cP0 1cJ0|81e3","Asia/Irkutsk|+09 +08|-90 -80|01|1N7t0|60e4","Europe/Istanbul|EET EEST +03|-20 -30 -30|01010101012|1GNB0 1qM0 11A0 1o00 1200 1nA0 11A0 1tA0 U00 15w0|13e6","Asia/Jakarta|WIB|-70|0||31e6","Asia/Jayapura|WIT|-90|0||26e4","Asia/Jerusalem|IST IDT|-20 -30|01010101010101010101010|1GPA0 1aL0 1eN0 1oL0 10N0 1oL0 10N0 1oL0 10N0 1rz0 W10 1rz0 W10 1rz0 10N0 1oL0 10N0 1oL0 10N0 1rz0 W10 1rz0|81e4","Asia/Kabul|+0430|-4u|0||46e5","Asia/Karachi|PKT|-50|0||24e6","Asia/Kathmandu|+0545|-5J|0||12e5","Asia/Yakutsk|+10 +09|-a0 -90|01|1N7s0|28e4","Asia/Krasnoyarsk|+08 +07|-80 -70|01|1N7u0|10e5","Asia/Magadan|+12 +10 +11|-c0 -a0 -b0|012|1N7q0 3Cq0|95e3","Asia/Makassar|WITA|-80|0||15e5","Europe/Athens|EET EEST|-20 -30|01010101010101010101010|1GNB0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0|35e5","Asia/Novosibirsk|+07 +06|-70 -60|010|1N7v0 4eN0|15e5","Asia/Omsk|+07 +06|-70 -60|01|1N7v0|12e5","Asia/Pyongyang|KST KST|-90 -8u|010|1P4D0 6BAu|29e5","Asia/Rangoon|+0630|-6u|0||48e5","Asia/Sakhalin|+11 +10|-b0 -a0|010|1N7r0 3rd0|58e4","Asia/Seoul|KST|-90|0||23e6","Asia/Srednekolymsk|+12 +11|-c0 -b0|01|1N7q0|35e2","Asia/Tehran|+0330 +0430|-3u -4u|01010101010101010101010|1GLUu 1dz0 1cN0 1dz0 1cp0 1dz0 1cp0 1dz0 1cp0 1dz0 1cN0 1dz0 1cp0 1dz0 1cp0 1dz0 1cp0 1dz0 1cN0 1dz0 1cp0 1dz0|14e6","Asia/Tokyo|JST|-90|0||38e6","Asia/Tomsk|+07 +06|-70 -60|010|1N7v0 3Qp0|10e5","Asia/Vladivostok|+11 +10|-b0 -a0|01|1N7r0|60e4","Asia/Yekaterinburg|+06 +05|-60 -50|01|1N7w0|14e5","Europe/Lisbon|WET WEST|0 -10|01010101010101010101010|1GNB0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0|27e5","Atlantic/Cape_Verde|-01|10|0||50e4","Australia/Sydney|AEDT AEST|-b0 -a0|01010101010101010101010|1GQg0 1fA0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1fA0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0|40e5","Australia/Adelaide|ACDT ACST|-au -9u|01010101010101010101010|1GQgu 1fA0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1fA0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0|11e5","Australia/Brisbane|AEST|-a0|0||20e5","Australia/Darwin|ACST|-9u|0||12e4","Australia/Eucla|+0845|-8J|0||368","Australia/Lord_Howe|+11 +1030|-b0 -au|01010101010101010101010|1GQf0 1fAu 1cLu 1cMu 1cLu 1cMu 1cLu 1cMu 1cLu 1cMu 1cLu 1cMu 1cLu 1fAu 1cLu 1cMu 1cLu 1cMu 1cLu 1cMu 1cLu 1cMu|347","Australia/Perth|AWST|-80|0||18e5","Pacific/Easter|-05 -06|50 60|010101010101010101010|1H3D0 Op0 1zb0 Rd0 1wn0 Rd0 46n0 Ap0 1Nb0 Ap0 1Nb0 Ap0 1Nb0 Ap0 1Nb0 Ap0 1Nb0 Dd0 1Nb0 Ap0|30e2","Europe/Dublin|GMT IST|0 -10|01010101010101010101010|1GNB0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0|12e5","Etc/GMT-1|+01|-10|0|","Pacific/Fakaofo|+13|-d0|0||483","Pacific/Kiritimati|+14|-e0|0||51e2","Etc/GMT-2|+02|-20|0|","Pacific/Tahiti|-10|a0|0||18e4","Pacific/Niue|-11|b0|0||12e2","Etc/GMT+12|-12|c0|0|","Pacific/Galapagos|-06|60|0||25e3","Etc/GMT+7|-07|70|0|","Pacific/Pitcairn|-08|80|0||56","Pacific/Gambier|-09|90|0||125","Etc/UCT|UCT|0|0|","Etc/UTC|UTC|0|0|","Europe/Astrakhan|+04 +03|-40 -30|010|1N7y0 3rd0","Europe/London|GMT BST|0 -10|01010101010101010101010|1GNB0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0|10e6","Europe/Chisinau|EET EEST|-20 -30|01010101010101010101010|1GNA0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0|67e4","Europe/Kaliningrad|+03 EET|-30 -20|01|1N7z0|44e4","Europe/Volgograd|+04 +03|-40 -30|01|1N7y0|10e5","Europe/Moscow|MSK MSK|-40 -30|01|1N7y0|16e6","Europe/Saratov|+04 +03|-40 -30|010|1N7y0 5810","Europe/Simferopol|EET EEST MSK MSK|-20 -30 -40 -30|0101023|1GNB0 1qM0 11A0 1o00 11z0 1nW0|33e4","Pacific/Honolulu|HST|a0|0||37e4","MET|MET MEST|-10 -20|01010101010101010101010|1GNB0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0","Pacific/Chatham|+1345 +1245|-dJ -cJ|01010101010101010101010|1GQe0 1cM0 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1cM0 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1a00|600","Pacific/Apia|+14 +13|-e0 -d0|01010101010101010101010|1GQe0 1cM0 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1cM0 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1a00|37e3","Pacific/Bougainville|+10 +11|-a0 -b0|01|1NwE0|18e4","Pacific/Fiji|+13 +12|-d0 -c0|01010101010101010101010|1Goe0 1Nc0 Ao0 1Q00 xz0 1SN0 uM0 1SM0 uM0 1VA0 s00 1VA0 s00 1VA0 uM0 1SM0 uM0 1SM0 uM0 1VA0 s00 1VA0|88e4","Pacific/Guam|ChST|-a0|0||17e4","Pacific/Marquesas|-0930|9u|0||86e2","Pacific/Pago_Pago|SST|b0|0||37e2","Pacific/Norfolk|+1130 +11|-bu -b0|01|1PoCu|25e4","Pacific/Tongatapu|+13 +14|-d0 -e0|010|1S4d0 s00|75e3"],links:["Africa/Abidjan|Africa/Accra","Africa/Abidjan|Africa/Bamako","Africa/Abidjan|Africa/Banjul","Africa/Abidjan|Africa/Bissau","Africa/Abidjan|Africa/Conakry","Africa/Abidjan|Africa/Dakar","Africa/Abidjan|Africa/Freetown","Africa/Abidjan|Africa/Lome","Africa/Abidjan|Africa/Monrovia","Africa/Abidjan|Africa/Nouakchott","Africa/Abidjan|Africa/Ouagadougou","Africa/Abidjan|Africa/Timbuktu","Africa/Abidjan|America/Danmarkshavn","Africa/Abidjan|Atlantic/Reykjavik","Africa/Abidjan|Atlantic/St_Helena","Africa/Abidjan|Etc/GMT","Africa/Abidjan|Etc/GMT+0","Africa/Abidjan|Etc/GMT-0","Africa/Abidjan|Etc/GMT0","Africa/Abidjan|Etc/Greenwich","Africa/Abidjan|GMT","Africa/Abidjan|GMT+0","Africa/Abidjan|GMT-0","Africa/Abidjan|GMT0","Africa/Abidjan|Greenwich","Africa/Abidjan|Iceland","Africa/Algiers|Africa/Tunis","Africa/Cairo|Egypt","Africa/Casablanca|Africa/El_Aaiun","Africa/Johannesburg|Africa/Maseru","Africa/Johannesburg|Africa/Mbabane","Africa/Lagos|Africa/Bangui","Africa/Lagos|Africa/Brazzaville","Africa/Lagos|Africa/Douala","Africa/Lagos|Africa/Kinshasa","Africa/Lagos|Africa/Libreville","Africa/Lagos|Africa/Luanda","Africa/Lagos|Africa/Malabo","Africa/Lagos|Africa/Ndjamena","Africa/Lagos|Africa/Niamey","Africa/Lagos|Africa/Porto-Novo","Africa/Maputo|Africa/Blantyre","Africa/Maputo|Africa/Bujumbura","Africa/Maputo|Africa/Gaborone","Africa/Maputo|Africa/Harare","Africa/Maputo|Africa/Kigali","Africa/Maputo|Africa/Lubumbashi","Africa/Maputo|Africa/Lusaka","Africa/Nairobi|Africa/Addis_Ababa","Africa/Nairobi|Africa/Asmara","Africa/Nairobi|Africa/Asmera","Africa/Nairobi|Africa/Dar_es_Salaam","Africa/Nairobi|Africa/Djibouti","Africa/Nairobi|Africa/Juba","Africa/Nairobi|Africa/Kampala","Africa/Nairobi|Africa/Mogadishu","Africa/Nairobi|Indian/Antananarivo","Africa/Nairobi|Indian/Comoro","Africa/Nairobi|Indian/Mayotte","Africa/Tripoli|Libya","America/Adak|America/Atka","America/Adak|US/Aleutian","America/Anchorage|America/Juneau","America/Anchorage|America/Nome","America/Anchorage|America/Sitka","America/Anchorage|America/Yakutat","America/Anchorage|US/Alaska","America/Campo_Grande|America/Cuiaba","America/Chicago|America/Indiana/Knox","America/Chicago|America/Indiana/Tell_City","America/Chicago|America/Knox_IN","America/Chicago|America/Matamoros","America/Chicago|America/Menominee","America/Chicago|America/North_Dakota/Beulah","America/Chicago|America/North_Dakota/Center","America/Chicago|America/North_Dakota/New_Salem","America/Chicago|America/Rainy_River","America/Chicago|America/Rankin_Inlet","America/Chicago|America/Resolute","America/Chicago|America/Winnipeg","America/Chicago|CST6CDT","America/Chicago|Canada/Central","America/Chicago|US/Central","America/Chicago|US/Indiana-Starke","America/Chihuahua|America/Mazatlan","America/Chihuahua|Mexico/BajaSur","America/Denver|America/Boise","America/Denver|America/Cambridge_Bay","America/Denver|America/Edmonton","America/Denver|America/Inuvik","America/Denver|America/Ojinaga","America/Denver|America/Shiprock","America/Denver|America/Yellowknife","America/Denver|Canada/Mountain","America/Denver|MST7MDT","America/Denver|Navajo","America/Denver|US/Mountain","America/Fortaleza|America/Argentina/Buenos_Aires","America/Fortaleza|America/Argentina/Catamarca","America/Fortaleza|America/Argentina/ComodRivadavia","America/Fortaleza|America/Argentina/Cordoba","America/Fortaleza|America/Argentina/Jujuy","America/Fortaleza|America/Argentina/La_Rioja","America/Fortaleza|America/Argentina/Mendoza","America/Fortaleza|America/Argentina/Rio_Gallegos","America/Fortaleza|America/Argentina/Salta","America/Fortaleza|America/Argentina/San_Juan","America/Fortaleza|America/Argentina/San_Luis","America/Fortaleza|America/Argentina/Tucuman","America/Fortaleza|America/Argentina/Ushuaia","America/Fortaleza|America/Belem","America/Fortaleza|America/Buenos_Aires","America/Fortaleza|America/Catamarca","America/Fortaleza|America/Cayenne","America/Fortaleza|America/Cordoba","America/Fortaleza|America/Jujuy","America/Fortaleza|America/Maceio","America/Fortaleza|America/Mendoza","America/Fortaleza|America/Paramaribo","America/Fortaleza|America/Recife","America/Fortaleza|America/Rosario","America/Fortaleza|America/Santarem","America/Fortaleza|Antarctica/Rothera","America/Fortaleza|Atlantic/Stanley","America/Fortaleza|Etc/GMT+3","America/Halifax|America/Glace_Bay","America/Halifax|America/Goose_Bay","America/Halifax|America/Moncton","America/Halifax|America/Thule","America/Halifax|Atlantic/Bermuda","America/Halifax|Canada/Atlantic","America/Havana|Cuba","America/La_Paz|America/Boa_Vista","America/La_Paz|America/Guyana","America/La_Paz|America/Manaus","America/La_Paz|America/Porto_Velho","America/La_Paz|Brazil/West","America/La_Paz|Etc/GMT+4","America/Lima|America/Bogota","America/Lima|America/Guayaquil","America/Lima|Etc/GMT+5","America/Los_Angeles|America/Dawson","America/Los_Angeles|America/Ensenada","America/Los_Angeles|America/Santa_Isabel","America/Los_Angeles|America/Tijuana","America/Los_Angeles|America/Vancouver","America/Los_Angeles|America/Whitehorse","America/Los_Angeles|Canada/Pacific","America/Los_Angeles|Canada/Yukon","America/Los_Angeles|Mexico/BajaNorte","America/Los_Angeles|PST8PDT","America/Los_Angeles|US/Pacific","America/Los_Angeles|US/Pacific-New","America/Managua|America/Belize","America/Managua|America/Costa_Rica","America/Managua|America/El_Salvador","America/Managua|America/Guatemala","America/Managua|America/Regina","America/Managua|America/Swift_Current","America/Managua|America/Tegucigalpa","America/Managua|Canada/Saskatchewan","America/Mexico_City|America/Bahia_Banderas","America/Mexico_City|America/Merida","America/Mexico_City|America/Monterrey","America/Mexico_City|Mexico/General","America/New_York|America/Detroit","America/New_York|America/Fort_Wayne","America/New_York|America/Indiana/Indianapolis","America/New_York|America/Indiana/Marengo","America/New_York|America/Indiana/Petersburg","America/New_York|America/Indiana/Vevay","America/New_York|America/Indiana/Vincennes","America/New_York|America/Indiana/Winamac","America/New_York|America/Indianapolis","America/New_York|America/Iqaluit","America/New_York|America/Kentucky/Louisville","America/New_York|America/Kentucky/Monticello","America/New_York|America/Louisville","America/New_York|America/Montreal","America/New_York|America/Nassau","America/New_York|America/Nipigon","America/New_York|America/Pangnirtung","America/New_York|America/Thunder_Bay","America/New_York|America/Toronto","America/New_York|Canada/Eastern","America/New_York|EST5EDT","America/New_York|US/East-Indiana","America/New_York|US/Eastern","America/New_York|US/Michigan","America/Noronha|Atlantic/South_Georgia","America/Noronha|Brazil/DeNoronha","America/Noronha|Etc/GMT+2","America/Panama|America/Atikokan","America/Panama|America/Cayman","America/Panama|America/Coral_Harbour","America/Panama|America/Jamaica","America/Panama|EST","America/Panama|Jamaica","America/Phoenix|America/Creston","America/Phoenix|America/Dawson_Creek","America/Phoenix|America/Hermosillo","America/Phoenix|MST","America/Phoenix|US/Arizona","America/Rio_Branco|America/Eirunepe","America/Rio_Branco|America/Porto_Acre","America/Rio_Branco|Brazil/Acre","America/Santiago|Chile/Continental","America/Santo_Domingo|America/Anguilla","America/Santo_Domingo|America/Antigua","America/Santo_Domingo|America/Aruba","America/Santo_Domingo|America/Barbados","America/Santo_Domingo|America/Blanc-Sablon","America/Santo_Domingo|America/Curacao","America/Santo_Domingo|America/Dominica","America/Santo_Domingo|America/Grenada","America/Santo_Domingo|America/Guadeloupe","America/Santo_Domingo|America/Kralendijk","America/Santo_Domingo|America/Lower_Princes","America/Santo_Domingo|America/Marigot","America/Santo_Domingo|America/Martinique","America/Santo_Domingo|America/Montserrat","America/Santo_Domingo|America/Port_of_Spain","America/Santo_Domingo|America/Puerto_Rico","America/Santo_Domingo|America/St_Barthelemy","America/Santo_Domingo|America/St_Kitts","America/Santo_Domingo|America/St_Lucia","America/Santo_Domingo|America/St_Thomas","America/Santo_Domingo|America/St_Vincent","America/Santo_Domingo|America/Tortola","America/Santo_Domingo|America/Virgin","America/Sao_Paulo|Brazil/East","America/St_Johns|Canada/Newfoundland","Antarctica/Palmer|America/Punta_Arenas","Asia/Baghdad|Antarctica/Syowa","Asia/Baghdad|Asia/Aden","Asia/Baghdad|Asia/Bahrain","Asia/Baghdad|Asia/Kuwait","Asia/Baghdad|Asia/Qatar","Asia/Baghdad|Asia/Riyadh","Asia/Baghdad|Etc/GMT-3","Asia/Baghdad|Europe/Minsk","Asia/Bangkok|Asia/Ho_Chi_Minh","Asia/Bangkok|Asia/Novokuznetsk","Asia/Bangkok|Asia/Phnom_Penh","Asia/Bangkok|Asia/Saigon","Asia/Bangkok|Asia/Vientiane","Asia/Bangkok|Etc/GMT-7","Asia/Bangkok|Indian/Christmas","Asia/Dhaka|Antarctica/Vostok","Asia/Dhaka|Asia/Almaty","Asia/Dhaka|Asia/Bishkek","Asia/Dhaka|Asia/Dacca","Asia/Dhaka|Asia/Kashgar","Asia/Dhaka|Asia/Qyzylorda","Asia/Dhaka|Asia/Thimbu","Asia/Dhaka|Asia/Thimphu","Asia/Dhaka|Asia/Urumqi","Asia/Dhaka|Etc/GMT-6","Asia/Dhaka|Indian/Chagos","Asia/Dili|Etc/GMT-9","Asia/Dili|Pacific/Palau","Asia/Dubai|Asia/Muscat","Asia/Dubai|Asia/Tbilisi","Asia/Dubai|Asia/Yerevan","Asia/Dubai|Etc/GMT-4","Asia/Dubai|Europe/Samara","Asia/Dubai|Indian/Mahe","Asia/Dubai|Indian/Mauritius","Asia/Dubai|Indian/Reunion","Asia/Gaza|Asia/Hebron","Asia/Hong_Kong|Hongkong","Asia/Jakarta|Asia/Pontianak","Asia/Jerusalem|Asia/Tel_Aviv","Asia/Jerusalem|Israel","Asia/Kamchatka|Asia/Anadyr","Asia/Kamchatka|Etc/GMT-12","Asia/Kamchatka|Kwajalein","Asia/Kamchatka|Pacific/Funafuti","Asia/Kamchatka|Pacific/Kwajalein","Asia/Kamchatka|Pacific/Majuro","Asia/Kamchatka|Pacific/Nauru","Asia/Kamchatka|Pacific/Tarawa","Asia/Kamchatka|Pacific/Wake","Asia/Kamchatka|Pacific/Wallis","Asia/Kathmandu|Asia/Katmandu","Asia/Kolkata|Asia/Calcutta","Asia/Makassar|Asia/Ujung_Pandang","Asia/Manila|Asia/Brunei","Asia/Manila|Asia/Kuala_Lumpur","Asia/Manila|Asia/Kuching","Asia/Manila|Asia/Singapore","Asia/Manila|Etc/GMT-8","Asia/Manila|Singapore","Asia/Rangoon|Asia/Yangon","Asia/Rangoon|Indian/Cocos","Asia/Seoul|ROK","Asia/Shanghai|Asia/Chongqing","Asia/Shanghai|Asia/Chungking","Asia/Shanghai|Asia/Harbin","Asia/Shanghai|Asia/Macao","Asia/Shanghai|Asia/Macau","Asia/Shanghai|Asia/Taipei","Asia/Shanghai|PRC","Asia/Shanghai|ROC","Asia/Tashkent|Antarctica/Mawson","Asia/Tashkent|Asia/Aqtau","Asia/Tashkent|Asia/Aqtobe","Asia/Tashkent|Asia/Ashgabat","Asia/Tashkent|Asia/Ashkhabad","Asia/Tashkent|Asia/Atyrau","Asia/Tashkent|Asia/Dushanbe","Asia/Tashkent|Asia/Oral","Asia/Tashkent|Asia/Samarkand","Asia/Tashkent|Etc/GMT-5","Asia/Tashkent|Indian/Kerguelen","Asia/Tashkent|Indian/Maldives","Asia/Tehran|Iran","Asia/Tokyo|Japan","Asia/Ulaanbaatar|Asia/Choibalsan","Asia/Ulaanbaatar|Asia/Ulan_Bator","Asia/Vladivostok|Asia/Ust-Nera","Asia/Yakutsk|Asia/Khandyga","Atlantic/Azores|America/Scoresbysund","Atlantic/Cape_Verde|Etc/GMT+1","Australia/Adelaide|Australia/Broken_Hill","Australia/Adelaide|Australia/South","Australia/Adelaide|Australia/Yancowinna","Australia/Brisbane|Australia/Lindeman","Australia/Brisbane|Australia/Queensland","Australia/Darwin|Australia/North","Australia/Lord_Howe|Australia/LHI","Australia/Perth|Australia/West","Australia/Sydney|Australia/ACT","Australia/Sydney|Australia/Canberra","Australia/Sydney|Australia/Currie","Australia/Sydney|Australia/Hobart","Australia/Sydney|Australia/Melbourne","Australia/Sydney|Australia/NSW","Australia/Sydney|Australia/Tasmania","Australia/Sydney|Australia/Victoria","Etc/UCT|UCT","Etc/UTC|Etc/Universal","Etc/UTC|Etc/Zulu","Etc/UTC|UTC","Etc/UTC|Universal","Etc/UTC|Zulu","Europe/Astrakhan|Europe/Ulyanovsk","Europe/Athens|Asia/Nicosia","Europe/Athens|EET","Europe/Athens|Europe/Bucharest","Europe/Athens|Europe/Helsinki","Europe/Athens|Europe/Kiev","Europe/Athens|Europe/Mariehamn","Europe/Athens|Europe/Nicosia","Europe/Athens|Europe/Riga","Europe/Athens|Europe/Sofia","Europe/Athens|Europe/Tallinn","Europe/Athens|Europe/Uzhgorod","Europe/Athens|Europe/Vilnius","Europe/Athens|Europe/Zaporozhye","Europe/Chisinau|Europe/Tiraspol","Europe/Dublin|Eire","Europe/Istanbul|Asia/Istanbul","Europe/Istanbul|Turkey","Europe/Lisbon|Atlantic/Canary","Europe/Lisbon|Atlantic/Faeroe","Europe/Lisbon|Atlantic/Faroe","Europe/Lisbon|Atlantic/Madeira","Europe/Lisbon|Portugal","Europe/Lisbon|WET","Europe/London|Europe/Belfast","Europe/London|Europe/Guernsey","Europe/London|Europe/Isle_of_Man","Europe/London|Europe/Jersey","Europe/London|GB","Europe/London|GB-Eire","Europe/Moscow|W-SU","Europe/Paris|Africa/Ceuta","Europe/Paris|Arctic/Longyearbyen","Europe/Paris|Atlantic/Jan_Mayen","Europe/Paris|CET","Europe/Paris|Europe/Amsterdam","Europe/Paris|Europe/Andorra","Europe/Paris|Europe/Belgrade","Europe/Paris|Europe/Berlin","Europe/Paris|Europe/Bratislava","Europe/Paris|Europe/Brussels","Europe/Paris|Europe/Budapest","Europe/Paris|Europe/Busingen","Europe/Paris|Europe/Copenhagen","Europe/Paris|Europe/Gibraltar","Europe/Paris|Europe/Ljubljana","Europe/Paris|Europe/Luxembourg","Europe/Paris|Europe/Madrid","Europe/Paris|Europe/Malta","Europe/Paris|Europe/Monaco","Europe/Paris|Europe/Oslo","Europe/Paris|Europe/Podgorica","Europe/Paris|Europe/Prague","Europe/Paris|Europe/Rome","Europe/Paris|Europe/San_Marino","Europe/Paris|Europe/Sarajevo","Europe/Paris|Europe/Skopje","Europe/Paris|Europe/Stockholm","Europe/Paris|Europe/Tirane","Europe/Paris|Europe/Vaduz","Europe/Paris|Europe/Vatican","Europe/Paris|Europe/Vienna","Europe/Paris|Europe/Warsaw","Europe/Paris|Europe/Zagreb","Europe/Paris|Europe/Zurich","Europe/Paris|Poland","Europe/Volgograd|Europe/Kirov","Pacific/Auckland|Antarctica/McMurdo","Pacific/Auckland|Antarctica/South_Pole","Pacific/Auckland|NZ","Pacific/Chatham|NZ-CHAT","Pacific/Easter|Chile/EasterIsland","Pacific/Fakaofo|Etc/GMT-13","Pacific/Fakaofo|Pacific/Enderbury","Pacific/Galapagos|Etc/GMT+6","Pacific/Gambier|Etc/GMT+9","Pacific/Guadalcanal|Antarctica/Macquarie","Pacific/Guadalcanal|Etc/GMT-11","Pacific/Guadalcanal|Pacific/Efate","Pacific/Guadalcanal|Pacific/Kosrae","Pacific/Guadalcanal|Pacific/Noumea","Pacific/Guadalcanal|Pacific/Pohnpei","Pacific/Guadalcanal|Pacific/Ponape","Pacific/Guam|Pacific/Saipan","Pacific/Honolulu|HST","Pacific/Honolulu|Pacific/Johnston","Pacific/Honolulu|US/Hawaii","Pacific/Kiritimati|Etc/GMT-14","Pacific/Niue|Etc/GMT+11","Pacific/Pago_Pago|Pacific/Midway","Pacific/Pago_Pago|Pacific/Samoa","Pacific/Pago_Pago|US/Samoa","Pacific/Pitcairn|Etc/GMT+8","Pacific/Port_Moresby|Antarctica/DumontDUrville","Pacific/Port_Moresby|Etc/GMT-10","Pacific/Port_Moresby|Pacific/Chuuk","Pacific/Port_Moresby|Pacific/Truk","Pacific/Port_Moresby|Pacific/Yap","Pacific/Tahiti|Etc/GMT+10","Pacific/Tahiti|Pacific/Rarotonga"]}),e});