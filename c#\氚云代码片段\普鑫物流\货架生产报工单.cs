
using System;
using System.Collections.Generic;
using System.Text;
using H3;
using H3.DataModel;
using System.Data;
public class D2826052468b3ca3f39406a80a5ee6055d35ec1: H3.SmartForm.SmartFormController
{
    private DPuXinReport report;
    public D2826052468b3ca3f39406a80a5ee6055d35ec1(H3.SmartForm.SmartFormRequest request): base(request)
    {
    }

    protected override void OnLoad(H3.SmartForm.LoadSmartFormResponse response)
    {
        base.OnLoad(response);
    }

    protected override void OnSubmit(string actionName, H3.SmartForm.SmartFormPostValue postValue, H3.SmartForm.SubmitSmartFormResponse response)
    {

        this.report = new DPuXinReport(this.Engine, this.Request.UserContext.UserId);
        DPuXinError error = this.report.Validate(this.Request.BizObject);
        if(error.HasError())
        {
            response.ReturnData = BuildResp(error);
            return;
        }

        base.OnSubmit(actionName, postValue, response);
    }

    protected override void OnWorkflowInstanceStateChanged(H3.Workflow.Instance.WorkflowInstanceState oldState, H3.Workflow.Instance.WorkflowInstanceState newState)
    {

        //流程审批结束,业务规则执行。还有个审批流程结束（finished状态下直接删除），就直接删除也会触发业务规则执行。在submit里面处理
        if(oldState == H3.Workflow.Instance.WorkflowInstanceState.Running && newState == H3.Workflow.Instance.WorkflowInstanceState.Finished)
        {
            //获取销售订单
            string reportId = this.Request.BizObjectId;
            this.report.WriteToDetail(reportId);
        }

        base.OnWorkflowInstanceStateChanged(oldState, newState);
    }

    public Dictionary < string, object > BuildResp(DPuXinError xinError)
    {
        Dictionary < string, object > resp = new Dictionary<string, object>();
        if(!string.IsNullOrEmpty(xinError.GetError()))
        {
            resp.Add("Error", xinError.GetError());
        }
        if(xinError.GetErrors() != null && xinError.GetErrors().Count > 0)
        {
            resp.Add("Errors", this.Serialize(xinError.GetErrors()));
        }
        return resp;
    }
}


public class DPuXinReport
{
    private string reportSchema = "D2826052468b3ca3f39406a80a5ee6055d35ec1";
    private string reportChildSchema = "D282605Fd8a58da6e52d4efe942395b6048e06f0";
    private string reportDetailSchema = "D282605swgcbfrfszeetpkvlahrz";
    private string planSchema = "D282605Sbces9w53iq957t4gc8vulc6y2"; // 主计划
    private string planChildSchema = "D282605F80a2dc5e16c74b21813e7f1b5ad3615b"; // 主计划child
    private string processSheetSchemaCode = "D282605Sz5g9cj2b7tj1pr4ax1ao7prg4"; // 工艺单
    private string inventorySchemaCode = "D282605Swtn30nwzlozmh2upo2qsnvmp0"; // k库存
    private string inventoryChildSchemaCode = "D282605F76c8c2580d3244c4b8fe0774cde6f917";
    private string materialSchemaCode = "D282605suten5clu70qwo9gkhtlp"; // 物料
    private Dictionary<string, BizObject> inventoryDict = new Dictionary<string, BizObject>(); // 库存表
    private List<BizObject> processSheet = new List<BizObject>(); // 工艺单表
    private IEngine Engine;
    private string UserId;

    private DPuXinCalcPrice calcPrice;


    public DPuXinReport(IEngine Engine, string UserId)
    {
        this.Engine = Engine;
        this.UserId = UserId;
        this.calcPrice = new DPuXinCalcPrice(Engine, UserId);
    }


    public DPuXinError Validate(BizObject obj)
    {
        DPuXinError error = new DPuXinError();
        try
        {
            BizObject[] children = (BizObject[]) obj[this.reportChildSchema];
            if(children == null || children.Length == 0)
            {
                throw new Exception("请填写报工明细数据");
            }
            int index = 1;
            foreach(BizObject child in children)
            {
                child["Index"] = index;
                SyncPlan(child); // 同步计划数据
                index ++;
            }
            string[] orders = FilterOrders(children);
            DataRowCollection haveApply = this.QueryHaveApply(orders);
            List < BizObject > tree = new List<BizObject>(children);
            List < BizObject > sheets = this.GetProcessSheet(orders);
            while(tree.Count > 0)
            {
                BizObject root = tree[0]; // 取叶子节点， 再根据叶节点工序排序优先入库
                BizObject node = this.SearchLeaf(tree, root);
                tree.Remove(node);
                string orderNo = node["OrderNo"] + string.Empty;
                string materialNo = node["MaterialNo"] + string.Empty;
                string materialName = node["MaterialName"] + string.Empty;
                int waiting = this.ToInt(node["DeclaredQuantity"]);
                try
                {
                    if(waiting <= 0)
                    {
                        throw new Exception("没有申报数量");
                    }
                    if(string.IsNullOrEmpty(node["ReportPlan"] + string.Empty))
                    {
                        throw new Exception("没有对应的生产计划");
                    }
                    string process = node["Process"] + string.Empty;
                    DPuXinProcessCard card = new DPuXinProcessCard(node["ProcessLine"] + string.Empty);

                    if(!card.HasProcess(process))
                    {
                        throw new Exception(string.Format("当前物料不存在工序[{0}]", process));
                    }
                    int haveApplyCount = this.FindCountOfHaveApply(haveApply, node);
                    int count = this.ToInt(node["Count"]);
                    if(waiting + haveApplyCount > count)
                    {
                        throw new Exception("超过可以提交的订单数量：" + count);
                    }
                    BizObject sheet = Find(sheets, orderNo, materialNo);
                    if(sheet == null)
                    {
                        throw new Exception("工艺单不存在该物料");
                    }

                    if(card.IsFirst(process))
                    {
                        // 从工艺单中，取对应的子集，计划中是合并数据取不到关系了
                        List < BizObject > sub = FindChildInSheet(sheets, orderNo, sheet["SerialNo"] + string.Empty);

                        if(sub.Count > 0)
                        {
                            for(int i = 0;i < sub.Count; i++)
                            {
                                BizObject item = sub[i];
                                string processFlow = item["ProcessFlow"] + string.Empty;
                                string[] processItems = processFlow.Split(';');
                                if(processItems.Length == 0)
                                {
                                    throw new Exception("当前工艺单不存在工序");
                                }
                                string last = processItems[processItems.Length - 1];
                                BizObject inventory = this.GetProcessInvetory(item["MaterialNo"] + string.Empty, last, false);
                                int stock = 0;
                                string errorMsg = "子物料[{0}_{1}_{2}]库存不足,当前库存：{3}";
                                if(inventory == null)
                                {
                                    throw new Exception(String.Format(errorMsg, Convert.ToString(item["OrderNo"]),
                                        Convert.ToString(item["MaterialName"]), last, stock));
                                }
                                stock = this.ToInt(inventory["Quantity"]); // 当前库存
                                int subCount = this.ToInt(item["Count"]);

                                if(stock < (waiting * Math.Round((decimal) subCount / count, 1)))
                                {
                                    throw new Exception(String.Format(errorMsg + ",占比：{4}", Convert.ToString(item["OrderNo"]),
                                        Convert.ToString(item["MaterialName"]), last, stock, count + ":" + subCount));
                                }
                            }
                            for(int i = 0;i < sub.Count; i++)
                            {
                                BizObject item = sub[i];
                                string processFlow = item["ProcessFlow"] + string.Empty;
                                string[] processItems = processFlow.Split(';');
                                string last = processItems[processItems.Length - 1];
                                BizObject inventory = this.GetProcessInvetory(item["MaterialNo"] + string.Empty, last, false);

                                int subCount = this.ToInt(item["Count"]);

                                inventory["Quantity"] = this.ToInt(inventory["Quantity"]) - Math.Ceiling(waiting * Math.Round((decimal) subCount / count, 1));
                            }
                        }
                        BizObject cur = this.GetProcessInvetory(materialNo, process, true);
                        cur["Quantity"] = this.ToInt(cur["Quantity"]) + waiting; // 当前物料加库存
                    }
                    else
                    {
                        string beforeProcess = card.Before(process);
                        BizObject before = this.GetProcessInvetory(materialNo, beforeProcess, false);
                        if(before != null)
                        {
                            int beforeStock = this.ToInt(before["Quantity"]);
                            BizObject cur = this.GetProcessInvetory(materialNo, process, true);
                            int diff = beforeStock - waiting;
                            if(diff >= 0)
                            {
                                before["Quantity"] = beforeStock - waiting; // 上个物料减库存
                                cur["Quantity"] = this.ToInt(cur["Quantity"]) + waiting; // 当前物料加库存
                            }
                            else
                            {
                                throw new Exception(String.Format("超过上个工序[{0}]物料库存, 库存：{1}", beforeProcess, beforeStock));
                            }
                        }
                        else
                        {
                            throw new Exception(String.Format("超过上个工序[{0}]物料库存, 库存：0", beforeProcess));
                        }
                    }
                }
                catch(Exception ex)
                {
                    error["Index"] = node["Index"] + string.Empty;
                    error["OrderNo"] = orderNo;
                    error["MaterialName"] = materialName;
                    error["Quantity"] = waiting + string.Empty;
                    error["Error"] = ex.Message;
                    error.PutError();
                }

            }

        }
        catch(Exception ex)
        {
            error.Error(ex.Message);
        }

        return error;
    }


    public void WriteToDetail(string reportId)
    {
        BizObject report = Load(this.reportSchema, reportId);
        if(report == null)
        {
            return;
        }
        BizObject[] children = (BizObject[]) report[this.reportChildSchema];
        if(children == null || children.Length == 0)
        {
            return;
        }

        foreach(BizObject child in children)
        {
            BizObject detail = SaveToDetail(child, report);
            UpdatePlan(detail, false);
        }
        UpdateInventory();
    }

    // 更新计划
    public void UpdatePlan(BizObject detail, bool reverse)
    {
        BizObject plan = Load(this.planSchema, detail["ReportPlan"] + string.Empty);
        if(plan == null)
        {
            return;
        }
        string process = detail["Process"] + string.Empty;
        string groupName = detail["FlowProcess"] + string.Empty;
        if(string.IsNullOrEmpty(groupName))
        {
            return;
        }

        DPuXinProcessCard card = new DPuXinProcessCard(plan["ProcessLine"] + string.Empty);
        DPuXinProcessGroup group = card.Group(process);
        if(group.Last() != process)
        {
            return;
        }

        BizObject[] progress = (BizObject[]) plan[this.planChildSchema];
        List < BizObject > progressList = new List<BizObject>();
        if(progress != null && progress.Length > 0) progressList = new List<BizObject>(progress);
        DateTime date = Convert.ToDateTime(plan["DeliveryDate"]);
        BizObject obj = FindByKey(progressList, "ProcessName", groupName);
        if(obj == null)
        {
            BizObjectSchema schema = this.Engine.BizObjectManager.GetPublishedSchema(this.planSchema);
            obj = new BizObject(this.Engine,
                schema.GetChildSchema(this.planChildSchema), this.UserId); // 半成品库存
            obj["Finished"] = this.ToInt(detail["DeclaredQuantity"]) * (reverse ? -1 : 1); //完成数
            obj["ProcessName"] = groupName;
            int diff = group.DayDiff();
            obj["PlanDate"] = date.AddDays(diff);
            progressList.Add(obj);
        }
        else
        {
            obj["Finished"] = this.ToInt(obj["Finished"]) + this.ToInt(detail["DeclaredQuantity"]) * (reverse ? -1 : 1);
        }
        int finished = this.ToInt(obj["Finished"]);
        int total = this.ToInt(plan["Count"]);
        obj["Remain"] = total - finished;
        obj["Progress"] = Math.Round((decimal) finished / total, 2);
        obj["ActualDate"] = finished == total ? (object) System.DateTime.Today: null;
        plan[this.planChildSchema] = progressList.ToArray();
        DPuXinQuasiState.ConfirmState(plan);

        plan.Update();
    }

    // 更新库存
    public void UpdateInventory()
    {
        foreach(BizObject item in this.inventoryDict.Values)
        {
            item.Update();
        }
    }
    public void Rollback(string[] reportIds)
    {
        H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();  //根据物资编码查询库存
        H3.Data.Filter.And andMatcher = new H3.Data.Filter.And();    //构造And匹配器
        andMatcher.Add(new H3.Data.Filter.ItemMatcher("ObjectId", H3.Data.ComparisonOperatorType.In, reportIds));
        filter.Matcher = andMatcher;
        BizObjectSchema schema = this.Engine.BizObjectManager.GetPublishedSchema(this.reportSchema);
        BizObject[] reports = BizObject.GetList(this.Engine, this.UserId,
            schema, GetListScopeType.GlobalAll, filter);
        if(reports != null && reports.Length > 0)
        {
            foreach(BizObject report in reports)
            {
                BizObject[] details = (BizObject[]) report[this.reportChildSchema];
                foreach(BizObject detail in details)
                {
                    BizObject reportDetail = Load(this.reportDetailSchema, detail["ObjectId"] + string.Empty);
                    if(reportDetail != null)
                    {
                        UpdatePlan(reportDetail, true);
                        reportDetail.Remove();
                    }
                }
                report.Remove();
            }
            UpdateInventory();
        }
    }

    public DPuXinError RollbackValidate(BizObject obj)
    {
        DPuXinError error = new DPuXinError();
        try
        {
            BizObject[] children = (BizObject[]) obj[this.reportChildSchema];
            string[] orders = FilterOrders(children);
            List < BizObject > tree = new List<BizObject>(children);
            List < BizObject > sheets = this.GetProcessSheet(orders);
            while(tree.Count > 0)
            {
                BizObject root = tree[0]; // 取叶子节点， 再根据叶节点工序排序优先入库
                BizObject node = this.SearchRoot(tree, root);
                tree.Remove(node);
                string orderNo = node["OrderNo"] + string.Empty;
                string materialNo = node["MaterialNo"] + string.Empty;
                string materialName = node["MaterialName"] + string.Empty;
                int waiting = this.ToInt(node["DeclaredQuantity"]);
                try
                {
                    string process = node["Process"] + string.Empty;
                    DPuXinProcessCard card = new DPuXinProcessCard(node["ProcessLine"] + string.Empty);

                    if(!card.HasProcess(process))
                    {
                        throw new Exception(string.Format("当前物料不存在工序 [{0}]", process));
                    }
                    BizObject sheet = Find(sheets, orderNo, materialNo);
                    if(sheet == null)
                    {
                        throw new Exception("工艺单不存在该物料");
                    }
                    if(card.IsFirst(process))
                    {
                        BizObject cur = this.GetProcessInvetory(materialNo, process, false);
                        if(cur == null)
                        {
                            throw new Exception(String.Format("工序[{0}]物料库存不存在", process));
                        }
                        int curStock = this.ToInt(cur["Quantity"]);
                        if(curStock - waiting < 0)
                        {
                            throw new Exception(String.Format("工序[{0}]物料库存不足不能回滚, 库存：{1}", process, curStock));
                        }
                        cur["Quantity"] = curStock - waiting; // 当前物料加库存
                        int count = this.ToInt(sheet["Count"]);
                        // 从工艺单中，取对应的子集，计划中是合并数据取不到关系了
                        List < BizObject > sub = FindChildInSheet(sheets, orderNo, sheet["SerialNo"] + string.Empty);
                        if(sub.Count > 0)
                        {
                            for(int i = 0;i < sub.Count; i++)
                            {
                                BizObject item = sub[i];
                                string processFlow = item["ProcessFlow"] + string.Empty;
                                string[] processItems = processFlow.Split(';');
                                string last = processItems[processItems.Length - 1];
                                BizObject inventory = this.GetProcessInvetory(item["MaterialNo"] + string.Empty, last, true);
                                int subCount = this.ToInt(item["Count"]);
                                inventory["Quantity"] = this.ToInt(inventory["Quantity"]) + Math.Ceiling(waiting * Math.Round((decimal) subCount / count, 1));
                            }
                        }
                    }
                    else
                    {
                        string beforeProcess = card.Before(process);
                        BizObject before = this.GetProcessInvetory(materialNo, beforeProcess, true);

                        BizObject cur = this.GetProcessInvetory(materialNo, process, false);
                        if(cur == null)
                        {
                            throw new Exception(String.Format("工序[{0}]物料库存不存在", process));
                        }
                        int curStock = this.ToInt(cur["Quantity"]);
                        int diff = curStock - waiting;
                        if(diff >= 0)
                        {
                            before["Quantity"] = this.ToInt(before["Quantity"]) + waiting; // 上个物料减库存
                            cur["Quantity"] = curStock - waiting; // 当前物料加库存
                        }
                        else
                        {
                            throw new Exception(String.Format("工序[{0}]物料库存不足不能回滚, 库存：{1}", process, curStock));
                        }
                    }
                }
                catch(Exception ex)
                {
                    error["OrderNo"] = orderNo;
                    error["MaterialName"] = materialName;
                    error["Quantity"] = waiting + string.Empty;
                    error["Error"] = ex.Message;
                    error.PutError();
                }
            }
        }
        catch(Exception ex)
        {
            error.Error(ex.Message);
        }

        return error;
    }
    private BizObject SearchRoot(List < BizObject > tree, BizObject node)
    {
        string orderNo = node["OrderNo"] + string.Empty;
        string parentMaterialNo = node["ParentMaterialNo"] + string.Empty;

        BizObject current = null;
        if(!string.IsNullOrEmpty(parentMaterialNo))
        {
            current = Find(tree, orderNo, parentMaterialNo);
        }
        if(current == null)
        {
            string process = node["Process"] + string.Empty;
            string[] processItems = (node["ProcessLine"] + string.Empty).Substring(1).Split('/');
            Array.Reverse(processItems);
            foreach(string processItem in processItems)
            {
                if(processItem == process)
                {
                    return node;
                }
                else
                {
                    BizObject before = FindByProcess(tree, orderNo, node["MaterialNo"] + string.Empty, processItem);
                    if(before == null)
                    {
                        continue;
                    }
                    return before;
                }
            }
            return node;
        }
        return this.SearchRoot(tree, current);
    }

    public BizObject Load(string schema, string id)
    {
        return BizObject.Load(this.UserId,
            this.Engine, schema, id, false);
    }

    private BizObject SaveToDetail(BizObject obj, BizObject root)
    {
        string[] excludes = { "ParentObjectId", "State" };
        Dictionary < string, object > table = obj.GetValueTable();

        BizObject detail = Load(this.reportDetailSchema, obj["ObjectId"] + string.Empty);
        bool isCreate = false;
        if(detail == null)
        {
            BizObjectSchema detailSchema = this.Engine.BizObjectManager.GetPublishedSchema(this.reportDetailSchema);
            detail = new BizObject(this.Engine, detailSchema, this.UserId);
            detail.Status = BizObjectStatus.Effective; //设置状态生效
            isCreate = true;
        }
        string team = Convert.ToString(root["Team"]);
        detail["Team"] = team;
        detail["ReportDate"] = root["ReportDate"];

        foreach(KeyValuePair < string, object > pair in table)
        {
            string key = pair.Key;
            if(key.Contains("."))
            {
                int index = key.IndexOf(".");
                if(index <= key.Length)
                {
                    key = key.Substring(key.IndexOf(".") + 1);
                }
            }
            object value = pair.Value;
            if(InArray(excludes, key))
            {
                continue;
            }

            detail[key] = value;
        }
        detail["FlowProcess"] = ConvertProcess(detail["ProcessLine"] + string.Empty, detail["Process"] + string.Empty);
        this.calcPrice.CalcPrice(detail); // 计算价格

        if(isCreate)
        {
            detail.Create();
        }
        else
        {
            detail.Update();
        }
        return detail;
    }
    private bool InArray(string[] array, string key)
    {
        foreach(string item in array)
        {
            if(item == key)
            {
                return true;
            }
        }
        return false;
    }
    private string ConvertProcess(string processLine, string process)
    {
        DPuXinProcessCard card = new DPuXinProcessCard(processLine);
        DPuXinProcessGroup group = card.Group(process);
        if(group == null || !group.IsValid())  // 不在组中的移除
        {
            return null;
        }
        return group.GroupName();
    }

    private string[] FilterOrders(BizObject[] bizObjects)
    {
        List < string > orders = new List<string>();

        foreach(BizObject obj in bizObjects)
        {
            string orderNo = obj["OrderNo"] + string.Empty;
            if(!orders.Contains(orderNo))
            {
                orders.Add(orderNo);
            }
        }
        return orders.ToArray();
    }
    private BizObject SearchLeaf(List < BizObject > tree, BizObject root)
    {
        string orderNo = root["OrderNo"] + string.Empty;
        string materialNo = root["MaterialNo"] + string.Empty;

        List < BizObject > child = FindByParentInSheet(this.processSheet, orderNo, materialNo);
        BizObject node = null;
        foreach(BizObject childItem in child)
        {
            node = Find(tree, orderNo, childItem["MaterialNo"] + string.Empty);
            if(node != null)
            {
                break;
            }
        }
        if(node == null)
        {
            string process = root["Process"] + string.Empty;
            string procesLine = root["ProcessLine"] + string.Empty;
            string[] processItems = procesLine.Substring(1).Split('/');
            foreach(string processItem in processItems)
            {
                if(processItem == process)
                {
                    return root;
                }
                else
                {
                    BizObject before = FindByProcess(tree, orderNo, materialNo, processItem);
                    if(before == null)
                    {
                        continue;
                    }
                    return before;
                }
            }
            return root;
        };

        return this.SearchLeaf(tree, node);
    }

    private BizObject GetProcessInvetory(string materialNo, string process, bool isNew)
    {
        BizObjectSchema schema = this.Engine.BizObjectManager.GetPublishedSchema(this.inventorySchemaCode);
        BizObject inventory = this.QueryInventory(materialNo); // 取库存
        BizObject[] balance = (BizObject[]) inventory[this.inventoryChildSchemaCode];
        List < BizObject > list = new List<BizObject>();
        if(balance != null && balance.Length > 0)
        {
            list = new List<BizObject>(balance);
        }

        BizObject cur = FindByProcess(list, process);
        if(cur == null && isNew)
        {
            cur = new BizObject(this.Engine,
                schema.GetChildSchema(this.inventoryChildSchemaCode), this.UserId); // 半成品库存
            cur["Process"] = process;
            list.Add(cur);
            inventory[this.inventoryChildSchemaCode] = list.ToArray(); // 重新设置子集
        }
        return cur;
    }

    private BizObject QueryInventory(string materialNo)
    {
        if(inventoryDict.ContainsKey(materialNo))
        {
            return inventoryDict[materialNo];
        }
        H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();  //根据物资编码查询库存
        filter.Matcher = new H3.Data.Filter.ItemMatcher("MaterialNo", H3.Data.ComparisonOperatorType.Equal, materialNo);
        BizObjectSchema schema = this.Engine.BizObjectManager.GetPublishedSchema(this.inventorySchemaCode);
        BizObject[] stocks = BizObject.GetList(this.Engine, this.UserId,
            schema, GetListScopeType.GlobalAll, filter);
        if(stocks == null || stocks.Length == 0)
        {
            // 库存为空，新生成一条库存数据
            BizObject stock = new BizObject(this.Engine, schema, this.UserId);
            DataRow material = this.GetMaterialId(materialNo);
            stock["MaterialInfo"] = material["ObjectId"];
            stock["MaterialNo"] = material["MaterialNo"];
            stock["MaterialName"] = material["MaterialName"];
            stock["MaterialModel"] = material["MaterialModel"];
            stock.Status = BizObjectStatus.Effective; //设置状态生效
            stock.Create();
            inventoryDict.Add(materialNo, stock);
            return stock;
        }
        inventoryDict.Add(materialNo, stocks[0]);
        return stocks[0];
    }
    private List<BizObject> GetProcessSheet(string[] orderNos)
    {
        if(this.processSheet.Count > 0)
        {
            return this.processSheet;
        }
        H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();  //根据物资编码查询库存
        H3.Data.Filter.And andMatcher = new H3.Data.Filter.And();    //构造And匹配器
        andMatcher.Add(new H3.Data.Filter.ItemMatcher("OrderNo", H3.Data.ComparisonOperatorType.In, orderNos));
        filter.Matcher = andMatcher;
        BizObjectSchema schema = this.Engine.BizObjectManager.GetPublishedSchema(this.processSheetSchemaCode);
        BizObject[] items = BizObject.GetList(this.Engine, this.UserId,
            schema, GetListScopeType.GlobalAll, filter);
        List < BizObject > sheet = new List<BizObject>(items);
        this.processSheet = sheet;
        return sheet;
    }

    private DataRow GetMaterialId(string materialNo)
    {
        string sql = String.Format("select ObjectId, MaterialNo, MaterialName, MaterialModel from i_{0} where MaterialNo = '{1}'", this.materialSchemaCode, materialNo);
        DataTable table = this.Engine.Query.QueryTable(sql, null);

        if(table != null && table.Rows.Count > 0)
        {
            return table.Rows[0];
        }
        return null;
    }

    private int FindCountOfHaveApply(DataRowCollection rows, BizObject obj)
    {
        if(rows == null)
        {
            return 0;
        }
        foreach(DataRow row in rows)
        {
            if(Convert.ToString(row["OrderNo"]) == Convert.ToString(obj["OrderNo"])
                && Convert.ToString(row["MaterialNo"]) == Convert.ToString(obj["MaterialNo"])
                && Convert.ToString(row["Process"]) == Convert.ToString(obj["Process"]))
            {
                return this.ToInt(row["DeclaredQuantity"]);
            }
        }
        return 0;
    }
    private DataRowCollection QueryHaveApply(string[] orderNos)
    {
        if(orderNos == null || orderNos.Length == 0)
        {
            return null;
        }
        string query = "";
        foreach(string orderNo in orderNos)
        {
            query = query + string.Format("'{0}',", orderNo);
        }
        query = query.Substring(0, query.Length - 1);
        string sql = String.Format("select p.OrderNo OrderNo,p.MaterialNo MaterialNo, p.MaterialName MaterialName,p.MaterialModel MaterialModel,p.Process Process, sum(p.DeclaredQuantity) DeclaredQuantity from i_{0} p " +
            "where p.OrderNo in ({1}) group by p.OrderNo, p.MaterialNo,p.Process", this.reportDetailSchema, query);

        DataTable dt = this.Engine.Query.QueryTable(sql, null);
        if(dt == null || dt.Rows == null || dt.Rows.Count == 0)
        {
            return null;
        }

        return dt.Rows;
    }

    private void SyncPlan(BizObject detail)
    {
        string reportPlan = detail["ReportPlan"] + string.Empty;
        BizObject plan = null;
        if(string.IsNullOrEmpty(reportPlan))
        {
            H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();
            H3.Data.Filter.And andMatcher = new H3.Data.Filter.And();
            andMatcher.Add(new H3.Data.Filter.ItemMatcher("OrderNo", H3.Data.ComparisonOperatorType.Equal, detail["OrderNo"] + string.Empty));
            andMatcher.Add(new H3.Data.Filter.ItemMatcher("MaterialName", H3.Data.ComparisonOperatorType.Equal, detail["MaterialName"] + string.Empty));
            filter.Matcher = andMatcher;
            BizObjectSchema schema = this.Engine.BizObjectManager.GetPublishedSchema(this.planSchema);
            BizObject[] plans = BizObject.GetList(this.Engine, this.UserId,
                schema, GetListScopeType.GlobalAll, filter);
            if(plans != null && plans.Length > 0)
            {
                plan = plans[0];
            }
        }
        else
        {
            plan = Load(this.planSchema, reportPlan);
        }

        if(plan == null)
        {
            throw new Exception("当前报工没有对应的计划");
        }
        string[] waiting = { "OrderNo", "MaterialNo", "MaterialName", "Customer",
        "ProjectName", "MaterialModel", "Length", "Width", "Height", "Count",
        "Weight", "ProcessLine", "ParentMaterialNo","SprayingArea" };
        foreach(string key in waiting)
        {
            detail[key] = plan[key];
        }
        detail["ReportPlan"] = plan["ObjectId"];
    }
    private BizObject Find(List < BizObject > list, string orderNo, string materialNo)
    {
        foreach(BizObject obj in list)
        {

            if(Convert.ToString(obj["OrderNo"]) == orderNo && Convert.ToString(obj["MaterialNo"]) == materialNo)
            {
                return obj;
            }
        }
        return null;
    }
    private List<BizObject> FindChildInSheet(List<BizObject>sheets, string orderNo, string serialNo)
    {
        List < BizObject > list = new List<BizObject>();
        foreach(BizObject obj in sheets)
        {

            if(Convert.ToString(obj["ParentSerialNo"]) == serialNo && Convert.ToString(obj["OrderNo"]) == orderNo)
            {
                list.Add(obj);
            }
        }

        return list;
    }
    private List<BizObject> FindByParentInSheet(List<BizObject>sheets, string orderNo, string materialNo)
    {
        List < BizObject > list = new List<BizObject>();
        foreach(BizObject obj in sheets)
        {

            if(Convert.ToString(obj["ParentMaterialNo"]) == materialNo && Convert.ToString(obj["OrderNo"]) == orderNo)
            {
                list.Add(obj);
            }
        }

        return list;
    }
    private BizObject FindByProcess(List < BizObject > list, string orderNo, string materialNo, string process)
    {
        foreach(BizObject obj in list)
        {

            if(Convert.ToString(obj["OrderNo"]) == orderNo && Convert.ToString(obj["MaterialNo"]) == materialNo
                && Convert.ToString(obj["Process"]) == process)
            {
                return obj;
            }
        }
        return null;
    }
    private BizObject FindByProcess(List < BizObject > list, string process)
    {
        foreach(BizObject obj in list)
        {
            if(Convert.ToString(obj["Process"]) == process)
            {
                return obj;
            }
        }
        return null;
    }

    private BizObject FindByKey(List < BizObject > list, string key, string value)
    {
        foreach(BizObject obj in list)
        {
            if(Convert.ToString(obj[key]) == value)
            {
                return obj;
            }
        }
        return null;
    }

    private int ToInt(object obj)
    {
        return Convert.ToInt32(String.IsNullOrEmpty(obj + "") ? "0" + obj : obj);
    }

}