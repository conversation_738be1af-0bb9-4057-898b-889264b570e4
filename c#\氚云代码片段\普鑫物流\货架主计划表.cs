
using System;
using System.Collections.Generic;
using System.Text;
using H3;
using H3.DataModel;
using System.Data;

public class D282605Sbces9w53iq957t4gc8vulc6y2: H3.SmartForm.SmartFormController
{
    public D282605Sbces9w53iq957t4gc8vulc6y2(H3.SmartForm.SmartFormRequest request): base(request)
    {
    }

    protected override void OnLoad(H3.SmartForm.LoadSmartFormResponse response)
    {
        base.OnLoad(response);
    }

    protected override void OnSubmit(string actionName, H3.SmartForm.SmartFormPostValue postValue, H3.SmartForm.SubmitSmartFormResponse response)
    {
        base.OnSubmit(actionName, postValue, response);
    }
}

public class DPuXinProcessGroup
{
    protected int sort;
    protected string name;

    protected int dayDiff;

    protected string[] processGroup;
    protected bool last;

    protected List<string> realGroup = new List<string>();

    public bool InGroup(string process)
    {
        if(string.IsNullOrEmpty(process)) return false;
        foreach(string item in processGroup)
        {
            if(item == process) return true;
        }
        return false;
    }
    public bool HasProcess(string process)
    {
        if(string.IsNullOrEmpty(process)) return false;
        foreach(string item in realGroup)
        {
            if(item == process) return true;
        }
        return false;
    }
    public bool IsValid()
    {
        return realGroup.Count > 0;
    }
    public void Put(string process)
    {
        if(!InGroup(process)) return;
        if(realGroup.Contains(process)) return;
        realGroup.Add(process);
    }
    public void PutAll(string[] process)
    {
        foreach(string item in process)
        {
            Put(item);
        }
    }

    public List<string> Process()
    {
        return realGroup;
    }
    public string Last()
    {
        if(realGroup.Count == 0) return null;
        return realGroup[realGroup.Count - 1];
    }

    public bool IsLast()
    {
        return last;
    }

    public string GroupName()
    {
        return name;
    }
    public int DayDiff()
    {
        return dayDiff;
    }
    private static List<DPuXinProcessGroup> groups = CreateGroups();
    public static List<DPuXinProcessGroup> CreateGroups()
    {
        List < DPuXinProcessGroup > groups = new List<DPuXinProcessGroup>();
        groups.Add(new DPuXinBlankingProcessGroup());
        groups.Add(new DPuXinSheetMetalProcessGroup());
        groups.Add(new DPuXinRollingProcessGroup());
        groups.Add(new DPuXinWeldingProcessGroup());
        groups.Add(new DPuXinPackProcessGroup());

        groups.Sort(delegate(DPuXinProcessGroup a, DPuXinProcessGroup b)
            {
                return a.sort.CompareTo(b.sort);
            });
        return groups;
    }

    public static DPuXinProcessGroup GetGroup(string name)
    {
        DPuXinProcessGroup group = Find(groups, name);
        return group;
    }

    public static DPuXinProcessGroup Find(List < DPuXinProcessGroup > list, string name)
    {
        foreach(DPuXinProcessGroup group in list)
        {
            if(group.name == name)
            {
                return group;
            }
        }
        return null;
    }

}

public class DPuXinBlankingProcessGroup: DPuXinProcessGroup
{
    public DPuXinBlankingProcessGroup()
    {
        this.name = "下料";
        this.processGroup = new string[] { "切断" };
        this.dayDiff = -8;
        this.sort = 1;
        this.last = false;
    }
}
public class DPuXinSheetMetalProcessGroup: DPuXinProcessGroup
{
    public DPuXinSheetMetalProcessGroup()
    {
        this.name = "钣金";
        this.processGroup = new string[] { "冲压", "剪板", "折弯", "激光" };
        this.dayDiff = -5;
        this.sort = 2;
        this.last = false;
    }
}
public class DPuXinWeldingProcessGroup: DPuXinProcessGroup
{
    public DPuXinWeldingProcessGroup()
    {
        this.name = "辊轧";
        this.processGroup = new string[] { "带冲", "辊轧" };
        this.dayDiff = -5;
        this.sort = 3;
        this.last = false;
    }
}
public class DPuXinRollingProcessGroup: DPuXinProcessGroup
{
    public DPuXinRollingProcessGroup()
    {
        this.name = "焊接";
        this.processGroup = new string[] { "焊接" };
        this.dayDiff = -4;
        this.sort = 4;
        this.last = false;
    }
}
public class DPuXinPackProcessGroup: DPuXinProcessGroup
{
    public DPuXinPackProcessGroup()
    {
        this.name = "喷塑&打包";
        this.processGroup = new string[] { "喷塑", "打包" };
        this.dayDiff = -3;
        this.sort = 5;
        this.last = true;
    }
}


//货架准交状态
public class DPuXinQuasiState
{
    private static string childSchema = "D282605F80a2dc5e16c74b21813e7f1b5ad3615b";
    public static void ConfirmState(BizObject bizObject)
    {
        BizObject[] details = (BizObject[]) bizObject[childSchema];
        BizObject obj = Find(details);
        //总状态设置
        if(obj == null)
        {
            bizObject["ProcessStatus"] = "在制中";
        }
        else
        {
            int finished = ToInt(obj["Finished"]);
            bizObject["Finished"] = finished;
            int count = ToInt(bizObject["Count"]);
            if(finished >= count)
            {
                bizObject["ProcessStatus"] = "生产结案";
            }
            else
            {
                bizObject["ProcessStatus"] = "在制中";
            }
            int quantitySent = ToInt(bizObject["QuantitySent"]);
            if(quantitySent >= count)
            {
                bizObject["ProcessStatus"] = "发货结案";
            }
        }
        // 准交状态设置
        string processLine = bizObject["ProcessLine"] + string.Empty;
        if(!processLine.Contains("打包"))
        {
            bizObject["QuasiDelivery"] = "子件物料";
            return;
        }

        string deliveryDateStr = bizObject["DeliveryDate"] + string.Empty;
        if(string.IsNullOrEmpty(deliveryDateStr))
        {
            bizObject["QuasiDelivery"] = "交期不详";
        }
        else
        {
            DateTime deliveryDate = Convert.ToDateTime(deliveryDateStr);
            if(obj != null)
            {
                string actualDateStr = obj["ActualDate"] + string.Empty;
                if(!string.IsNullOrEmpty(actualDateStr))
                {
                    DateTime actualDate = Convert.ToDateTime(actualDateStr);
                    bizObject["QuasiDelivery"] = actualDate.CompareTo(deliveryDate) <= 0 ? "按期完成" : "超期完成";
                    return;
                }
            }
            DateTime now = System.DateTime.Now;
            if(deliveryDate.CompareTo(now) >= 0)
            {
                bizObject["QuasiDelivery"] = "交期未到";
            }
            else
            {
                bizObject["QuasiDelivery"] = "超期未完";
            }
        }
    }

    private static BizObject Find(BizObject[] array)
    {
        if(array == null || array.Length == 0)
        {
            return null;
        }
        foreach(BizObject obj in array)
        {
            if(Convert.ToString(obj["ProcessName"]) == "喷塑&打包")
            {
                return obj;
            }
        }
        return null;
    }
    private static int ToInt(object obj)
    {
        return Convert.ToInt32(String.IsNullOrEmpty(obj + "") ? "0" + obj : obj);
    }
}


public class DPuXinSheetPlan: DPuXinBaseSheet
{
    private BizObject plan;
    private List < BizObject > progress;
    private DPuXinProcessCard card;
    public DPuXinSheetPlan(IEngine Engine, string UserId): base(Engine, UserId)
    {
    }
    public void Init(string orderNo, string materialNo)
    {
        Init(QueryPlan(orderNo, materialNo));
    }
    public void Init(object planId)
    {
        BizObject p = Load(this.planSchemaCode, planId);
        Init(p);
    }
    public void Init(BizObject p)
    {
        if(p == null)
        {
            return;
        }
        this.plan = p;
        this.progress = GetChild(p, this.planChildSchemaCode);
        this.card = new DPuXinProcessCard(p["ProcessFlow"]);

        CheckProgress();
    }

    public bool CanReport(string process)
    {
        bool has = this.card.HasProcess(process);
        if(!has)
        {
            return false;
        }

        DPuXinProcessGroup group = this.card.Group(process);
        if(group == null)
        {
            return true;
        }
        BizObject obj = GetProgress(group);
        decimal p = Convert.ToDecimal(obj["Progress"]);
        if(p >= 1)
        {
            return false;
        }

        if(GetField(this.plan, "ProcessStatus") != "在制中")
        {
            return false;
        }

        return true;
    }

    public void UpdateState()
    {
        if(IsEmpty(this.plan["ReceiptDate"]))
        {
            this.plan["ReceiptDate"] = System.DateTime.Now;
        }
        if(IsEmpty(this.plan["ProcessStatus"]))
        {
            this.plan["ProcessStatus"] = "在制中";
        }
        DPuXinProcessGroup group = GetLast();
        if(group == null)
        {
            return;
        }
        BizObject last = GetProgress(group);

        this.plan["Finished"] = last["Finished"]; // 设置完成数
        int finished = ToInt(last["Finished"]);

        string status = GetField(this.plan, "ProcessStatus");
        int count = ToInt(this.plan["Count"]);
        if(status == "在制中")
        {
            if(finished >= count)
            {
                this.plan["ProcessStatus"] = "生产结案";
            }
        }
        if(status == "生产结案")
        {
            if(finished < count)
            {
                this.plan["ProcessStatus"] = "在制中";
            }
            else
            {
                int quantitySent = ToInt(this.plan["QuantitySent"]);
                if(quantitySent >= count)
                {
                    this.plan["ProcessStatus"] = "发货结案";
                }
            }
        }

        if(group.IsLast())
        {
            string deliveryDateStr = this.plan["DeliveryDate"] + string.Empty;
            if(string.IsNullOrEmpty(deliveryDateStr))
            {
                this.plan["QuasiDelivery"] = "交期不详";
            }
            else
            {
                DateTime deliveryDate = Convert.ToDateTime(deliveryDateStr);
                string actualDateStr = last["ActualDate"] + string.Empty;
                if(!string.IsNullOrEmpty(actualDateStr))
                {
                    DateTime actualDate = Convert.ToDateTime(actualDateStr);
                    this.plan["QuasiDelivery"] = actualDate.CompareTo(deliveryDate) <= 0 ? "按期完成" : "超期完成";
                }
                else
                {
                    DateTime now = System.DateTime.Now;
                    //throw new Exception("验证" + (deliveryDate.CompareTo(now) >= 0));
                    this.plan["QuasiDelivery"] = deliveryDate.CompareTo(now) >= 0 ? "交期未到" : "超期未完";
                }
            }
        }
        else
        {
            this.plan["QuasiDelivery"] = "子件物料";
        }
    }


    public void CheckProgress()
    {
        List < DPuXinProcessGroup > groups = card.EnableGroups();

        foreach(DPuXinProcessGroup group in groups)
        {
            BizObject obj = FindByKey(this.progress, "ProcessName", group.GroupName());
            if(obj == null)
            {
                GetProgress(group);
            }
        }
        foreach(BizObject obj in this.progress)
        {
            DPuXinProcessGroup group = DPuXinProcessGroup.Find(groups, GetField(obj, "ProcessName"));
            if(group == null)
            {
                obj["Progress"] = 1;
            }
            else
            {
                UpdateProgress(obj, 0); // 优先更新进度
            }
        }
        UpdateState();
    }

    //报工
    public void Report(string process, int finished)
    {
        DPuXinProcessGroup group = card.Group(process);
        if(group == null || group.Last() != process)
        {
            return;
        }
        BizObject obj = GetProgress(group);
        UpdateProgress(obj, finished);
        UpdateState();
    }

    public void Finished()
    {
        if(GetField(this.plan, "ProcessStatus") == "在制中")
        {
            this.plan["ProcessStatus"] = "生产结案";
        }
        if(this.plan != null)
        {
            //this.plan.Update();
        }
    }

    public void Save(bool create)
    {
        var s = create ? this.plan.Create() : this.plan.Update();
    }

    private void UpdateProgress(BizObject obj, int finished)
    {
        int total = this.ToInt(this.plan["Count"]);
        int result = this.ToInt(obj["Finished"]) + finished;
        obj["Finished"] = result;

        obj["Remain"] = (total - result) < 0 ? 0 : total - result;
        decimal p = Math.Round((decimal) result / total, 2);
        obj["Progress"] = p < 0 ? 0 : (p > 1 ? 1 : p);
        if(IsEmpty(obj["ActualDate"]))
        {
            obj["ActualDate"] = result >= total ? (object) System.DateTime.Today: null;
        }
    }
    private BizObject GetProgress(DPuXinProcessGroup group)
    {
        string groupName = group.GroupName();
        BizObject obj = FindByKey(this.progress, "ProcessName", group.GroupName());
        if(obj == null)
        {
            obj = CreateChild(this.planSchemaCode, this.planChildSchemaCode);
            obj["Finished"] = 0; //完成数
            obj["ProcessName"] = groupName;
            DateTime date = Convert.ToDateTime(this.plan["DeliveryDate"]);
            int diff = group.DayDiff();
            obj["PlanDate"] = date.AddDays(diff);
            this.progress.Add(obj);
            this.plan[this.planChildSchemaCode] = this.progress.ToArray();
        }
        return obj;
    }
    public DPuXinProcessCard GetCard()
    {
        return this.card;
    }
    public DPuXinProcessGroup GetLast()
    {
        List < DPuXinProcessGroup > groups = card.EnableGroups();
        if(IsEmpty(groups))
        {
            return null;
        }

        return groups[groups.Count - 1];
    }

    public BizObject QueryPlan(string orderNo, string materialNo)
    {
        H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();

        H3.Data.Filter.And andMatcher = new H3.Data.Filter.And();
        andMatcher.Add(new H3.Data.Filter.ItemMatcher("OrderNo", H3.Data.ComparisonOperatorType.Equal, orderNo));
        andMatcher.Add(new H3.Data.Filter.ItemMatcher("MaterialNo", H3.Data.ComparisonOperatorType.Equal, materialNo));
        filter.Matcher = andMatcher;

        BizObject[] plans = BizObject.GetList(this.Engine, this.UserId,
            this.Engine.BizObjectManager.GetPublishedSchema(this.planSchemaCode),
            GetListScopeType.GlobalAll, filter);

        if(plans == null || plans.Length == 0)
        {
            return null;
        }
        return plans[0];
    }
}