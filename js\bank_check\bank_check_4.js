let request = require('request');
let fs = require('fs');
let data = fs.readFileSync('./bank_check_4.txt').toString();
let persons = data.split(/\r\n/)
for (let person of persons) {
    if(!person) continue
    let infos = person.trim().split(/\s+/)

    request({
        url: 'http://192.168.1.200:18089/factors/bank_check_4',
        method: 'post',
        json: {
            "account_no": infos[0],
            "id_card": infos[1],
            "mobile": infos[2],
            "name": infos[3]
        }
    }, (err, resp, body) => {
        console.log(infos[0] + '_' + infos[1] + '_' + infos[2] + '_' + infos[3] + '_' + (resp.statusCode == 200?'OK':JSON.stringify(body)))
    })
    
}
/*request({
    url: 'http://192.168.1.200:18089/factors/bank_check_4',
    method: 'post',
    json: {
        "account_no": "6221881811017312208",
        "id_card": "142701198506182133",
        "mobile": "***********",
        "name": "姚兵兵"
    }
}, (err, resp, body) => {
    console.log(body)
})*/