
using System;
using System.Collections.Generic;
using System.Text;
using H3;

public class D149913a102c20e89494db8a7703c3a331053b4 : H3.SmartForm.SmartFormController
{
    public D149913a102c20e89494db8a7703c3a331053b4(H3.SmartForm.SmartFormRequest request) : base(request)
    {
    }

    protected override void OnLoad(H3.SmartForm.LoadSmartFormResponse response)
    {
        base.OnLoad(response);
    }

    protected override void OnSubmit(string actionName, H3.SmartForm.SmartFormPostValue postValue, H3.SmartForm.SubmitSmartFormResponse response)
    {
        string saleId = this.Request.BizObject["F0000008"] + string.Empty;
        string userId = this.Request.BizObject["F0000009"] + string.Empty;
        H3.DataModel.BizObject[] upBos = (H3.DataModel.BizObject[])this.Request.BizObject["D149913F11379756e86e49dc81ac506eb1a02e13"];

        if (string.IsNullOrWhiteSpace(userId) || string.IsNullOrWhiteSpace(saleId))
        {
            response.Message = "操作完成";
            return;
        }

        if (upBos == null || upBos.Length == 0)
        {
            response.Errors.Add("没有需要更新的表单");
            return;
        }
        foreach (H3.DataModel.BizObject upbo in upBos)
        {
            string objectId = upbo["F0000005"] + string.Empty;

            //修改公海信息
            string sql = "update I_D149913Sb5cj1kgr8jav74qk65xeu3800 set F0000034='" +
             saleId + "',F0000017='跟进中',OwnerId='" + userId + "'" +
             " where ObjectId='" + objectId + "'";
            //修改客户信息拥有者
            sql += ";update I_D149913d77c222c479b4bc0994639a67827eeb0 set F0000035='" +
             saleId + "',F0000017='跟进中',OwnerId='" + userId + "'" +
             " where ObjectId='" + objectId + "'";
            //修改销售订单拥有者
            sql += ";update I_D149913Sno9vwxo1ym57slwtoxwojn4h5 set OwnerId='" + userId + "' where F0000002='" + objectId + "'";
            // 修改跟进记录
            sql += ";update I_D149913c4d3264fd7dd440cb6f8262f16aa5428 set OwnerId='" + userId + "' where F0000001='" + objectId + "'";
            // 修改项目信息
            sql += ";update I_D149913cb600e63d0f64430b2bf116d08c19046 set OwnerId='" + userId + "' where F0000001='" + objectId + "'";
            this.Engine.EngineConfig.CreateCommand().ExecuteScalar(sql);
        }
        response.Message = "操作成功";
        response.ClosePage = true;
        response.Refresh = true;
    }
}