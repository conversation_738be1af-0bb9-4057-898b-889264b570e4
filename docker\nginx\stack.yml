 admin-web:
    image: hub.91cloudpay.com:4001/crowdsourcing-platform/admin-web:${DOCKER_TAG:-latest}
    ports:
       - ${ADMIN_WEB_PORT:-8866}:80
    volumes:
      - ${LOG_PATH:-/srv/crowdsourcing-platform/dev/logs}:/logs
    deploy:
      replicas: 1
      resources:
        limits:
          memory: 500M
      restart_policy:
        condition: on-failure
        delay: 1m
        max_attempts: 3
      update_config:
        order: start-first
    logging:
      driver: "json-file"
      options:
        max-size: "5M"
        max-file: "2"