export default class Header {
    static parse(str) {
        let lines = str.split(/\r|\n|\r\n/)
        let header = {}
        for (const line of lines) {
            if (!line || !line.trim()) continue
            let index = line.indexOf(':')
            let key = line.substr(0, index).trim()
            let value = line.substr(index+1).trim()
            header[key] = value
        }
        return header
    }

    static cookie(cookies, name) {
        if (Array.isArray(cookies)) {
            for (const cookie of cookies) {
                let result = findCookie(cookie, name)
                if (result) return result
            }
        } else {
            let result = findCookie(cookie, name)
            if (result) return result
        }

        function findCookie(_cookies, name) {
            let items = _cookies.split(';')
            for (let item of items) {
                let result = item.split('=')
                if (result.length == 2 && result[0] == name) {
                    return result[1]
                }
            }
        }
    }

    static convertCookie(cookies) {
        if(!Array.isArray(cookies) || cookies.length === 0) return

        let result = {}
        for (const cookie of cookies) {
            let items = cookie.split(';')

            for (const item of items) {
                let arr = item.trim().split('=')
                result[arr[0]] = item.trim()
            }
        }
        return Object.values(result).join(';') + ';'
    }

    static mergeCookie(...cookies) {
        let result = []
        if(cookies.length === 0) return ''
        for (const cookie of cookies) {
            if(!cookie) continue
            if(Array.isArray(cookie)) result = result.concat(cookie)
            else result.push(cookie)
        }
        return Header.convertCookie(result)
    }
}
