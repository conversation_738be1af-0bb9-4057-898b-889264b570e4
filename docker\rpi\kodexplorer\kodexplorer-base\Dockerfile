FROM arm32v7/php:7.2-apache-stretch

#COPY sources.list /etc/apt/sources.list

RUN  apt-get update && \
    yes|apt-get install wget  unzip ssh

RUN yes|apt-get install libfreetype6-dev libjpeg62-turbo-dev && \
    docker-php-ext-install mysqli && \
    docker-php-ext-install mbstring && \
    docker-php-ext-configure gd --with-freetype-dir=/usr/include/ --with-jpeg-dir=/usr/include/  &&  \
    docker-php-ext-install gd && \
    a2enmod cgi
