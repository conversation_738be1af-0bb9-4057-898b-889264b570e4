using System.Globalization;
public class FollowUpRecord
{
    string schema = "Svkx5tb4uzms1bljuj6dq763d3";

    H3.IEngine engine;
    string userId;
    public FollowUpRecord(H3.IEngine engine, string userId)
    {
        this.engine = engine;
        this.userId = userId;
    }
    public void Create(H3.DataModel.BizObject record, H3.DataModel.BizObject customer)
    {
        string objectId = record["ObjectId"] + string.Empty;
        H3.DataModel.BizObject followUPRecord = H3.DataModel.BizObject.Load(this.userId,
  this.engine, this.schema, objectId, false);     //加载客户公海信息
        bool isCreate = false;
        if (followUPRecord == null)
        {
            H3.DataModel.BizObjectSchema followUPRecordSchema = this.engine.BizObjectManager.GetPublishedSchema(this.schema);
            followUPRecord = new H3.DataModel.BizObject(this.engine, followUPRecordSchema, this.userId);
            isCreate = true;
        }
        followUPRecord["ObjectId"] = objectId; //objectId
        followUPRecord["F0000003"] = customer["ObjectId"]; //客户信息
        followUPRecord["F0000004"] = record["F0000041"];//跟进日期
        followUPRecord["F0000007"] = record["F0000042"];//跟进方式
        followUPRecord["F0000008"] = record["F0000043"];//跟进内容
        followUPRecord["CreatedBy"] = record["F0000044"];//跟进人
        followUPRecord["F0000011"] = customer["F0000035"];//客户等级
        followUPRecord["F0000020"] = customer["F0000001"];//客户名称
        followUPRecord.Status = H3.DataModel.BizObjectStatus.Effective; // 设置状态 ，触发业务规则
        string worflowId = followUPRecord.WorkflowInstanceId;//流程id

        if (string.IsNullOrEmpty(worflowId))
        {
            string instanceId = System.Guid.NewGuid().ToString(); // 创建流程id
            followUPRecord.WorkflowInstanceId = instanceId;
        }
        H3.ErrorCode code = isCreate ? followUPRecord.Create() : followUPRecord.Update();
        H3.Workflow.Instance.WorkflowInstance wfInstance = this.engine.WorkflowInstanceManager.GetWorkflowInstance(followUPRecord.WorkflowInstanceId);
        if (wfInstance == null)
        {
            //发起流程
            string workItemID = string.Empty;
            string errorMsg = string.Empty;
            H3.Workflow.Template.WorkflowTemplate wfTemp = this.engine.WorkflowTemplateManager.GetDefaultWorkflow(followUPRecord.Schema.SchemaCode);
            this.engine.Interactor.OriginateInstance(this.userId, followUPRecord.Schema.SchemaCode,
               wfTemp.WorkflowVersion, followUPRecord.ObjectId, followUPRecord.WorkflowInstanceId, H3.Workflow.WorkItem.AccessMethod.Web,
               true, // 是否提交流程操作
               string.Empty, true, out workItemID, out errorMsg);
        }
    }

}