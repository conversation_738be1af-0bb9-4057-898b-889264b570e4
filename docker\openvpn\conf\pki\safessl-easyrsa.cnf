# For use with Easy-RSA 3.0+ and OpenSSL or LibreSSL

####################################################################
[ ca ]
default_ca	= CA_default		# The default ca section

####################################################################
[ CA_default ]

dir		= /etc/openvpn/pki	# Where everything is kept
certs		= /etc/openvpn/pki			# Where the issued certs are kept
crl_dir		= /etc/openvpn/pki			# Where the issued crl are kept
database	= /etc/openvpn/pki/index.txt	# database index file.
new_certs_dir	= /etc/openvpn/pki/certs_by_serial	# default place for new certs.

certificate	= /etc/openvpn/pki/ca.crt	 	# The CA certificate
serial		= /etc/openvpn/pki/serial 		# The current serial number
crl		= /etc/openvpn/pki/crl.pem 		# The current CRL
private_key	= /etc/openvpn/pki/private/ca.key	# The private key
RANDFILE	= /etc/openvpn/pki/.rand		# private random number file

x509_extensions	= basic_exts		# The extensions to add to the cert

# This allows a V2 CRL. Ancient browsers don't like it, but anything Easy-RSA
# is designed for will. In return, we get the Issuer attached to CRLs.
crl_extensions	= crl_ext

default_days	= 825	# how long to certify for
default_crl_days= 3650	# how long before next CRL
default_md	= sha256		# use public key default MD
preserve	= no			# keep passed DN ordering

# This allows to renew certificates which have not been revoked
unique_subject	= no

# A few different ways of specifying how similar the request should look
# For type CA, the listed attributes must be the same, and the optional
# and supplied fields are just that :-)
policy		= policy_anything

# For the 'anything' policy, which defines allowed DN fields
[ policy_anything ]
countryName		= optional
stateOrProvinceName	= optional
localityName		= optional
organizationName	= optional
organizationalUnitName	= optional
commonName		= supplied
name			= optional
emailAddress		= optional

####################################################################
# Easy-RSA request handling
# We key off $DN_MODE to determine how to format the DN
[ req ]
default_bits		= 2048
default_keyfile 	= privkey.pem
default_md		= sha256
distinguished_name	= cn_only
x509_extensions		= easyrsa_ca	# The extensions to add to the self signed cert

# A placeholder to handle the $EXTRA_EXTS feature:
#%EXTRA_EXTS%	# Do NOT remove or change this line as $EXTRA_EXTS support requires it

####################################################################
# Easy-RSA DN (Subject) handling

# Easy-RSA DN for cn_only support:
[ cn_only ]
commonName		= Common Name (eg: your user, host, or server name)
commonName_max		= 64
commonName_default	= ChangeMe

# Easy-RSA DN for org support:
[ org ]
countryName			= Country Name (2 letter code)
countryName_default		= US
countryName_min			= 2
countryName_max			= 2

stateOrProvinceName		= State or Province Name (full name)
stateOrProvinceName_default	= California

localityName			= Locality Name (eg, city)
localityName_default		= San Francisco

0.organizationName		= Organization Name (eg, company)
0.organizationName_default	= Copyleft Certificate Co

organizationalUnitName		= Organizational Unit Name (eg, section)
organizationalUnitName_default	= My Organizational Unit

commonName			= Common Name (eg: your user, host, or server name)
commonName_max			= 64
commonName_default		= ChangeMe

emailAddress			= Email Address
emailAddress_default		= <EMAIL>
emailAddress_max		= 64

####################################################################
# Easy-RSA cert extension handling

# This section is effectively unused as the main script sets extensions
# dynamically. This core section is left to support the odd usecase where
# a user calls openssl directly.
[ basic_exts ]
basicConstraints	= CA:FALSE
subjectKeyIdentifier	= hash
authorityKeyIdentifier	= keyid,issuer:always

# The Easy-RSA CA extensions
[ easyrsa_ca ]

# PKIX recommendations:

subjectKeyIdentifier=hash
authorityKeyIdentifier=keyid:always,issuer:always

# This could be marked critical, but it's nice to support reading by any
# broken clients who attempt to do so.
basicConstraints = CA:true

# Limit key usage to CA tasks. If you really want to use the generated pair as
# a self-signed cert, comment this out.
keyUsage = cRLSign, keyCertSign

# nsCertType omitted by default. Let's try to let the deprecated stuff die.
# nsCertType = sslCA

# CRL extensions.
[ crl_ext ]

# Only issuerAltName and authorityKeyIdentifier make any sense in a CRL.

# issuerAltName=issuer:copy
authorityKeyIdentifier=keyid:always,issuer:always

