
using System;
using System.Collections.Generic;
using System.Text;
using H3;
using H3.DataModel;
using System.Data;


public class D282605Seibgfbqg1mf1id55ejcjoz6f3_ListViewController : H3.SmartForm.ListViewController
{
    public D282605Seibgfbqg1mf1id55ejcjoz6f3_ListViewController(H3.SmartForm.ListViewRequest request) : base(request)
    {
    }

    protected override void OnLoad(H3.SmartForm.LoadListViewResponse response)
    {
        base.OnLoad(response);
    }

    protected override void OnSubmit(string actionName, H3.SmartForm.ListViewPostValue postValue, H3.SmartForm.SubmitListViewResponse response)
    {
        if (actionName == "custom_import")
        {
            BizObject report = ConvetToBizObject();
            DPuXinBoxReport boxReport = new DPuXinBoxReport(this.Engine, this.Request.UserContext.UserId);
            DPuXinError error = boxReport.Validate(report);
            if (error.HasError())
            {
                response.ReturnData = BuildResp(error);
                return;
            }

            report.Status = BizObjectStatus.Effective; //设置状态生效
            string instanceId = System.Guid.NewGuid().ToString(); // 创建流程id
            report.WorkflowInstanceId = instanceId;
            report.Create();
            //发起流程
            H3.Workflow.Instance.WorkflowInstance wfInstance = this.Engine.WorkflowInstanceManager.GetWorkflowInstance(report.WorkflowInstanceId);
            string workItemID = string.Empty;
            string errorMsg = string.Empty;
            H3.Workflow.Template.WorkflowTemplate wfTemp = this.Engine.WorkflowTemplateManager.GetDefaultWorkflow(report.Schema.SchemaCode);
            this.Engine.Interactor.OriginateInstance(this.Request.UserContext.UserId, report.Schema.SchemaCode,
                wfTemp.WorkflowVersion, report.ObjectId, report.WorkflowInstanceId, H3.Workflow.WorkItem.AccessMethod.Web,
                true, // 是否提交流程操作
                string.Empty, true, out workItemID, out errorMsg);

        }
        if(actionName == "remove")
        {
            string[]  objectIds = this.Deserialize<string[]>(this.Request["ObjectIds"]);
            D282605Sc2hh33czw2c9935a17pfux831

        }
        base.OnSubmit(actionName, postValue, response);
    }

    protected BizObject ConvetToBizObject()
    {
        Dictionary<string, object> obj = this.Deserialize<Dictionary<string, object>>(this.Request["ObjectId"]);

        BizObjectSchema schema = this.Engine.BizObjectManager.GetPublishedSchema("D282605Seibgfbqg1mf1id55ejcjoz6f3");
        BizObject report = new BizObject(this.Engine, schema, this.Request.UserContext.UserId);
        report["Team"] = obj["Team"];
        report["ReportDate"] = System.DateTime.Now;

        List<BizObject> list = new List<BizObject>();
        List<Dictionary<string, object>> details = this.Deserialize<List<Dictionary<string, object>>>(obj["Details"] + string.Empty);
        foreach (Dictionary<string, object> detail in details)
        {
            BizObject item = this.ConvertToObj(detail, schema.GetChildSchema("D282605F9ca39816aecd46baba26c8004d2d8716"));
            list.Add(item);
        }
        report["D282605F9ca39816aecd46baba26c8004d2d8716"] = list.ToArray();
        return report;
    }
    private BizObject ConvertToObj(Dictionary<string, object> dict, BizObjectSchema schema)
    {
        BizObject item = new BizObject(this.Engine, schema, this.Request.UserContext.UserId);
        foreach (KeyValuePair<string, object> pair in dict)
        {
            item[pair.Key] = pair.Value;
        }
        return item;
    }
    public Dictionary<string, object> BuildResp(DPuXinError xinError)
    {
        Dictionary<string, object> resp = new Dictionary<string, object>();
        if (!string.IsNullOrEmpty(xinError.GetError()))
        {
            resp.Add("Error", xinError.GetError());
        }
        if (xinError.GetErrors() != null && xinError.GetErrors().Count > 0)
        {
            resp.Add("Errors", this.Serialize(xinError.GetErrors()));
        }
        return resp;
    }
}