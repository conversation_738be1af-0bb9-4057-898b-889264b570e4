# 示例 PowerShell 脚本
Add-Type -AssemblyName System.Windows.Forms

Add-Type @"
using System;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Text;

public struct RECT
{
    public int left;
    public int top;
    public int right;
    public int bottom;
}

public class pInvoke
{
    [DllImport("user32.dll", SetLastError = true)]
    public static extern bool MoveWindow(IntPtr hWnd, int X, int Y, int nWidth, int nHeight, bool bRepaint);

    [DllImport("user32.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.StdCall, ExactSpelling = true, SetLastError = true)]
    public static extern bool GetWindowRect(IntPtr hWnd, ref RECT rect);

    [DllImport("user32.dll")]
    public static extern bool ShowWindowAsync(IntPtr hWnd, int nCmdShow);
}
"@


function Move-Window([System.IntPtr]$hwnd) {
    # get which screen the app has been spawned into
    $screen = [System.Windows.Forms.Screen]::AllScreens | Where-Object { $_.DeviceName -eq '\\.\DISPLAY1' }
    [pInvoke]::MoveWindow($hwnd, $screen.Bounds.X, $screen.Bounds.Y, $screen.Bounds.Width, $screen.Bounds.Height, $true)
}


$app = $args[0]
$name = $args[1]
Start-Process $app
Start-Sleep -Milliseconds 500
$main = Get-Process $name
foreach ($item in $main) {
    if( $item.MainWindowHandle -ne 0){
        Move-Window $item.MainWindowHandle
    }
}

