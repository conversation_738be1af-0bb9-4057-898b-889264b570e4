import req from '../util/request.js'
import Notify from '../util/notify.js'
import log from '../util/log.js'
import Time from '../util/time.js'
import Header from '../util/header.js'
import XunFly from '../util/XunFly.js'

import cloudscraper from '../cloudscraper/index.js'
const notify = new Notify()
req.global_options.proxy = "http://192.168.1.173:7890"
let url = 'https://purefast.net'

  ; (async () => {
    for (let i = 0; i < 3; i++) {
      log.append(`--------登录 第${i + 1}次----------`)
      console.log(`登录第${i + 1}次`)
      let success = await exec()
      if (success) return
    }
  })();


async function exec() {
  try {

    let resp = await verifyLogin()

    try {
      if(!resp) throw new Error("登录失败")
      body = JSON.parse(resp?.body)
    } catch (e) {
      log.append('登录获取cookie失败')
      console.log(e, '登录获取cookie失败')
      return false
    }

    if (resp.statusCode === 200 && body.ret == 1) {
      await Time.wait(1)
      let cookies = resp.headers["set-cookie"];

      log.append(body);
      log.append("--------签到----------");

      console.log(`${Header.convertCookie(cookies)}${csrf_cookie}`)
      resp = await req.post(`${url}/user/checkin`, {
        headers: {
          'cookie': `${Header.convertCookie(cookies)}${csrf_cookie}`,
          'referer': `${url}/user`,
        },
        //http2: true,
        hooks: {
          beforeRedirect: [
            (options, resp) => {
              let cookies = options.headers['cookie']
              let redict_cookies = resp.headers['set-cookie']
              options.headers['cookie'] = Header.mergeCookie(cookies, redict_cookies)
            }
          ]
        }
      }, {
        _auto: true,
        _retry: {
          condition: (resp) => {
            try {
              JSON.parse(resp.body)
              return false
            } catch (e) {
              return true
            }

          }
        }
      })
      log.append(resp);
      log.print()
      notify.sendText('purefast\n' + log.body())
      return true
    } else {
      log.append('请求登录失败')
      return false
    }
  } catch (e) {
    log.append("请求异常")
    console.log(e)
    return false
  }
}

async function verifyLogin() {
  let login_url = `${url}/auth/login`
  await Time.wait(1)
  let cookie = await cloudscraper.get(login_url)
  if(!cookie) return false
  console.log('cloudflare', cookie)
  await Time.wait(6)

  let resp = await login(login_url, cookie)
  let body = resp?.body
  if (!body) {
    console.log('登录失败')
    return false
  }
  if (body.includes('html')) {
    if (body.includes('Verify Yourself')) {
      let captcha = parseVar(body, /<input type="hidden" name="GOEDGE_WAF_CAPTCHA_ID" value="(.*?)"\/>/)
      let image = parseVar(body, /<img src="data:image\/png;base64, (.*?)"\/>/)
      let fly = new XunFly()
      let result = await fly.req({ "img64": `${image}` })
      console.log('验证结果', result)
      let code = result?.result
      if (code) {
        let cookies = resp.headers['set-cookie']
        let verify_url = resp['redirectUrls'][0]['href']
        console.log('verify cookies', verify_url);
        resp = await verify(`${verify_url}`, Header.convertCookie(cookies), {
          'GOEDGE_WAF_CAPTCHA_ID': captcha,
          'GOEDGE_WAF_CAPTCHA_CODE': code
        })

        console.log('verify resp', resp.statusCode)
        resp = await login(login_url, cookie, verify_url)
      }
    }
  }
  return resp
}


async function login(login_url, cookie, referer) {
  return await req.post(login_url, {
    headers: {
      'cookie': cookie,
      'referer': referer || login_url,
    },
    form: {
      email: "<EMAIL>",
      passwd: "NSE5guF7x79u",
      code: ''
    }
  }, {
    _auto: false,
    _retry: {
      condition: (resp) => {
        let body = resp?.body
        if (body) {
          if (body.includes('<html>')) return false
        } else {
          return true
        }
        console.log(resp.body, resp.statusCode)
        console.log('重试登录')
        let _cookies = resp.headers['set-cookie']
        let result = !_cookies || _cookies.length === 0
        try {
          JSON.parse(resp.body)
          return result
        } catch (e) {
          return true
        }
      }
    }
  })
}

async function verify(login_url, cookie, form) {
  return await req.post(login_url, {
    headers: {
      'cookie': cookie,
      'referer': login_url,
    },
    form: form
  }, {
    _auto: false,
  })
}

function parseVar(str, _reg = '"(.*?)"') {
  let reg = new RegExp(_reg, 'mg')
  try {
    let match = reg.exec(str)
    return match[1]
  } catch (e) {
    console.log(e.message)
  }
  return ''
}