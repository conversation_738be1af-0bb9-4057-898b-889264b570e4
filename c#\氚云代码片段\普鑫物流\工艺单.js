/*
 * $.ListView.GetSelected()获取选中的记录
 * $.ListView.RefreshView()刷新列表
 * $.ListView.Post()请求后台
 * $.ListView.InitQueryItems()修改过滤条件
 * $.ListView.RefreshView()刷新页面
 * $.ListView.ActionPreDo() 按钮执行之前的事件
 */
(function (window) {
  function findSelector(element, selector) {
    if (!element) return;
    if (element.className && (element.className + "").indexOf(selector) >= 0)
      return element;
    if (element.id == selector) return element;

    let children = element.children;

    if (children && children.length > 0) {
      for (let child of children) {
        let e = findSelector(child, selector);
        if (e) return e;
      }
    }
  }
  function UploadExcel(template) {
    this.template = template;
    this.doc = document;
    this.insertHeadStyle(".code-check-warn {display:none} ", "code-check");
    this.insertHeadStyle(`
.import-step1-content .import-card {
    position: relative;
    padding: 12px 20px 8px;
    color: #304265;
}
.import-step1-content .import-card .template-download {
    height: 20px;
    line-height: 20px;
    margin-bottom: 4px;
    font-size: 12px;
}
.import-step1-content .import-card .template-download span {
    margin-left: 4px;
    color: #107fff;
    cursor: pointer;
}
.import-step1-content .import-card .template-download span .download-o {
    font-size: 12px;
}
.import-step1-content .import-card .template-download strong {
    text-decoration: none;
    font-weight: 600;
}
.import-step1-content .import-card .upload-file {
    position: relative;
}
.import-step1-content .import-card .upload-file .h3-upload-helper {
    display: block;
    height: 106px;
    padding: 16px 24px;
    text-align: center;
    border: 1px dashed #c9ccd8;
    border-radius: 8px;
    background-color: #f3f5f8;
}
.import-step1-content .import-card .upload-file .h3-upload-helper .h3-upload-dragger {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
.import-step1-content .import-card .upload-file .selectfile-trigger {
    display: inline-block;
    line-height: 22px;
    margin: 0 0 8px;
    color: #107fff;
}
.import-step1-content .import-card .upload-file .selectfile-trigger .cloud-upload-o {
    font-size: 20px;
    vertical-align: bottom;
    margin-right: 4px;
}
.import-step1-content .h3-upload__input {
    display: none;
}

.import-step1-content .import-card-title {
    position: relative;
    line-height: 22px;
    padding-left: 7px;
    margin-bottom: 8px;
    font-weight: 600;
}
.import-step1-content .import-card-title:before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    width: 3px;
    height: 12px;
    transform: translateY(-50%);
    background-color: #107fff;
    border-radius: 2px;
}
.import-step1-content .upload-file-list .upload-file-item {
    display: flex;
    justify-content: space-between;
    margin-top: 5px;
    padding: 5px;
    border: 1px solid #107fff;
    align-items: center;
    border-radius: 4px;
    background-color: rgba(16,127,255,.1);
}
.import-step1-content .upload-file-list .upload-file-item  .upload-file-item-close{
    speak: none;
    font-style: normal;
    font-weight: 400;
    font-variant: normal;
    text-transform: none;
    font-size:12px;
    line-height: 1;
    color: #000;
    font-family: icomoon!important;
    pointer: cursor;
}
.import-step1-content .upload-file-list .upload-file-item  .upload-file-item-close:before{
    content: "\\E059";
}
.import-step1-content .import-card .validate-error {
    color: #ff4384;
    display:none;
}
.validate-error .error-table {
  display:flex;
  flex-direction: column;
  width: 100%;
  border: thin solid #e5e5e5;
  margin-top:10px;
}
.validate-error .error-table-head,.validate-error .error-table-body{
  display:flex;
  flex:1;
}
.validate-error .error-table-body {
  flex-direction: column;
}
.validate-error .error-table-head > div, .validate-error .error-table-body .error-table-row > div{
  flex:1;
  padding: 5px;
}
.validate-error .error-table-head > div:not(:last-child) {
  border-right: thin solid #e5e5e5;
}
.validate-error .error-table-body .error-table-row {
  border-top: thin solid #e5e5e5;
}
.validate-error .error-table-body .error-table-row > div:not(:last-child) {
  border-right: thin solid #e5e5e5;
}
.validate-error .error-table-body .error-table-row {
  display:flex;
}


.ant-btn-right{
    float:right;
    margin-left: 10px;
}
`, "custom-import");
    if(!window['XLSX'])
    this.insertScript(
      "https://cdn.sheetjs.com/xlsx-0.20.0/package/dist/xlsx.full.min.js"
    );
  }

  UploadExcel.prototype.insertHeadStyle = function (css, className = "") {
    if(findSelector(this.doc.head, className)) {
      return;
    }
    let headFrag = this.doc.createDocumentFragment();
    headFrag.appendChild(this.createCSSNode(css, className));
    this.doc.head.appendChild(headFrag);
  };
  UploadExcel.prototype.insertScript = function (src) {
    let script = this.doc.createElement("script");
    script.type = "text/javascript";
    script.src = src;
    this.doc.body.appendChild(script);
  };

  UploadExcel.prototype.createCSSNode = function (css,  className = "",  initType = "text/css") {
    let cssNode = this.doc.createElement("style");
    if (className) {
      cssNode.className = className;
      const xclass = "." + className.split(" ").join(".");
      cssNode.dataset.xclass = xclass;
    }

    cssNode.setAttribute("type", initType);
    cssNode.appendChild(this.doc.createTextNode(css));
    return cssNode;
  };

  UploadExcel.prototype.createElement = function (content) {
    let node = this.doc.createElement("div");
    node.innerHTML = content;
    return node.children[0];
  };

  UploadExcel.prototype.createUpload = function () {
    let upload = this.createElement(`
  <div class="import-step1-content">
   <div class="import-card">
      <div class="import-card-title">上传文件</div>
      <div class="import-content">
          <p class="template-download">请先<span class="download download-template"><i class="h3yun_All download-o"></i>下载导入模板</span>修改后上传； 请勿修改模板表格的【<strong>字段标题</strong>】，防止导入失败</p>
          <div class="h3-upload upload-file">
              <div class="h3-upload-helper h3-upload--text is-dragger">
                  <div class="h3-upload-dragger">
                      <span class="selectfile-trigger"><i class="h3yun_All cloud-upload-o"></i>点击选择文件上传</span>
                      <p style="font-size: 12px; line-height: 20px; margin: 0px;">支持格式：xls或xlsx，大小不超过20M，数据最多不超过10000行、200列</p>
                  </div>
              </div>
              <input type="file" name="file" accept=".xls,.xlsx" class="h3-upload__input">
          </div>
          <div class="upload-file-list"></div>
      </div>
      <div class="validate-error"></div>
  </div>
  </div>`);
    let $btn = findSelector(upload, "upload-file");
    let $file = findSelector(upload, "upload__input");
    let $file_list = findSelector(upload, "upload-file-list");
    let $download = findSelector(upload, "download-template")
    let _this = this;
    $download.onclick = async function() {
      console.log("----download template")
      $.ajax({
        type: "POST",
        url: "/App/OnAction/",
        data: {
            PostData: JSON.stringify({"Command":"Download","ActionName":"DoAction","QueryCode":"D2826050ff2122070c84a41be2f42c1ac623b4a","ObjectId":_this.template})
        },
        dataType: "json",
        async: false,
        success: function(resp) {
          if(resp && resp.Successful) {
            let data = resp?.ReturnData?.Response?.ReturnData
            if(!data) return
              // 创建隐藏的可下载链接
            let eleLink = _this.doc.createElement('a');
            eleLink.download = data['Description'];
            eleLink.style.display = 'none';
            eleLink.href = data["Url"];
            // 触发点击
            _this.doc.body.appendChild(eleLink);
            eleLink.click();
            // 然后移除
            _this.doc.body.removeChild(eleLink);
          }
        }
      })


    }
    $btn.onclick = function () {
      $file.click();
    };

    $file.onchange = function (event) {
      console.log("----change");

      let file = $file.files[0];
      $file_list.innerHTML = "";

      let $file_item = _this.createElement(
        `<div class="upload-file-item"><div class="upload-file-item-title">${file.name}</div><div class="upload-file-item-close"></div></div>`
      );
      let $close = findSelector($file_item, "upload-file-item-close");
      $close.onclick = () => {
        $file_list.innerHTML = "";
        $file.files.length = 0;
      };
      $file_list.appendChild($file_item);
      _this.value = file;
      event.target.value = null;
    };

    this.upload = upload;
    return upload;
  };

  UploadExcel.prototype.writeError = function (errors) {
    let $error = findSelector(this.upload, "validate-error");
    $error.innerHTML = "";
    $error.style.display = "block";

    let $table = this
      .createElement(`<div class="error-table"><div class="error-table-head">
    <div>订单号</div><div>产品名称</div><div>错误原因</div></div></div>`);
    let body = "";
    for (let error of errors) {
      body += `<div class="error-table-row"><div>${error["OrderNo"]}</div><div>${error["MaterialName"]}</div><div>${error["Error"]}</div></div>`;
    }
    $table.appendChild(
      this.createElement(`<div class="error-table-body">${body}</div>`)
    );
    $error.appendChild($table);
  };


  window["UploadExcel"] = UploadExcel;
})(window);

function waitVariable(name) {
  let count = 1;
  function wait(resolve) {
    if (window[name]) resolve(window[name]);
    else {
      if (count > 50) resolve();
      else {
        count++;
        setTimeout(() => {
          wait(resolve);
        }, 100);
      }
    }
  }
  return new Promise((resolve) => {
    wait(resolve);
  });
}

function readFile(file) {
  return new Promise((resolve) => {
    let reader = new FileReader();
    reader.onload = async function (e) {
      let xlsx = await waitVariable("XLSX");
      if (xlsx) {
        let wb = xlsx.read(e.target.result);
        resolve(wb);
      } else {
        $.IShowError("读取Excel失败");
      }
    };
    reader.readAsArrayBuffer(file);
  });
}

function buildHeadMap(sheet) {
  let head = {};
  let ref = sheet["!autofilter"].ref;

  let range = ref.split(":");
  let start = range[0].replace(/\d+/, "");
  let end = range[1].replace(/\d+/, "");
  while (revertCode(start) <= revertCode(end)) {
    let v = sheet[`${start}4`] && sheet[`${start}4`].v;
    if (v !== undefined && v !== "") {
      head[v] = start;

      setMergeValue(sheet, start, v);
    }
    let next = sheet[`${start}5`] && sheet[`${start}5`].v;
    if (next !== undefined && next !== "") {
      head[v + "-" + next] = start;
    }

    start = Binary26(start, 1);
  }
  return head;
}

function setMergeValue(sheet, code, v) {
  let merges = sheet["!merges"];
  let c = revertCode(code) - 1;
  for (let merge of merges) {
    if (merge["s"].r == 3 && merge["e"].r == 3 && merge["s"].c == c) {
      let start = merge["s"].c;
      let end = merge["e"].c;
      for (let i = 0; i <= end - start; i++) {
        let column = Binary26(code, i);
        if (!sheet[column + 4]) sheet[column + 4] = {};
        sheet[column + 4].v = v;
      }
    }
  }
}
function revertCode(str) {
  let items = str.split("");
  let length = items.length;
  let result = 0;
  for (let i = 1; i <= length; i++) {
    let c = items[i - 1].charCodeAt() - 64;
    result += Math.pow(26, length - i) * c;
  }
  return result;
}
function Binary26(str, internal) {
  let items = str.split("");
  for (let i = items.length - 1; i >= 0; i--) {
    let code = items[i].charCodeAt();
    let remain = (code - 65 + internal) % 26;
    internal = Math.floor((code - 65 + internal) / 26);
    items[i] = String.fromCharCode(65 + remain);
  }
  let carry = "";
  if (internal >= 1) {
    carry = String.fromCharCode(64 + internal);
  }
  return carry + items.join("");
}

function fortmatDateXLSX(serial, format) {
  let utc_days = Math.floor(serial - 25569);
  let utc_value = utc_days * 86400;
  let date_info = new Date(utc_value * 1000);
  let fractional_day = serial - Math.floor(serial) + 0.0000001;
  let total_seconds = Math.floor(86400 * fractional_day);
  let seconds = total_seconds % 60;
  total_seconds -= seconds;
  let hours = Math.floor(total_seconds / (60 * 60));
  let minutes = Math.floor(total_seconds / 60) % 60;
  if(format) {
    const formatObj = {
      y: date_info.getFullYear(),
      m: date_info.getMonth() + 1,
      d: date_info.getDate(),
      h: hours,
      i: minutes,
      s: seconds,
      a: date_info.getDay()
    };
    return format.replace(/{([ymdhisa])+}/g, (result, key) => {
        let value = formatObj[key];
        if (key === 'a') return ['一', '二', '三', '四', '五', '六', '日'][value - 1];
        if (result.length > 0 && value < 10) {
            value = '0' + value
        }
        return value || 0
    })
  } else {
    return new Date(date_info.getFullYear(),date_info.getMonth(),date_info.getDate(),hours,minutes,seconds);
  }
}



$.ListView.ActionPreDo = function (actionCode) {
  if (actionCode === "custom_import") {
    let upload = new UploadExcel("ProcessSheetTemplate");

    let $modal = $.IModal({
      Title: "导入",
      Content: upload.createUpload(),
      ToolButtons: [
        {
          Text: "取消",
          Theme: "ant-btn  ant-btn-right",
          CallBack: () => {
            $modal.hide();
          }
        },
        {
          Text: "提交",
          Theme: "ant-btn ant-btn-primary ant-btn-right",
          CallBack: () => {
            let file = upload.value;
            if (!file) {
              $.IShowError("", "请上传文件");
              return;
            }
            $modal.SetButtonLoading()
            processFile(file,  (resp) => {
              $modal.ResetButton()
              if(resp.Successful) {
                $.ListView.RefreshView();
                $.IShowSuccess("上传成功");
                $modal.hide();
              } else {
                $.IShowError(resp.ErrorMessage || resp?.ReturnData?.Response?.Message);
              }
            });
          }
        }
      ],
      Width: "50%"
    });
    $modal.SetButtonLoading = function() {
      for (var e = 0; e < $modal.ActionObjects.length; e++) {
        let item = $modal.ActionObjects[e]
        if(!item || !item.Element) continue
        if(item.Text == '提交') {
          item.Element.attr("data-loading-text", "处理中")
        } else {
          item.Element.attr("data-loading-text", item.Text)
        }
        item.Element.button("loading")
      }
    }
    return false;
  }

  if(actionCode == 'create_plan') {
    $.IShowForm( "D282605d0ea69005b0b4194ab26382f3e054327", "", {
      ListView: $.ListView
    }, true,  false, {
      showInModal: true, title: "生成计划", height: 400, width: 600,   OnShowCallback: function( da ) { }, onHiddenCallback: function( data ) { }

    });

    return false
  }
}

async function processFile(file, callback) {
  let result = await readFile(file);

  let sheet = result.Sheets["下料单"];
  let head = buildHeadMap(sheet);

  let items = [];
  let customer = sheet["C2"].v;
  let order_no = sheet["C3"].v;
  let project_name = sheet["K2"].v;
  let delivery_date = sheet['AB3'].v;
  if(typeof delivery_date === 'number') {
    delivery_date = fortmatDateXLSX(delivery_date, '{y}-{m}-{d}')
  } else {
    delivery_date = ''
  }

  let process_flow_keys = ExtractProcessFlow(head);
  for (
    let i = 6;
    sheet[`${head["序号"]}${i}`] && sheet[`${head["序号"]}${i}`].v;
    i++
  ) {
    let serial_no = sheet[`${head["序号"]}${i}`]?.v;
    serial_no =
      typeof serial_no == "number"
        ? serial_no % 1 === 0
          ? serial_no + ""
          : serial_no.toFixed(1)
        : serial_no;
    let part_name = sheet[`${head["零/部件名称"]}${i}`]?.v;
    let draw_suffix = sheet[`${head["图号后缀"]}${i}`]?.v;

    let material_no = sheet[`${head["物料编码"]}${i}`]?.v;
    let material_model = sheet[`${head["材料名称规格型号"]}${i}`]?.v;

    let thick = sheet[`${head["下料尺寸-厚"]}${i}`]?.v;
    thick = thick != undefined ? parseFloat(thick).toFixed(1) : thick;
    let width = sheet[`${head["下料尺寸-宽"]}${i}`]?.v;
    width = width != undefined ? parseFloat(width).toFixed(1) : width;
    let length = sheet[`${head["下料尺寸-长"]}${i}`]?.v;
    length = length != undefined ? parseFloat(length).toFixed(1) : length;

    let piece = sheet[`${head["件/套"]}${i}`]?.v;
    let count = sheet[`${head["总数量"]}${i}`]?.v;

    let area = sheet[`${head["喷涂面积-单件"]}${i}`]?.v;
    area = area != undefined ? parseFloat(area).toFixed(2) : area;
    let sum_area = sheet[`${head["喷涂面积-总面积"]}${i}`]?.v;
    sum_area =
      sum_area != undefined ? parseFloat(sum_area).toFixed(1) : sum_area;
    let weight = sheet[`${head["产品净重-单件"]}${i}`]?.v;
    weight = weight != undefined ? parseFloat(weight).toFixed(2) : weight;
    let sum_weight = sheet[`${head["产品净重-总重量"]}${i}`]?.v;
    sum_weight =
      sum_weight != undefined ? parseFloat(sum_weight).toFixed(1) : sum_weight;

    let spray_color = sheet[`${head['喷塑颜色']}${i}`]?.v

    let remark = sheet[`${head["技术部备注"]}${i}`]?.v;

    let process_flow = process_flow_keys.reduce((accumulator, current) => {
      if (sheet[`${head[current]}${i}`]?.v == "√") {
        accumulator += current.split("-")[1] + ";";
      }
      return accumulator;
    }, "");
    // 排除没有勾工序的行
    if (process_flow)
      process_flow = process_flow.substring(0, process_flow.length - 1);
    else continue

    let item = {  customer,  project_name,  order_no, delivery_date, serial_no,  part_name,draw_suffix,  material_model,material_no,  thick,  width,  length,  piece,
       count,  area,  sum_area,  weight,  sum_weight, spray_color, remark,  process_flow};


    let parent = items.find((e) => serial_no.startsWith(e.serial_no));
    if (parent) {
      item.parent_material_no = parent.material_no
      item.parent_serial_no = parent.serial_no
      if(!parent['children']) parent.children = []
      parent.children.push(item)
    }
    items.push(item);
  }
  items.reverse()// 逆序
  $.ListView.Post("custom_import", { ObjectIds: DataAdapter(items) }, (resp) => {
    if(callback && typeof callback === 'function') {
      callback(resp)
    }
  }, () => {callback({ErrorMessage:'请求失败'}) }, false);
}

function DataOptimize(items, root){

  for(let item of items){
    let {children} = item
    if(children && children.length > 0) {
      DataOptimize(children, item)
    }

    if(!root) continue
    let proportion = Math.floor(parseInt(item['count']) / parseInt(root['count']) * 10)/ 10
    root['area'] = parseFloat(root['area'] || 0) + parseFloat(item['area']) * proportion
    root['weight'] = parseFloat(root['weight'] || 0) + parseFloat(item['weight']) * proportion
  }
}
function CalcAreaAdWeight(children, root){
  let count = parseInt(root['count'])
  for(let child of children){
    if(child.children) CalcAreaAdWeight(child.children, child)
    let sub_count = parseInt(child['count'])
    sub_count/count
  }
}

function ExtractProcessFlow(head) {
  let process_flows = Object.keys(head).filter((key) =>
    key.startsWith("工序流转分工卡-")
  );
  process_flows.sort((a, b) => {
    let x = revertCode(head[a]);
    let y = revertCode(head[b]);
    if (x < y) return -1;
    else if (x > y) return 1;
    return 0;
  });
  return process_flows;
}

function DataAdapter(items) {
  DataOptimize(items)
  let result = [];
  for (let item of items) {
    let data = {};
    data["SerialNo"] = item.serial_no
    data["ParentSerialNo"] = item.parent_serial_no
    data["OrderNo"] = item.order_no;
    data['DeliveryDate'] = item.delivery_date
    data["ParentMaterialNo"] = item.parent_material_no
    data["MaterialNo"] = item.material_no;
    data["Customer"] = item.customer;
    data["ProjectName"] = item.project_name;
    data["MaterialName"] = item.part_name;
    data["DrawSuffix"] = item.draw_suffix;
    data["MaterialModel"] = item.material_model;
    data["BlankingHeight"] = item.thick;
    data["BlankingLength"] = item.length;
    data["BlankingWidth"] = item.width;
    data["Piece"] = item.piece;
    data["Count"] = item.count;
    data["SprayingArea"] = item.area;
    data["SprayingSumArea"] = item.sum_area;
    data["NetWeight"] = item.weight;
    if(item.sum_weight && parseFloat(item.sum_weight)) {
      data["NetSumWeight"] = item.sum_weight;
    } else {
      data["NetSumWeight"] = parseInt( item.count) * parseFloat(item.weight)
    }

    data["Remark"] = item.remark;
    data["SprayColor"] = item.spray_color;
    data["ProcessFlow"] = item.process_flow;

    result.push(JSON.stringify(data));
  }
  return result;
}
