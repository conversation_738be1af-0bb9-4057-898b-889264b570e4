var cpk = "ge_ua_p"
var step = "prev";
var nonce = 8300;
function loadFunc() {
    var e = document.cookie;
    if (null != e) {
        for (
            var t = e.toString().split(";"), n = "", o = 0;
            o < t.length;
            o++
        ) {
            var i = (e = t[o].trim()).split("=");
            if (2 == i.length && i[0] == cpk) {
                n = i[1];
                break;
            }
        }
        if (0 != n.length) {
            for (var a = 0, o = 0; o < n.length; o++) {
                var d = n[o];
                /^[a-zA-Z0-9]$/.test(d) && (a += n.charCodeAt(o) * (nonce + o));
            }
            var r = window.location.toString(),
                l = new XMLHttpRequest();
            l.open("POST", r, !0),
                l.setRequestHeader(
                    "Content-type",
                    "application/x-www-form-urlencoded"
                ),
                l.setRequestHeader("X-GE-UA-Step", step),
                (l.onreadystatechange = function () {
                    4 == this.readyState &&
                        200 == this.status &&
                        window.setTimeout(function () {
                            window.location.reload();
                        }, 5e3);
                }),
                l.send("sum=" + a + "&nonce=" + nonce);
        }
    } else
        window.setTimeout(function () {
            window.location.reload();
        }, 5e3);
}
window.addEventListener
    ? window.addEventListener("load", loadFunc)
    : (window.onload = loadFunc);
