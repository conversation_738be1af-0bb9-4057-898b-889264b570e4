Dim count
count = 6

Set ws = CreateObject("Wscript.Shell")
ws.run "D:\Software\Chat\Wechat\WeChat.exe"

' 连接到本地计算机的 WMI 服务
Set objWMIService = GetObject("winmgmts:\\.\root\cimv2")


Do While count > 0
    ' 在此处编写要执行的代码
    count = count - 1
    WScript.Sleep 5000
    ' 查询进程
    Set colProcess = objWMIService.ExecQuery("Select * from Win32_Process Where Name = 'wechat.exe'")

    ' 检查进程是否存在
    If colProcess.Count > 0 Then
        ws.run "python start_wechat.py"
        Exit Do
    End If

Loop
