
using System;
using System.Collections.Generic;
using System.Text;
using H3;


public class Swl2tuysgxtp4164gsqyouwo25_ListViewController : H3.SmartForm.ListViewController
{
    public Swl2tuysgxtp4164gsqyouwo25_ListViewController(H3.SmartForm.ListViewRequest request) : base(request)
    {
    }

    protected override void OnLoad(H3.SmartForm.LoadListViewResponse response)
    {
        base.OnLoad(response);
    }

    protected override void OnSubmit(string actionName, H3.SmartForm.ListViewPostValue postValue, H3.SmartForm.SubmitListViewResponse response)
    {
        // 批量更新草稿状态
        if (actionName == "batch_submit")
        {
            string[] objectIds = this.Deserialize<string[]>(this.Request["ObjectIds"]);
            if (objectIds == null || objectIds.Length == 0)
            {
                response.Errors.Add("请选择草稿数据");
                return;
            }

            foreach (string objectId in objectIds)
            {
                H3.DataModel.BizObject order = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId,
                                     this.Engine, "Swl2tuysgxtp4164gsqyouwo25", objectId, false);
                if (order != null && order.Status == H3.DataModel.BizObjectStatus.Draft) // 草稿状态订单数据
                {
                    order.Status = H3.DataModel.BizObjectStatus.Effective; //设置状态生效
                    order.Update();
                    string worflowId = order.WorkflowInstanceId;//流程id

                    if (string.IsNullOrEmpty(worflowId))
                    {
                        string instanceId = System.Guid.NewGuid().ToString(); // 创建流程id
                        order.WorkflowInstanceId = instanceId;
                    }
                    H3.Workflow.Instance.WorkflowInstance wfInstance = this.Request.Engine.WorkflowInstanceManager.GetWorkflowInstance(order.WorkflowInstanceId);
                    if (wfInstance == null)
                    {
                        //发起流程
                        string workItemID = string.Empty;
                        string errorMsg = string.Empty;
                        H3.Workflow.Template.WorkflowTemplate wfTemp = this.Request.Engine.WorkflowTemplateManager.GetDefaultWorkflow(order.Schema.SchemaCode);
                        this.Request.Engine.Interactor.OriginateInstance(this.Request.UserContext.UserId, order.Schema.SchemaCode,
                            wfTemp.WorkflowVersion, order.ObjectId, order.WorkflowInstanceId, H3.Workflow.WorkItem.AccessMethod.Web,
                            true, // 是否提交流程操作
                            string.Empty, true, out workItemID, out errorMsg);
                    }
                }
            }

            response.Message = "操作成功";
            response.Refresh = true;
            return;
        }

        if (actionName == "batch_collect") //批量收款
        {

            string[] objectIds = this.Deserialize<string[]>(this.Request["ObjectIds"]);
            if (objectIds == null || objectIds.Length == 0)
            {
                response.Errors.Add("请选择订单数据");
                return;
            }
            //查询所有订单数据
            H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();
            filter.Matcher = new H3.Data.Filter.ItemMatcher("ObjectId", H3.Data.ComparisonOperatorType.In, objectIds);

            H3.DataModel.BizObject[] orders = H3.DataModel.BizObject.GetList(this.Engine, this.Request.UserContext.UserId,
                this.Engine.BizObjectManager.GetPublishedSchema("Swl2tuysgxtp4164gsqyouwo25"),
                H3.DataModel.GetListScopeType.GlobalAll, filter);
            if (!validOrders(orders, response))
            {
                return;
            }

            string customerId = orders[0]["F0000002"] + string.Empty;//客户id
            H3.DataModel.BizObject customer = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId,
                                                 this.Engine, "Sahj8417jpmkya2ndghgvorg83", customerId, false);
            if (customer == null)
            {
                response.Errors.Add("客户信息不存在");
                return;
            }

            //创建收款单
            H3.DataModel.BizObjectSchema collectOrderSchema = this.Engine.BizObjectManager.GetPublishedSchema("Sxgkxqq8chj7actq7olsqwexq5");
            H3.DataModel.BizObject collectOrder = new H3.DataModel.BizObject(this.Engine, collectOrderSchema, this.Request.UserContext.UserId);
            List<H3.DataModel.BizObject> saleOrdersOfCollect = new List<H3.DataModel.BizObject>();

            foreach (H3.DataModel.BizObject order in orders)
            {
                // 子表销售订单
                H3.DataModel.BizObject saleOrder = new H3.DataModel.BizObject(this.Engine,
                                  collectOrderSchema.GetChildSchema("Fu66y4m3b3yh5e9bf49v14amo3"), this.Request.UserContext.UserId);
                saleOrder["F0000027"] = order["ObjectId"]; // objectid
                saleOrder["F0000028"] = order["F0000052"]; //订单金额
                saleOrder["F0000029"] = order["F0000007"];//待汇款金额
                saleOrder["F0000055"] = order["F0000097"];//订单状态
                saleOrdersOfCollect.Add(saleOrder);
            }
            collectOrder["Fu66y4m3b3yh5e9bf49v14amo3"] = saleOrdersOfCollect.ToArray(); //销售订单
            collectOrder["F0000016"] = "销售订单"; //类型

            collectOrder["F0000003"] = customerId;//客户信息
            collectOrder["F0000018"] = customer["F0000009"];//发票抬头
            collectOrder["F0000019"] = customer["F0000010"];//税号
            collectOrder["F0000020"] = customer["F0000011"];//开户行
            collectOrder["F0000021"] = customer["F0000012"];//银行账号
            collectOrder["F0000022"] = customer["F0000021"];//开票地址
            collectOrder["F0000023"] = customer["F0000020"];//电话
            collectOrder.Create();

            response.Message = "操作成功";
            response.Refresh = true;
            return;
        }
        base.OnSubmit(actionName, postValue, response);
    }

    private bool validOrders(H3.DataModel.BizObject[] orders, H3.SmartForm.SubmitListViewResponse response)
    {
        if (orders != null && orders.Length > 0)
        {
            string customerId = ""; // 客户id
            foreach (H3.DataModel.BizObject order in orders)
            {
                if (order.Status != H3.DataModel.BizObjectStatus.Effective)
                {
                    response.Errors.Add("存在订单未结束");
                    return false;
                }

                if (string.IsNullOrEmpty(customerId))
                {
                    customerId = order["F0000002"] + string.Empty;//客户id
                }
                if (customerId != (order["F0000002"] + string.Empty))
                {
                    response.Errors.Add("存在不同客户订单");
                    return false;
                }
            }
            return true;
        }
        response.Errors.Add("选取订单不存在");
        return false;
    }
}
