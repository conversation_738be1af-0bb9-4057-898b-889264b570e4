FROM arm64v8/node:alpine3.12

LABEL AUTHOR="ye" \
      VERSION=1.0.0

ENV DEFAULT_LIST_FILE=crontab_list.sh \
    CUSTOM_LIST_MERGE_TYPE=append \
    COOKIES_LIST=/scripts/logs/cookies.list \
    REPO_URL=ssh://***************************:43001/own/jd_scripts.git \
    REPO_BRANCH=master

ADD id_rsa /root/.ssh/
ADD known_hosts /root/.ssh/

RUN apk update \
    && apk  upgrade --update \
    && apk add --no-cache bash tzdata git moreutils curl jq openssh-client unzip \
    && ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone

RUN chmod 600 /root/.ssh/* \
    && ssh-keygen -f ~/.ssh/id_rsa -y > ~/.ssh/id_rsa.pub \
    && git clone -b $REPO_BRANCH $REPO_URL /scripts \
    && cd /scripts \
    && mkdir logs \
    && npm install \
    && cp /scripts/docker/docker_entrypoint.sh /usr/local/bin \
    && chmod +x /usr/local/bin/docker_entrypoint.sh \
    && rm -rf /var/cache/apk/*

WORKDIR /scripts

ENTRYPOINT ["docker_entrypoint.sh"]

CMD [ "crond" ]