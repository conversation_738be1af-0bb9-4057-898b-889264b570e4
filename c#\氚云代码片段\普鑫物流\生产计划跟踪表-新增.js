/* 控件接口说明：
 * 1. 读取控件: this.***,*号输入控件编码;
 * 2. 读取控件的值： this.***.GetValue();
 * 3. 设置控件的值： this.***.SetValue(???);
 * 4. 绑定控件值变化事件： this.***.Bind<PERSON>hange(key,function(){})，key是定义唯一的方法名;
 * 5. 解除控件值变化事件： this.***.UnbindChange(key);
 * 6. CheckboxList、DropDownList、RadioButtonList: $.***.AddItem(value,text),$.***.ClearItems();
 */
/* 公共接口：
 * 1. ajax：$.SmartForm.PostForm(actionName,data,callBack,errorBack,async),
 *          actionName:提交的ActionName;data:提交后台的数据;callback:回调函数;errorBack:错误回调函数;async:是否异步;
 * 2. 打开表单：$.IShowForm(schemaCode, objectId, checkIsChange)，
 *          schemaCode:表单编码;objectId;表单数据Id;checkIsChange:关闭时，是否感知变化;
 * 3. 定位接口：$.ILocation();
 */
(function (window) {
  function insertHeadStyle(css, className = "") {
    if (className) {
      if (document.querySelector(className)) return;
    }
    let headFrag = document.createDocumentFragment();
    headFrag.appendChild(createCSSNode(css, className));
    document.head.appendChild(headFrag);
  }
  function insertScript(src) {
    let script = document.createElement("script");
    script.type = "text/javascript";
    script.src = src;
    document.body.appendChild(script);
  }

  function createCSSNode(css, className = "", initType = "text/css") {
    let cssNode = document.createElement("style");
    if (className) {
      cssNode.className = className;
      const xclass = "." + className.split(" ").join(".");
      cssNode.dataset.xclass = xclass;
    }

    cssNode.setAttribute("type", initType);
    cssNode.appendChild(document.createTextNode(css));

    return cssNode;
  }
  function querySelector(selector, all = false) {
    let max = 50,
      count = 0;
    function query(resolve) {
      setTimeout(() => {
        let e;
        if (all) e = document.querySelectorAll(selector);
        else e = document.querySelector(selector);
        if (count > max) resolve(undefined);
        if (e) {
          resolve(e);
        } else {
          count++;
          query(resolve);
        }
      }, 100);
    }
    return new Promise((resolve) => {
      query(resolve);
    });
  }

  function SheetOperator(form, code) {
    this.code = code;
    this.form = form;
    this.sheet = form[code];
    insertHeadStyle(`.code-check-warn {display:none} `, "code-check");
  }
  SheetOperator.prototype.AddButton = async function (options) {
    if (!options || options.length === 0) return;
    let actions_element = await querySelector(
      `#${this.code} .subgrid-toolbar__actions`
    );
    if (!actions_element) return;
    let sheet = this.sheet,
      form = this.form;
    for (let option of options) {
      if (option.type === "import") {
        let import_element = actions_element.querySelector(".custom_import");
        if (import_element) continue;
        insertHeadStyle(
          `.custom_import {display:inline-block;}`,
          "custom-import"
        );
        insertScript(
          `https://cdn.sheetjs.com/xlsx-0.20.0/package/dist/xlsx.full.min.js`
        );

        let import_button = document.createElement("div");
        import_button.className = "custom_import";
        import_button.innerHTML = `
                    <button type="button" class="h3-button ant-btn ant-btn-default"><i  class="h3yun_All download-o"></i><span style="margin-left:8px">导入</span></button>
                    <input type="file" style="display:none"></input>
                    <a style="margin-left:8px;font-size:12px;color: #107fff;vertical-align: bottom;"><i class="h3yun_All download-o"></i>下载导入模板</a>
                `;
        let input = import_button.querySelector("input");
        input.addEventListener("change", (event) => {
          let files = event.target.files;
          if (!files || files.length === 0) return;
          let file = files[0];

          let reader = new FileReader();
          reader.onload = function (e) {
            let wb = XLSX.read(e.target.result);
            if (option.click && typeof option.click === "function") {
              option.click(wb, sheet, form);
            }
          };
          reader.readAsArrayBuffer(file);
        });
        let button = import_button.querySelector("button");
        button.onclick = () => {
          input.click();
        };
        actions_element.appendChild(import_button);
      }
    }
  };
  window.SheetOperator = SheetOperator
})(window);
// 表单插件代码
$.extend($.JForm, {
  // 加载事件
  OnLoad: async function () {
    let code = "D282605st0gkjyen40hmwl7djv";
    let operator = new SheetOperator(this, code)
    operator.AddButton([
        {
            type: 'import',
            click: (xlsx, sheet, form) => {
                console.log(xlsx, sheet, form, '------')
            }
        }
    ])

  },

  // 按钮事件
  OnLoadActions: function (actions) {},

  // 提交校验
  OnValidate: function (actionControl) {
    return true;
  },

  // 提交前事件
  BeforeSubmit: function (action, postValue) {},

  // 提交后事件
  AfterSubmit: function (action, responseValue) {}
});
