version: '3.7'
services:
  nginx:
    image: nginx:alpine
    ports:
      - 80:80
    environment:
      - TZ=Asia/Shanghai
    configs:
      - source: nginx_config
        target: /etc/nginx/nginx.conf
    volumes:
      - conf_d:/etc/nginx/conf.d
      - html:/usr/share/nginx/html
      
configs:
  nginx_config:
    external: true
    
volumes:
  conf_d:
    driver_opts:
      device: ":/DataVolume/nginx/conf.d"
      o: "addr=192.168.1.210,rw,nolock"
      type: "nfs"
  html:
    driver_opts:
      device: ":/DataVolume/nginx/html"
      o: "addr=192.168.1.210,rw,nolock"
      type: "nfs"