version: "3"
services:
  redis_dev:
    image: redis
    container_name: redis_dev
    command: redis-server /usr/local/etc/redis/redis.conf
    ports:
      - "6379:6379"
    volumes:
      - /root/docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    restart: always

  redis_tst:
    image: redis
    container_name: redis_tst
    command: redis-server /usr/local/etc/redis/redis.conf
    ports:
      - "6380:6379"
    volumes:
      - /root/docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    restart: always
