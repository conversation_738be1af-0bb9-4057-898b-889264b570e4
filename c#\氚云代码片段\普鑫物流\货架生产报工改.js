/*
* $.ListView.GetSelected()获取选中的记录
* $.ListView.RefreshView()刷新列表
* $.ListView.Post()请求后台
* $.ListView.InitQueryItems()修改过滤条件
* $.ListView.RefreshView()刷新页面
* $.ListView.ActionPreDo() 按钮执行之前的事件
*/
!( function( window ) {
    function DocumentApi() {
        this.doc = document
    }
    DocumentApi.prototype.findSelector = function( selector, element ) {
        if( !selector ) return
        if( !element ) element = this.doc.body
        if( element.className && ( element.className + "" ).indexOf( selector ) >= 0 )
            return element
        if( element.id == selector ) return element

        let children = element.children

        if( children && children.length > 0 ) {
            for( let child of children ) {
                let e = this.findSelector( selector, child )
                if( e ) return e
            }
        }
    }

    DocumentApi.prototype.insertHeadStyle = function( css, className = "" ) {
        if( this.findSelector( className, this.doc.head ) ) return
        let headFrag = this.doc.createDocumentFragment()
        headFrag.appendChild( this.createCSSNode( css, className ) )
        this.doc.head.appendChild( headFrag )
    }
    DocumentApi.prototype.insertScript = function( src ) {
        let script = this.doc.createElement( "script" )
        script.type = "text/javascript"
        script.src = src
        this.doc.body.appendChild( script )
    }

    DocumentApi.prototype.createCSSNode = function( css, className = "", initType = "text/css" ) {
        let cssNode = this.doc.createElement( "style" )
        if( className ) {
            cssNode.className = className
            const xclass = "." + className.split( " " ).join( "." )
            cssNode.dataset.xclass = xclass
        }

        cssNode.setAttribute( "type", initType )
        cssNode.appendChild( this.doc.createTextNode( css ) )
        return cssNode
    }

    DocumentApi.prototype.createElement = function( content ) {
        return this.doc.createElement( content )
    }

    DocumentApi.prototype.createElementHtml = function( content ) {
        let node = this.doc.createElement( "div" )
        node.innerHTML = content
        return node.children[ 0 ]
    }

    DocumentApi.prototype.download = function( url, saveName ) {
        if( typeof url == 'object' && url instanceof Blob ) {
            url = URL.createObjectURL( url ) // 创建blob地址
        }
        var aLink = this.doc.createElement( 'a' )
        aLink.href = url
        aLink.download = saveName || '' // HTML5新增的属性，指定保存文件名，可以不要后缀，注意，file:///模式下不会生效
        var event
        if( window.MouseEvent ) event = new MouseEvent( 'click' )
        else {
            event = document.createEvent( 'MouseEvents' )
            event.initMouseEvent( 'click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null )
        }
        aLink.dispatchEvent( event )
    }
    window.docApi = new DocumentApi()
    docApi.insertHeadStyle( ".code-check-warn {display:none} ", "code-check" )
})( window );
!( function( window ) {
    function ExcelJSApi() {
        if( !window[ "ExcelJS" ] ) {
            docApi.insertScript(
                "https://www.h3yun.com/Form/DoPreview/?AttachmentID=9d8b3475-35c9-4a45-9a86-2251513b68f5"
            )
        }
    }
    ExcelJSApi.prototype.loadTemplate = async function( template ) {
        let data = await this.getTemplate( template )
        let excel = await this.getExcel()
        let wb = new excel.Workbook()
        await wb.xlsx.load( data )
        this.workbook = wb
        return wb
    }

    ExcelJSApi.prototype.getTemplate = function( template ) {
        return new Promise(( resolve, reject ) => {
            window.$.ajax( {
                type: "POST",
                url: "/App/OnAction/",
                data: { PostData: JSON.stringify( { Command: "Download", ActionName: "DoAction", QueryCode: "D2826050ff2122070c84a41be2f42c1ac623b4a", ObjectId: template }) },
                dataType: "json",
                async: false,
                success: function( resp ) {
                    if( resp && resp.Successful ) {
                        let data = resp ?.ReturnData ?.Response ?.ReturnData
            if ( !data ) return resolve()
                        window.axios.get( data[ "Url" ], { responseType: "blob" })
                            .then(( resp ) => resolve( resp ) )
                            .catch(( e ) => resolve() )
                    } else {
                        resolve()
                    }
                }
            })
        })
    }
    ExcelJSApi.prototype.getExcel = function() {
        let count = 1
        function wait( resolve ) {
            if( window[ 'ExcelJS' ] ) resolve( window[ 'ExcelJS' ] )
            else {
                if( count > 50 ) resolve()
                else {
                    count++
                    setTimeout(() => {
                        wait( resolve )
                    }, 100 )
                }
            }
        }
        return new Promise(( resolve ) => {
            wait( resolve )
        })
    }
    ExcelJSApi.prototype.writeExcel = async function( filename ) {
        let wbout = await this.workbook.xlsx.writeBuffer()
        let blob = new Blob( [ wbout ], {
            type: "application/octet-stream"
        })

        docApi.download( blob, filename )
    }
    ExcelJSApi.prototype.removeRows = async function( sheet, from = 1, to ) {
        if( !to ) to = sheet.rowCount
        for( let i = from;i <= to;i++ ) {
            sheet.spliceRows( from, 1 )
        }
    }

    ExcelJSApi.prototype.readExcel = function( file ) {
        let _this = this
        return new Promise(( resolve ) => {
            let reader = new FileReader();
            reader.onload = async function( e ) {
                let excel = await _this.getExcel();

                if( excel ) {
                    let wb = new excel.Workbook()
                    await wb.xlsx.load( e.target.result )
                    resolve( wb );
                } else {
                    $.IShowError( "读取Excel失败" );
                }
            };
            reader.readAsArrayBuffer( file );
        });
    }

    window.excelJSApi = new ExcelJSApi()
})( window );

!( function( window ) {
    function ErrorTip() {
        docApi.insertHeadStyle( `
    .error-modal {}
    .error-table {
      display:flex;
      flex-direction: column;
      width: 100%;
      border: thin solid #e5e5e5;
      margin-top:10px;
    }
    .error-table .error-table-head{
      font-weight: bold;
    }
    .error-table .error-table-head,.error-table .error-table-body{
      display:flex;
      flex:1;
    }
    .error-table .error-table-body {
      flex-direction: column;
    }
    .error-table .error-table-head > div, .error-table .error-table-body .error-table-row > div{
      flex:1;
      padding: 5px;
      word-wrap: break-word;
      word-break: break-all;
    }
    .error-table .error-table-head > div:last-child, .error-table .error-table-body .error-table-row > div:last-child{
      flex:2;
    }
    .error-table .error-table-head > div:not(:last-child) {
      border-right: thin solid #e5e5e5;
    }
    .error-table .error-table-body .error-table-row {
      border-top: thin solid #e5e5e5;
    }
    .error-table .error-table-body .error-table-row > div:not(:last-child) {
      border-right: thin solid #e5e5e5;
    }
    .error-table .error-table-body .error-table-row {
      display:flex;
    }
    `, 'custom-error-tip' )
    }

    ErrorTip.prototype.TableError = function( config, data ) {
        if( !config || !config.headers ) return

        let $error = docApi.createElement( 'div' )
        $error.classList.add( 'error-table' )

        $error.innerHTML = ""
        $error.style.display = "block"

        let header = '<div class="error-table-head">'
        for( let head of config.headers ) {
            let style = head.head_style || head.style
            header += `<div ${ style ? 'style="' + style + '"' : '' }>${ head.name }</div>`
        }
        header += '</div>'

        let body = '<div class="error-table-body">'
        for( let item of data ) {
            let row = `<div class="error-table-row">`
            for( let head of config.headers ) {
                let style = head.row_style || head.style
                row += `<div ${ style ? 'style="' + style + '"' : '' }>${ item[ head.key ] }</div>`
            }
            row += '</div>'
            body += row
        }
        body += '</div>'
        $error.innerHTML = header + body

        return $error
    }

    ErrorTip.prototype.ShowTableError = function( config, data ) {
        let $error = this.TableError( config, data )
        let $modal = window[ '$' ].IModal( {
            Title: config.title,
            Content: $error,
            ShowFooter: false,
            ToolButtons: [],
            Class: 'error-modal',
            Width: "80%"
        });
    }
    ErrorTip.prototype.ShowError = function( message ) {
        window[ '$' ].IShowError( message );
    }

    window.errorTip = new ErrorTip()
})( window );
!( function( window ) {
    function Upload( template ) {
        this.template = template
        docApi.insertHeadStyle( `
        .import-step1-content .import-card {
            position: relative;
            padding: 12px 20px 8px;
            color: #304265;
        }
        .import-step1-content .import-card .template-download {
            height: 20px;
            line-height: 20px;
            margin-bottom: 4px;
            font-size: 12px;
        }
        .import-step1-content .import-card .template-download span {
            margin-left: 4px;
            color: #107fff;
            cursor: pointer;
        }
        .import-step1-content .import-card .template-download span .download-o {
            font-size: 12px;
        }
        .import-step1-content .import-card .template-download strong {
            text-decoration: none;
            font-weight: 600;
        }
        .import-step1-content .import-card .upload-file {
            position: relative;
        }
        .import-step1-content .import-card .upload-file .h3-upload-helper {
            display: block;
            height: 106px;
            padding: 16px 24px;
            text-align: center;
            border: 1px dashed #c9ccd8;
            border-radius: 8px;
            background-color: #f3f5f8;
        }
        .import-step1-content .import-card .upload-file .h3-upload-helper .h3-upload-dragger {
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        .import-step1-content .import-card .upload-file .selectfile-trigger {
            display: inline-block;
            line-height: 22px;
            margin: 0 0 8px;
            color: #107fff;
        }
        .import-step1-content .import-card .upload-file .selectfile-trigger .cloud-upload-o {
            font-size: 20px;
            vertical-align: bottom;
            margin-right: 4px;
        }
        .import-step1-content .h3-upload__input {
            display: none;
        }

        .import-step1-content .import-card-title {
            position: relative;
            line-height: 22px;
            padding-left: 7px;
            margin-bottom: 8px;
            font-weight: 600;
        }
        .import-step1-content .import-card-title:before {
            content: "";
            position: absolute;
            left: 0;
            top: 50%;
            width: 3px;
            height: 12px;
            transform: translateY(-50%);
            background-color: #107fff;
            border-radius: 2px;
        }
        .import-step1-content .upload-file-list .upload-file-item {
            display: flex;
            justify-content: space-between;
            margin-top: 5px;
            padding: 5px;
            border: 1px solid #107fff;
            align-items: center;
            border-radius: 4px;
            background-color: rgba(16,127,255,.1);
        }
        .import-step1-content .upload-file-list .upload-file-item  .upload-file-item-close{
            speak: none;
            font-style: normal;
            font-weight: 400;
            font-variant: normal;
            text-transform: none;
            font-size:12px;
            line-height: 1;
            color: #000;
            font-family: icomoon!important;
            pointer: cursor;
        }
        .import-step1-content .upload-file-list .upload-file-item  .upload-file-item-close:before{
            content: "\\E059";
        }
        .custom_upload .ant-btn-right{
            float:right;
            margin-left: 10px;
        }
        `, 'custom-upload' )
    }

    Upload.prototype.downloadTemplate = function( template ) {
        $.ajax( {
            type: "POST",
            url: "/App/OnAction/",
            data: {
                PostData: JSON.stringify( { "Command": "Download", "ActionName": "DoAction", "QueryCode": "D2826050ff2122070c84a41be2f42c1ac623b4a", "ObjectId": template })
            },
            dataType: "json",
            async: false,
            success: function( resp ) {
                if( resp && resp.Successful ) {
                    let data = resp ?.ReturnData ?.Response ?.ReturnData
                    if ( !data ) return
                    docApi.download( data[ 'Url' ] )
                }
            }
        })
    }
    Upload.prototype.createUpload = function() {
        let upload = docApi.createElementHtml( `
      <div class="import-step1-content">
       <div class="import-card">
          <div class="import-card-title">上传文件</div>
          <div class="import-content">
              <p class="template-download">请先<span class="download download-template"><i class="h3yun_All download-o"></i>下载导入模板</span>修改后上传； 请勿修改模板表格的【<strong>字段标题</strong>】，防止导入失败</p>
              <div class="h3-upload upload-file">
                  <div class="h3-upload-helper h3-upload--text is-dragger">
                      <div class="h3-upload-dragger">
                          <span class="selectfile-trigger"><i class="h3yun_All cloud-upload-o"></i>点击选择文件上传</span>
                          <p style="font-size: 12px; line-height: 20px; margin: 0px;">支持格式：xls或xlsx，大小不超过20M，数据最多不超过10000行、200列</p>
                      </div>
                  </div>
                  <input type="file" name="file" accept=".xls,.xlsx" class="h3-upload__input">
              </div>
              <div class="upload-file-list"></div>
          </div>
          <div class="validate-error"></div>
      </div>
      </div>`)
        let $btn = docApi.findSelector( "upload-file", upload )
        let $file = docApi.findSelector( "upload__input", upload )
        let $file_list = docApi.findSelector( "upload-file-list", upload )
        let $download = docApi.findSelector( "download-template", upload )
        let $error = docApi.findSelector( "validate-error", upload );

        let _this = this
        $download.onclick = function() { _this.downloadTemplate( _this.template ) }

        $btn.onclick = function() { $file.click() }

        $file.onchange = function( event ) {
            $error.innerHTML = ''
            let file = $file.files[ 0 ]
            $file_list.innerHTML = ""

            let $file_item = docApi.createElementHtml(
                `<div class="upload-file-item"><div class="upload-file-item-title">${ file.name }</div><div class="upload-file-item-close"></div></div>`
            )
            let $close = docApi.findSelector( "upload-file-item-close", $file_item )
            $close.onclick = () => {
                $file_list.innerHTML = ""
                $file.files.length = 0
            }
            $file_list.appendChild( $file_item )
            _this.value = file
            event.target.value = null
        }
        this.$error = $error
        this.$upload = upload
        return {
            upload,
            $error
        }
    }
    Upload.prototype.putError = function( error ) {
        this.$error.innerHTML = ''
        this.$error.appendChild( error )
    }
    Upload.prototype.load = function( callback, config ) {
        let _this = this
        this.createUpload()
        let $modal = window.$.IModal( {
            Title: "导入",
            Content: _this.$upload,
            Class: 'custom_upload',
            ToolButtons: [
                {
                    Text: "取消",
                    Theme: "ant-btn  ant-btn-right",
                    CallBack: () => {
                        $modal.hide()
                    }
                },
                {
                    Text: "提交",
                    Theme: "ant-btn ant-btn-primary ant-btn-right",
                    CallBack: async () => {
                        let file = _this.value
                        if( !file ) {
                            $.IShowError( "", "请上传文件" )
                            return
                        }
                        $modal.SetButtonLoading()
                        if( callback && typeof callback == 'function' ) {
                            let result = await callback( file, _this )
                            $modal.ResetButton()
                            if( result ) {
                                $modal.hide()
                            }
                        }
                    }
                }
            ],
            Width: "60%"
        })
        $modal.SetButtonLoading = function() {
            for( var e = 0;e < $modal.ActionObjects.length;e++ ) {
                let item = $modal.ActionObjects[ e ]
                if( !item || !item.Element ) continue
                if( item.Text == '提交' ) {
                    item.Element.attr( "data-loading-text", "处理中" )
                } else {
                    item.Element.attr( "data-loading-text", item.Text )
                }
                item.Element.button( "loading" )
            }
        }
    }
    window.Upload = Upload
})( window );
!( function( window ) {
    function H3YunApi() { }

    H3YunApi.prototype.getTemplate = async function( template ) {
        return new Promise(( resolve, reject ) => {
            window.$.ajax( {
                type: "POST",
                url: "/App/OnAction/",
                data: { PostData: JSON.stringify( { Command: "Download", ActionName: "DoAction", QueryCode: "D2826050ff2122070c84a41be2f42c1ac623b4a", ObjectId: template }) },
                dataType: "json",
                async: false,
                success: function( resp ) {
                    if( resp && resp.Successful ) {
                        let data = resp ?.ReturnData ?.Response ?.ReturnData
                        if ( !data ) return resolve()
                        window.axios.get( data[ "Url" ], { responseType: "blob" })
                            .then(( resp ) => resolve( resp ) )
                            .catch(( e ) => resolve() )
                    } else {
                        resolve()
                    }
                }
            })
        })
    }

    H3YunApi.prototype.Submit = function( schemaCode, body ) {
        return new Promise(( resolve, reject ) => {
            let value = {
                "Command": "Submit",
                "Data": body
            }
            let PostData = JSON.stringify( {
                "ActionName": "DoAction",
                "Command": "Submit",
                "PostValue": JSON.stringify( value ),
                "SchemaCode": schemaCode,
                "Mode": "Create",
                "BizObjectId": '',
                "IsExternalForm": "False",
                "IsExternalShare": "False"
            })
            window.axios.post( '/form/OnAction', { PostData }).then( resp => resolve( resp ) )
                .catch(() => resolve( { ErrorMessage: '请求失败', Successful: false }) )
        })
    }

    H3YunApi.prototype.Load = async function( schemaCode ) {
        try {
            let PostData = JSON.stringify( {
                "ActionName": "Load",
                "SchemaCode": schemaCode,
                "BizObjectId": "",
                "SideModal": true, "WorkItemID": "",
                "IsExternalForm": false,
                "IsExternalShare": false,
                "ParentBizObjectId": "", "IsNewForm": true,
                "ddIngPid": "", "ddIngTid": "",
                "TimeStamp": -1
            })
            let resp = await window.axios.post( '/form/OnAction', { PostData })
            return resp
        } catch( e ) {
            return { ErrorMessage: e.message, Successful: false }
        }

    }
    window.h3yunApi = new H3YunApi()

})( window );


$.ListView.ActionPreDo = function( actionCode ) {

    if( actionCode == 'custom_import' ) {
        let upload = new Upload( "ReportTemplate" )
        upload.load( process )
        return false
    }
}


function buildHeadMap( sheet ) {
    let head = {}
    let row = sheet.getRow( 3 )
    row.eachCell(( cell, index ) => {
        let value = cell.value
        if( value ) {
            head[ cell.value ] = index
        }
    })
    return head
}
window.FormInfo = {
    ReportSchemaCode: 'D2826052468b3ca3f39406a80a5ee6055d35ec1',
    ReportChildSchemaCode: 'D282605Fd8a58da6e52d4efe942395b6048e06f0'
}
function findForm(form, callback) {
    for (let item of form) {
        if (callback(item)) return item
        if (item.ChildControls && item.ChildControls.length > 0) {
            let result = findForm(item.ChildControls, callback)
            if (result) return result
        }
    }
}
async function validTemplate( sheet ) {
    if( !sheet ) throw new Error( "无法读取模板" )
    let resp = await h3yunApi.Load( FormInfo.ReportSchemaCode )
    if( !resp || !resp.Successful || !resp.ReturnData ) throw new Error( "模板校验失败" )

    let {FormContent} = resp.ReturnData
    let form = JSON.parse( FormContent )

    let teamField = sheet.getCell( 'B2' ).value
    let team = findForm(form, e => e.Key == 'Team' )
    if( !team ) throw new Error( "模板解析错误" )
    if( team.Options.DisplayName != teamField ) throw new Error( `模板描述项：[ ${ team.Options.DisplayName } ] 不存在` )
    let reportDateField = sheet.getCell( 'D2' ).value
    let reportDate = findForm(form, e => e.Key == 'ReportDate' )
    if( !reportDate ) throw new Error( "模板解析错误" )
    if( reportDate.Options.DisplayName != reportDateField ) throw new Error( `模板描述项：[ ${ reportDate.Options.DisplayName } ]不存在` )

    let children = findForm(form, e => e.Key == FormInfo.ReportChildSchemaCode )
    if( !children || children.length == 0 ) throw new Error( "模板解析错误" )
    let row = sheet.getRow( 3 )
    for( let control of children.ChildControls ) {
        if( control.Options.ControlKey == 'FormQuery' || control.Options.DisplayRule.Rule == 'TRUE' ) {
            continue
        }
        if( !hasInRow( row, control.Options.DisplayName ) ) {
            throw new Error( `模板非法[ ${ control.Options.DisplayName } ]列不存在` )
        }
    }
    return form;
}
function hasInRow( row, key ) {
    for( let i = 1;i <= row.cellCount;i++ ) {
        if( row.getCell( i ).value == key ) return true
    }
    return false
}


async function process( file, upload ) {
    try {
        let wb = await excelJSApi.readExcel( file )
        let sheet = wb.getWorksheet( "报工" )
        // 模板校验
        let form = await validTemplate( sheet )
        let teamField = findForm(form, e => e.Key == 'Team' )
        let head = buildHeadMap( sheet )
        let team = sheet.getCell( 'C2' ).value
        if( !team ) {
            throw new Error( "请填写申报班组" )
        }
        if( !teamField.Options.DefaultItems.includes( team ) ) {
            throw new Error( "填写报工组不存在" )
        }
        let reportDate = sheet.getCell( 'E2' ).value
        if( !reportDate ) reportDate = new Date()

        let result = { 'Team': team, 'ReportDate': reportDate }
        result[ FormInfo.ReportChildSchemaCode ] = []
        let children = findForm(form, e => e.Key == FormInfo.ReportChildSchemaCode )
        let errors = []
        for( let i = 4;i <= sheet.rowCount;i++ ) {
            let row = sheet.getRow( i )
            let item = {}
            for( let control of children.ChildControls ) {
                if( control.Options.ControlKey == 'FormQuery' || control.Options.DisplayRule.Rule == 'TRUE' ) {
                    continue
                }
                if( control.Options.DisplayName in head ) {
                    let key = control.Key.substring( FormInfo.ReportChildSchemaCode.length + 1 )
                    item[ key ] = row.getCell( head[ control.Options.DisplayName ] ).value
                }
            }

            if( !item[ 'OrderNo' ] || !item[ 'MaterialName' ] || !item[ 'DeclaredQuantity' ] ) {
                errors.push( { "Row": `第 ${ i } 行`, "Error": '订单号或产品名称或申报数量未填' })
                continue
            }
            try {
                parseInt(item[ 'DeclaredQuantity' ])
                if(item['DeclaredQuantity'] <= 0) {
                      errors.push( { "Row": `第 ${ i } 行`, "Error": '申报数量非法' })
                      continue
                }
            } catch(e){
                 errors.push( { "Row": `第 ${ i } 行`, "Error": '申报数量非数字' })
                 continue
            }

            result[ FormInfo.ReportChildSchemaCode ].push( item )
        }
        if( errors && errors.length > 0 ) {
            upload.putError( errorTip.TableError( {
                headers: [
                    { name: "行数", key: "Row" },
                    { name: "错误原因", key: "Error" },
                ]
            }, errors ) )
            return false
        }
        if( result[ FormInfo.ReportChildSchemaCode ].length == 0 ) {
            errorTip.ShowError( '模板没有数据' )
            return false
        }
        console.log('data', result)

        //let resp = await Post()
        //return processResp( resp, upload )
        let resp = await h3yunApi.Submit( FormInfo.ReportSchemaCode, result )
        return processResp( resp, upload )
    } catch( error ) {
        console.log( error )
        errorTip.ShowError( error.message || '模板校验错误' )
        return false;
    }
}

function Post(){
    return new Promise((resolve) => {
         $.ListView.Post("custom_import", { ObjectIds: "" }, (resp) => {
        if(callback && typeof callback === 'function') {
          callback(resp)
        }
      }, () => {callback({ErrorMessage:'请求失败'}) }, false);
    })
}

function processResp( resp, upload ) {
    let { Successful, ErrorMessage, ReturnData} = resp

    if( Successful ) {
        let StartFormResponse = ReturnData.StartFormResponse

        if( StartFormResponse ) {
            if( StartFormResponse.Errors && StartFormResponse.Errors.length > 0 ) {
                errorTip.ShowError( StartFormResponse.Errors[ 0 ] )
                return false
            }
            let error = StartFormResponse?.ReturnData?.Error
            if( error ) {
                errorTip.ShowError( error )
                return false
            }
            let errors = StartFormResponse?.ReturnData?.Errors
            if( errors ) {
                let errors = JSON.parse( StartFormResponse?.ReturnData?.Errors )
                upload.putError( errorTip.TableError( {
                    headers: [
                        { name: "订单号", key: "OrderNo" },
                        { name: "产品名称", key: "MaterialName" },
                        { name: "申报数量", key: "Quantity" },
                        { name: "错误原因", key: "Error" },
                    ]
                }, errors ) )
            } else {
                window[ '$' ].ListView.RefreshView()
                window[ '$' ].IShowSuccess( "上传成功" )
                return true
            }
        }

    } else {
        $.IShowError( ErrorMessage || "上传失败" )
    }
    return false
}