const sign_req = require("./sign_req");
const sm4 = require("../encrypt/SM4");

const service = require("request");
let key = "f623db4ed287bb58a0b3b02f600ced93";
let sm4_key = "43a8853b22d9af64af52fb837cd093e5";
let settle_req = {
  service: "TRADE.SETTLE",
  version: "*******",
  merId: "****************",
  tradeNo: "test1",
  tradeDate: "********",
  amount: "1",
  notifyUrl: "http://www.baidu.com",
  extra: "",
  summary: "test",
  bankCardNo: sm4.encrypt("6212264402010975823", sm4_key),
  bankCardName: sm4.encrypt("代飞", sm4_key),
  //'bankCardName':'代飞',
  bankId: "ICBC",
  bankName: "中国工商银行",
  purpose: "",
};

let quick_apply_req = {
  service: "TRADE.QUICKPAY.APPLY",
  version: "*******",
  merId: "****************",
  tradeNo: "test1",
  tradeDate: "********",
  amount: "1",
  notifyUrl: "http://www.baidu.com",
  extra: "",
  summary: "test",
  expireTime: "600",
  clientIp: "*************",
  cardType: "1",
  cardNo: sm4.encrypt("6212264402010975823", sm4_key),
  cardName: sm4.encrypt("代飞", sm4_key),
  idCardNo: sm4.encrypt("510525198811113019", sm4_key),
  mobile: sm4.encrypt("***********", sm4_key),
};

let quick_confirm_req = {
  service: "TRADE.QUICKPAY.CONFIRM",
  version: "*******",
  merId: "****************",
  opeNo: "*********",
  opeDate: "********",
  sessionID: "***************",
  dymPwd: sm4.encrypt("123456", sm4_key),
};

let params = sign_req(quick_confirm_req, key);

post(params)
  .then((data) => {
    console.log(data);
  })
  .catch((err) => {
    console.log(err);
  });

async function post(req) {
  return new Promise((resolve, reject) => {
    service.post(
      "http://*************:8080/cooperate/gateway.do",
      {
        form: req,
      },
      (err, resp, body) => {
        if (err) {
          reject(err);
        } else {
          resolve(body);
        }
      }
    );
  });
}
