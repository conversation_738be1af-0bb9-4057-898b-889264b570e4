FROM alpine:latest

COPY docker_entrypoint.sh /usr/local/bin/

ARG KEY="-----BEGIN OPENSSH PRIVATE KEY-----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\nyTnwyBSnujjFc/AAAAGnJvb3RAbG9jYWxob3N0LmxvY2FsZG9tYWlu\n-----END OPENSSH PRIVATE KEY-----"


RUN set -ex \
    && apk update \
    && apk upgrade \
    && apk add --no-cache tzdata git nodejs npm curl openssh-client openrc \
    && rm -rf /var/cache/apk/* 

RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone \
    && mkdir -p /root/.ssh \
    && echo -e $KEY > /root/.ssh/id_rsa \
    && chmod 600 /root/.ssh/id_rsa \
    && ssh-keyscan ************ > /root/.ssh/known_hosts \
    && npm config set registry https://registry.npm.taobao.org \
    && mkdir -p /scripts/logs \
    && cd /scripts \ 
    && chmod +x /usr/local/bin/docker_entrypoint.sh 
    

WORKDIR /scripts
ENTRYPOINT ["docker_entrypoint.sh"]
CMD [ "crond" ] 
