{
	"WorkflowState": 0,
	"InstanceApproval": -1,
	"ActivityCode": null,
	"DisplayName": "客户",
	"SchemaCode": "Sahj8417jpmkya2ndghgvorg83",
	"Originator": "0f283637-6b16-42fc-a225-03c0ff8094e2",
	"OriginatorCode": "yeyunlong",
	"OriginatorParentId": "18f923a7-5a5e-426d-94ae-a55ad1a4b240",
	"WorkflowVersion": 0,
	"FormMode": 4,
	"IsCreateMode": false,
	"InstanceId": null,
	"BizObjectId": "ee49a5f6-1ae0-4ce8-b88b-452cf41e61d0",
	"BizObjectStatus": 1,
	"WorkItemId": null,
	"Close": false,
	"WorkItemType": 0,
	"Actions": {
		"Edit": {
			"Action": "Edit",
			"Icon": "icon-ok",
			"Text": "编辑",
			"IsPrintAction": false,
			"IsCustomPrint": false,
			"PrintTemplateCode": null,
			"SortKey": 0,
			"CommentConfig": null,
			"Extend": null
		},
		"Print": {
			"Action": "Print",
			"Icon": "fa-print",
			"Text": "打印",
			"IsPrintAction": false,
			"IsCustomPrint": false,
			"PrintTemplateCode": null,
			"SortKey": 1,
			"CommentConfig": null,
			"Extend": null
		},
		"Remove": {
			"Action": "Remove",
			"Icon": "fa-minus",
			"Text": "删除",
			"IsPrintAction": false,
			"IsCustomPrint": false,
			"PrintTemplateCode": null,
			"SortKey": 2,
			"CommentConfig": null,
			"Extend": null
		},
		"ViewQrCode": {
			"Action": "ViewQrCode",
			"Icon": "",
			"Text": "二维码",
			"IsPrintAction": false,
			"IsCustomPrint": false,
			"PrintTemplateCode": null,
			"SortKey": 3,
			"CommentConfig": null,
			"Extend": null
		},
		"Close": {
			"Action": "Close",
			"Icon": "",
			"Text": "关闭",
			"IsPrintAction": false,
			"IsCustomPrint": false,
			"PrintTemplateCode": null,
			"SortKey": 4,
			"CommentConfig": null,
			"Extend": null
		}
	},
	"AssociatedBoNames": {},
	"AssociationLists": {},
	"EnableFormSns": true,
	"EnableTask": false,
	"EnableLog": true,
	"Name": "werfsdf",
	"FormDataType": 2,
	"AllowedReuseSignature": false,
	"AllowedUploadSignature": false,
	"ReturnData": {
		"ObjectId": {
			"Type": 14,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": "ee49a5f6-1ae0-4ce8-b88b-452cf41e61d0",
			"DefaultValue": null,
			"DisplayName": "ObjectId",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"Name": {
			"Type": 14,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": "werfsdf",
			"DefaultValue": null,
			"DisplayName": "数据标题",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"CreatedBy": {
			"Type": 26,
			"Visible": true,
			"Editable": false,
			"Required": true,
			"Printable": true,
			"Value": [{
				"_dirtyProperties": {
					"Birthday": 2,
					"EntryDate": 2,
					"DepartureDate": 2,
					"ProfilePhotoUrl": 2,
					"DingTalkAccount": 2,
					"DingId": 2,
					"DingTalkUnionId": 2,
					"ExtAttr": 2,
					"ViceParentIds": 2,
					"ParentId": 2,
					"Name": 2,
					"Code": 2,
					"ModifiedTime": 2,
					"ParentObjectId": 2
				},
				"UnitType": 4,
				"FullName": "叶云龙[yeyunlong]",
				"Birthday": "1753-01-01T00:00:00",
				"Gender": 0,
				"EntryDate": "1753-01-01T00:00:00",
				"DepartureDate": "1753-01-01T00:00:00",
				"EmployeeNumber": null,
				"Title": null,
				"HomePhone": null,
				"OfficePhone": null,
				"QQ": null,
				"IdNumber": null,
				"EmployeeRank": 0,
				"ProfilePhotoUrl": "https://static-legacy.dingtalk.com/media/lALPD1zawO40kV7NAyDNAyA_800_800.png",
				"DepartmentName": "四川怀信光业照明设备有限公司",
				"Password": null,
				"Mobile": null,
				"Email": null,
				"DingTalkAccount": "100959054621320094.dinge00194288e0da66cf5bf40eda33b7ba0",
				"DingId": "$:LWCP_v1:$5T5lsI9jK8/kjfKGdL3LZg==",
				"DingTalkUnionId": "pXFliiSiiRKal9Cii7iPIDCx8AiEiE",
				"Position": null,
				"ExtAttr": "{}",
				"WeChatUserId": null,
				"ViceParentIds": [],
				"ParentIds": ["18f923a7-5a5e-426d-94ae-a55ad1a4b240"],
				"AuthorizedState": 1,
				"H3Id": null,
				"EntryType": "DingTalk",
				"EntryUserId": "100959054621320094",
				"ParentId": "18f923a7-5a5e-426d-94ae-a55ad1a4b240",
				"CompanyId": "18f923a7-5a5e-426d-94ae-a55ad1a4b240",
				"Visibility": 7,
				"State": 0,
				"DomainType": 1,
				"Name": "叶云龙",
				"Description": null,
				"UnitId": "0f283637-6b16-42fc-a225-03c0ff8094e2",
				"Code": "yeyunlong",
				"ManagerId": null,
				"CreatedTime": "2022-12-06T19:47:17",
				"ModifiedTime": "2022-12-07T10:37:47",
				"SortKey": 0,
				"Serialized": true,
				"ObjectId": "0f283637-6b16-42fc-a225-03c0ff8094e2",
				"ParentObjectId": "",
				"ParentPropertyName": null,
				"ParentIndex": 0,
				"DirtyProperties": ["Birthday", "EntryDate", "DepartureDate", "ProfilePhotoUrl", "DingTalkAccount", "DingId", "DingTalkUnionId", "ExtAttr", "ViceParentIds", "ParentId", "Name", "Code", "ModifiedTime", "ParentObjectId"]
			}],
			"DefaultValue": null,
			"DisplayName": "创建人",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"CreatedTime": {
			"Type": 5,
			"Visible": true,
			"Editable": false,
			"Required": true,
			"Printable": true,
			"Value": "2022-12-09 17:33:24",
			"DefaultValue": "2022-12-09 17:33:24",
			"DisplayName": "创建时间",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"ModifiedBy": {
			"Type": 26,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": [{
				"_dirtyProperties": {
					"Birthday": 2,
					"EntryDate": 2,
					"DepartureDate": 2,
					"ProfilePhotoUrl": 2,
					"DingTalkAccount": 2,
					"DingId": 2,
					"DingTalkUnionId": 2,
					"ExtAttr": 2,
					"ViceParentIds": 2,
					"ParentId": 2,
					"Name": 2,
					"Code": 2,
					"ModifiedTime": 2,
					"ParentObjectId": 2
				},
				"UnitType": 4,
				"FullName": "叶云龙[yeyunlong]",
				"Birthday": "1753-01-01T00:00:00",
				"Gender": 0,
				"EntryDate": "1753-01-01T00:00:00",
				"DepartureDate": "1753-01-01T00:00:00",
				"EmployeeNumber": null,
				"Title": null,
				"HomePhone": null,
				"OfficePhone": null,
				"QQ": null,
				"IdNumber": null,
				"EmployeeRank": 0,
				"ProfilePhotoUrl": "https://static-legacy.dingtalk.com/media/lALPD1zawO40kV7NAyDNAyA_800_800.png",
				"DepartmentName": "四川怀信光业照明设备有限公司",
				"Password": null,
				"Mobile": null,
				"Email": null,
				"DingTalkAccount": "100959054621320094.dinge00194288e0da66cf5bf40eda33b7ba0",
				"DingId": "$:LWCP_v1:$5T5lsI9jK8/kjfKGdL3LZg==",
				"DingTalkUnionId": "pXFliiSiiRKal9Cii7iPIDCx8AiEiE",
				"Position": null,
				"ExtAttr": "{}",
				"WeChatUserId": null,
				"ViceParentIds": [],
				"ParentIds": ["18f923a7-5a5e-426d-94ae-a55ad1a4b240"],
				"AuthorizedState": 1,
				"H3Id": null,
				"EntryType": "DingTalk",
				"EntryUserId": "100959054621320094",
				"ParentId": "18f923a7-5a5e-426d-94ae-a55ad1a4b240",
				"CompanyId": "18f923a7-5a5e-426d-94ae-a55ad1a4b240",
				"Visibility": 7,
				"State": 0,
				"DomainType": 1,
				"Name": "叶云龙",
				"Description": null,
				"UnitId": "0f283637-6b16-42fc-a225-03c0ff8094e2",
				"Code": "yeyunlong",
				"ManagerId": null,
				"CreatedTime": "2022-12-06T19:47:17",
				"ModifiedTime": "2022-12-07T10:37:47",
				"SortKey": 0,
				"Serialized": true,
				"ObjectId": "0f283637-6b16-42fc-a225-03c0ff8094e2",
				"ParentObjectId": "",
				"ParentPropertyName": null,
				"ParentIndex": 0,
				"DirtyProperties": ["Birthday", "EntryDate", "DepartureDate", "ProfilePhotoUrl", "DingTalkAccount", "DingId", "DingTalkUnionId", "ExtAttr", "ViceParentIds", "ParentId", "Name", "Code", "ModifiedTime", "ParentObjectId"]
			}],
			"DefaultValue": null,
			"DisplayName": "ModifiedBy",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"ModifiedTime": {
			"Type": 5,
			"Visible": true,
			"Editable": false,
			"Required": true,
			"Printable": true,
			"Value": "2022-12-09 19:48:01",
			"DefaultValue": "2022-12-09 19:48:01",
			"DisplayName": "修改时间",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"WorkflowInstanceId": {
			"Type": 14,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": "",
			"DefaultValue": null,
			"DisplayName": "WorkflowInstanceId",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"Status": {
			"Type": 9,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": 1,
			"DefaultValue": null,
			"DisplayName": "Status",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"OwnerId": {
			"Type": 26,
			"Visible": true,
			"Editable": false,
			"Required": true,
			"Printable": true,
			"Value": [{
				"_dirtyProperties": {
					"Birthday": 2,
					"EntryDate": 2,
					"DepartureDate": 2,
					"ProfilePhotoUrl": 2,
					"DingTalkAccount": 2,
					"DingId": 2,
					"DingTalkUnionId": 2,
					"ExtAttr": 2,
					"ViceParentIds": 2,
					"ParentId": 2,
					"Name": 2,
					"Code": 2,
					"ModifiedTime": 2,
					"SortKey": 2,
					"ParentObjectId": 2
				},
				"UnitType": 4,
				"FullName": "钉钉-崔宇波[dingding-cuiyubo1]",
				"Birthday": "1753-01-01T00:00:00",
				"Gender": 0,
				"EntryDate": "1753-01-01T00:00:00",
				"DepartureDate": "1753-01-01T00:00:00",
				"EmployeeNumber": null,
				"Title": null,
				"HomePhone": null,
				"OfficePhone": null,
				"QQ": null,
				"IdNumber": null,
				"EmployeeRank": 0,
				"ProfilePhotoUrl": "https://static-legacy.dingtalk.com/media/lADPDhJzyrRZv_nNAljNAlg_600_600.jpg",
				"DepartmentName": "四川怀信光业照明设备有限公司",
				"Password": null,
				"Mobile": null,
				"Email": null,
				"DingTalkAccount": "********-**********.dinge00194288e0da66cf5bf40eda33b7ba0",
				"DingId": "$:LWCP_v1:$v9azx9MKg8XlxKnLQuD9Ag==",
				"DingTalkUnionId": "URmK6o99wLgiE",
				"Position": null,
				"ExtAttr": "{}",
				"WeChatUserId": null,
				"ViceParentIds": [],
				"ParentIds": ["18f923a7-5a5e-426d-94ae-a55ad1a4b240"],
				"AuthorizedState": 1,
				"H3Id": null,
				"EntryType": "DingTalk",
				"EntryUserId": "********-**********",
				"ParentId": "18f923a7-5a5e-426d-94ae-a55ad1a4b240",
				"CompanyId": "18f923a7-5a5e-426d-94ae-a55ad1a4b240",
				"Visibility": 7,
				"State": 0,
				"DomainType": 1,
				"Name": "钉钉-崔宇波",
				"Description": null,
				"UnitId": "c4359bb6-4499-45c0-b5cf-98707ab6e731",
				"Code": "dingding-cuiyubo1",
				"ManagerId": null,
				"CreatedTime": "2022-09-13T17:22:43",
				"ModifiedTime": "2022-09-13T17:35:03",
				"SortKey": 176254917781886512,
				"Serialized": true,
				"ObjectId": "c4359bb6-4499-45c0-b5cf-98707ab6e731",
				"ParentObjectId": "",
				"ParentPropertyName": null,
				"ParentIndex": 0,
				"DirtyProperties": ["Birthday", "EntryDate", "DepartureDate", "ProfilePhotoUrl", "DingTalkAccount", "DingId", "DingTalkUnionId", "ExtAttr", "ViceParentIds", "ParentId", "Name", "Code", "ModifiedTime", "SortKey", "ParentObjectId"]
			}],
			"DefaultValue": null,
			"DisplayName": "客户经理",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"OwnerDeptId": {
			"Type": 26,
			"Visible": true,
			"Editable": false,
			"Required": true,
			"Printable": true,
			"Value": [{
				"_dirtyProperties": {
					"Name": 2,
					"Code": 2,
					"ManagerId": 2,
					"ModifiedTime": 2,
					"ParentObjectId": 2
				},
				"UnitType": 1,
				"CompanyId": "18f923a7-5a5e-426d-94ae-a55ad1a4b240",
				"DomainType": 1,
				"Name": "四川怀信光业照明设备有限公司",
				"Description": null,
				"UnitId": "18f923a7-5a5e-426d-94ae-a55ad1a4b240",
				"Code": "G00000",
				"ManagerId": "16e394a6-5237-4a2c-9440-0e66f234a6b2",
				"CreatedTime": "2022-09-09T13:31:23",
				"ModifiedTime": "2022-10-31T18:32:54",
				"SortKey": 0,
				"Serialized": true,
				"ObjectId": "18f923a7-5a5e-426d-94ae-a55ad1a4b240",
				"ParentObjectId": "",
				"ParentPropertyName": null,
				"ParentIndex": 0,
				"DirtyProperties": ["Name", "Code", "ManagerId", "ModifiedTime", "ParentObjectId"]
			}],
			"DefaultValue": null,
			"DisplayName": "所属部门",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"F0000032": {
			"Type": 27,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": null,
			"DefaultValue": null,
			"DisplayName": "协同人员（多选）",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"F0000049": {
			"Type": 26,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": [{
				"_dirtyProperties": {
					"Birthday": 2,
					"EntryDate": 2,
					"DepartureDate": 2,
					"DingTalkAccount": 2,
					"DingId": 2,
					"DingTalkUnionId": 2,
					"ViceParentIds": 2,
					"ParentId": 2,
					"Name": 2,
					"Code": 2,
					"ModifiedTime": 2,
					"SortKey": 2,
					"ParentObjectId": 2
				},
				"UnitType": 4,
				"FullName": "鱼书[*****************-dinge00194288e0da66cf5bf40eda33b7ba0]",
				"Birthday": "1753-01-01T00:00:00",
				"Gender": 0,
				"EntryDate": "1753-01-01T00:00:00",
				"DepartureDate": "1753-01-01T00:00:00",
				"EmployeeNumber": null,
				"Title": null,
				"HomePhone": null,
				"OfficePhone": null,
				"QQ": null,
				"IdNumber": null,
				"EmployeeRank": 0,
				"ProfilePhotoUrl": null,
				"DepartmentName": "四川怀信光业照明设备有限公司",
				"Password": null,
				"Mobile": null,
				"Email": null,
				"DingTalkAccount": "*****************.dinge00194288e0da66cf5bf40eda33b7ba0",
				"DingId": "$:LWCP_v1:$gv2xDQ0AXfjXMnHCy/25RVM9GeYQiaY2",
				"DingTalkUnionId": "KFC1yyiS3O299Cii7iPIDCx8AiEiE",
				"Position": null,
				"ExtAttr": null,
				"WeChatUserId": null,
				"ViceParentIds": [],
				"ParentIds": ["18f923a7-5a5e-426d-94ae-a55ad1a4b240"],
				"AuthorizedState": 1,
				"H3Id": null,
				"EntryType": "DingTalk",
				"EntryUserId": "*****************",
				"ParentId": "18f923a7-5a5e-426d-94ae-a55ad1a4b240",
				"CompanyId": "18f923a7-5a5e-426d-94ae-a55ad1a4b240",
				"Visibility": 7,
				"State": 0,
				"DomainType": 1,
				"Name": "鱼书",
				"Description": null,
				"UnitId": "8eea39d6-be5d-4a0b-82f7-a4d6b8eaabe4",
				"Code": "*****************-dinge00194288e0da66cf5bf40eda33b7ba0",
				"ManagerId": null,
				"CreatedTime": "2022-09-13T15:31:31",
				"ModifiedTime": "2022-09-13T17:35:03",
				"SortKey": 176254919342560512,
				"Serialized": true,
				"ObjectId": "8eea39d6-be5d-4a0b-82f7-a4d6b8eaabe4",
				"ParentObjectId": "",
				"ParentPropertyName": null,
				"ParentIndex": 0,
				"DirtyProperties": ["Birthday", "EntryDate", "DepartureDate", "DingTalkAccount", "DingId", "DingTalkUnionId", "ViceParentIds", "ParentId", "Name", "Code", "ModifiedTime", "SortKey", "ParentObjectId"]
			}],
			"DefaultValue": null,
			"DisplayName": "跟单号",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"F0000001": {
			"Type": 14,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": "werfsdf",
			"DefaultValue": null,
			"DisplayName": "客户名称",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"F0000022": {
			"Type": 14,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": "正式客户",
			"DefaultValue": null,
			"DisplayName": "客户类型",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"Fmvl8idez8o6hkgrlq8wdhbl20": {
			"Type": 41,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": {
				"R": [{
					"Fmvl8idez8o6hkgrlq8wdhbl20.ObjectId": {
						"Type": 14,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": "8e33287a-7846-4930-b1f4-17b706ee2598",
						"DefaultValue": null,
						"DisplayName": "ObjectId",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"Fmvl8idez8o6hkgrlq8wdhbl20.Name": {
						"Type": 14,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": "fasd",
						"DefaultValue": null,
						"DisplayName": "数据标题",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"Fmvl8idez8o6hkgrlq8wdhbl20.ParentObjectId": {
						"Type": 14,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": "ee49a5f6-1ae0-4ce8-b88b-452cf41e61d0",
						"DefaultValue": null,
						"DisplayName": "ParentObjectId",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"Fmvl8idez8o6hkgrlq8wdhbl20.F0000028": {
						"Type": 14,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": "fasd",
						"DefaultValue": null,
						"DisplayName": "联系人姓名",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"Fmvl8idez8o6hkgrlq8wdhbl20.F0000023": {
						"Type": 14,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": "sfdfasd",
						"DefaultValue": null,
						"DisplayName": "手机",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"Fmvl8idez8o6hkgrlq8wdhbl20.F0000024": {
						"Type": 14,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": "fasdf",
						"DefaultValue": null,
						"DisplayName": "邮箱",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"Fmvl8idez8o6hkgrlq8wdhbl20.F0000026": {
						"Type": 14,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": "asdfas",
						"DefaultValue": null,
						"DisplayName": "QQ",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"Fmvl8idez8o6hkgrlq8wdhbl20.F0000025": {
						"Type": 14,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": "fas",
						"DefaultValue": null,
						"DisplayName": "职位",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"Fmvl8idez8o6hkgrlq8wdhbl20.F0000029": {
						"Type": 1,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": false,
						"DefaultValue": null,
						"DisplayName": "是否为主要联系人",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					}
				}, {
					"Fmvl8idez8o6hkgrlq8wdhbl20.ObjectId": {
						"Type": 14,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": "94c6b7b7-a09f-4196-b082-eb1c32f103f5",
						"DefaultValue": null,
						"DisplayName": "ObjectId",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"Fmvl8idez8o6hkgrlq8wdhbl20.Name": {
						"Type": 14,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": "asdfasdf",
						"DefaultValue": null,
						"DisplayName": "数据标题",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"Fmvl8idez8o6hkgrlq8wdhbl20.ParentObjectId": {
						"Type": 14,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": "ee49a5f6-1ae0-4ce8-b88b-452cf41e61d0",
						"DefaultValue": null,
						"DisplayName": "ParentObjectId",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"Fmvl8idez8o6hkgrlq8wdhbl20.F0000028": {
						"Type": 14,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": "asdfasdf",
						"DefaultValue": null,
						"DisplayName": "联系人姓名",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"Fmvl8idez8o6hkgrlq8wdhbl20.F0000023": {
						"Type": 14,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": "ewr",
						"DefaultValue": null,
						"DisplayName": "手机",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"Fmvl8idez8o6hkgrlq8wdhbl20.F0000024": {
						"Type": 14,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": "wer",
						"DefaultValue": null,
						"DisplayName": "邮箱",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"Fmvl8idez8o6hkgrlq8wdhbl20.F0000026": {
						"Type": 14,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": "qwe",
						"DefaultValue": null,
						"DisplayName": "QQ",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"Fmvl8idez8o6hkgrlq8wdhbl20.F0000025": {
						"Type": 14,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": "v",
						"DefaultValue": null,
						"DisplayName": "职位",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"Fmvl8idez8o6hkgrlq8wdhbl20.F0000029": {
						"Type": 1,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": false,
						"DefaultValue": null,
						"DisplayName": "是否为主要联系人",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					}
				}],
				"T": {
					"Fmvl8idez8o6hkgrlq8wdhbl20.F0000028": {
						"Type": 14,
						"Visible": true,
						"Editable": true,
						"Required": false,
						"Printable": true,
						"Value": null,
						"DefaultValue": null,
						"DisplayName": "联系人姓名",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"Fmvl8idez8o6hkgrlq8wdhbl20.F0000023": {
						"Type": 14,
						"Visible": true,
						"Editable": true,
						"Required": false,
						"Printable": true,
						"Value": null,
						"DefaultValue": null,
						"DisplayName": "手机",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"Fmvl8idez8o6hkgrlq8wdhbl20.F0000024": {
						"Type": 14,
						"Visible": true,
						"Editable": true,
						"Required": false,
						"Printable": true,
						"Value": null,
						"DefaultValue": null,
						"DisplayName": "邮箱",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"Fmvl8idez8o6hkgrlq8wdhbl20.F0000026": {
						"Type": 14,
						"Visible": true,
						"Editable": true,
						"Required": false,
						"Printable": true,
						"Value": null,
						"DefaultValue": null,
						"DisplayName": "QQ",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"Fmvl8idez8o6hkgrlq8wdhbl20.F0000025": {
						"Type": 14,
						"Visible": true,
						"Editable": true,
						"Required": false,
						"Printable": true,
						"Value": null,
						"DefaultValue": null,
						"DisplayName": "职位",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"Fmvl8idez8o6hkgrlq8wdhbl20.F0000029": {
						"Type": 1,
						"Visible": true,
						"Editable": true,
						"Required": false,
						"Printable": true,
						"Value": false,
						"DefaultValue": null,
						"DisplayName": "是否为主要联系人",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					}
				}
			},
			"DefaultValue": null,
			"DisplayName": "联系人信息",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": "{F0000028}"
		},
		"F0000002": {
			"Type": 56,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": "{\\"
			adcode\\ ":\\"
			810018\\ ",\\"
			adname\\ ":\\"
			香港特别行政区 离岛区\\ ",\\"
			Detail\\ ":\\"
			sdfe\\ "}",
			"DefaultValue": null,
			"DisplayName": "地址",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"F0000033": {
			"Type": 13,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": "wfe",
			"DefaultValue": null,
			"DisplayName": "客户属性",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"F0000034": {
			"Type": 14,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": "C 制造业",
			"DefaultValue": null,
			"DisplayName": "行业",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"F0000035": {
			"Type": 14,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": "B级：重要，意向明确",
			"DefaultValue": null,
			"DisplayName": "客户等级",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"F0000036": {
			"Type": 14,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": "400电话",
			"DefaultValue": null,
			"DisplayName": "客户来源",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"F0000037": {
			"Type": 14,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": "商机客户",
			"DefaultValue": null,
			"DisplayName": "客户状态",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"F0000047": {
			"Type": 14,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": "是",
			"DefaultValue": null,
			"DisplayName": "是否要清单",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"F0000045": {
			"Type": 14,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": "顺丰到付",
			"DefaultValue": null,
			"DisplayName": "交货方式",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"F0000046": {
			"Type": 14,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": "其他",
			"DefaultValue": null,
			"DisplayName": "付款方式",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"F0000048": {
			"Type": 14,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": "其他",
			"DefaultValue": null,
			"DisplayName": "包装",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"F0000038": {
			"Type": 14,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": "未跟进",
			"DefaultValue": null,
			"DisplayName": "跟进状态",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"F0000039": {
			"Type": 5,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": "2022-12-30 00:00:00",
			"DefaultValue": "2022-12-30 00:00:00",
			"DisplayName": "最新跟进时间",
			"DataDictItemValue": null,
			"ComputationRuleFields": ["F34685c8b5e494255b73f944d259ec9f9.F0000041"],
			"ComputationRule": "$.fn.MAX({F34685c8b5e494255b73f944d259ec9f9.F0000041})",
			"DisplayRuleFields": ["F0000038"],
			"DisplayRule": "{F0000038} == \\"
			未跟进\\ "",
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"F34685c8b5e494255b73f944d259ec9f9": {
			"Type": 41,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": {
				"R": [{
					"F34685c8b5e494255b73f944d259ec9f9.ObjectId": {
						"Type": 14,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": "9d92e2db-02fb-4742-887b-109cfad7fa80",
						"DefaultValue": null,
						"DisplayName": "ObjectId",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"F34685c8b5e494255b73f944d259ec9f9.Name": {
						"Type": 14,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": "叶云龙    2022-12-09     werfsdf",
						"DefaultValue": null,
						"DisplayName": "数据标题",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"F34685c8b5e494255b73f944d259ec9f9.ParentObjectId": {
						"Type": 14,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": "ee49a5f6-1ae0-4ce8-b88b-452cf41e61d0",
						"DefaultValue": null,
						"DisplayName": "ParentObjectId",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"F34685c8b5e494255b73f944d259ec9f9.F0000041": {
						"Type": 5,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": "2022-12-09 00:00:00",
						"DefaultValue": "2022-12-09 00:00:00",
						"DisplayName": "跟进日期",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"F34685c8b5e494255b73f944d259ec9f9.F0000042": {
						"Type": 14,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": "拜访",
						"DefaultValue": null,
						"DisplayName": "跟进方式",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"F34685c8b5e494255b73f944d259ec9f9.F0000043": {
						"Type": 13,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": "erwer",
						"DefaultValue": null,
						"DisplayName": "跟进内容",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"F34685c8b5e494255b73f944d259ec9f9.F0000044": {
						"Type": 26,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": {
							"_dirtyProperties": {
								"Birthday": 2,
								"EntryDate": 2,
								"DepartureDate": 2,
								"ProfilePhotoUrl": 2,
								"DingTalkAccount": 2,
								"DingId": 2,
								"DingTalkUnionId": 2,
								"ExtAttr": 2,
								"ViceParentIds": 2,
								"ParentId": 2,
								"Name": 2,
								"Code": 2,
								"ModifiedTime": 2,
								"ParentObjectId": 2
							},
							"UnitType": 4,
							"FullName": "叶云龙[yeyunlong]",
							"Birthday": "1753-01-01T00:00:00",
							"Gender": 0,
							"EntryDate": "1753-01-01T00:00:00",
							"DepartureDate": "1753-01-01T00:00:00",
							"EmployeeNumber": null,
							"Title": null,
							"HomePhone": null,
							"OfficePhone": null,
							"QQ": null,
							"IdNumber": null,
							"EmployeeRank": 0,
							"ProfilePhotoUrl": "https://static-legacy.dingtalk.com/media/lALPD1zawO40kV7NAyDNAyA_800_800.png",
							"DepartmentName": "四川怀信光业照明设备有限公司",
							"Password": null,
							"Mobile": null,
							"Email": null,
							"DingTalkAccount": "100959054621320094.dinge00194288e0da66cf5bf40eda33b7ba0",
							"DingId": "$:LWCP_v1:$5T5lsI9jK8/kjfKGdL3LZg==",
							"DingTalkUnionId": "pXFliiSiiRKal9Cii7iPIDCx8AiEiE",
							"Position": null,
							"ExtAttr": "{}",
							"WeChatUserId": null,
							"ViceParentIds": [],
							"ParentIds": ["18f923a7-5a5e-426d-94ae-a55ad1a4b240"],
							"AuthorizedState": 1,
							"H3Id": null,
							"EntryType": "DingTalk",
							"EntryUserId": "100959054621320094",
							"ParentId": "18f923a7-5a5e-426d-94ae-a55ad1a4b240",
							"CompanyId": "18f923a7-5a5e-426d-94ae-a55ad1a4b240",
							"Visibility": 7,
							"State": 0,
							"DomainType": 1,
							"Name": "叶云龙",
							"Description": null,
							"UnitId": "0f283637-6b16-42fc-a225-03c0ff8094e2",
							"Code": "yeyunlong",
							"ManagerId": null,
							"CreatedTime": "2022-12-06T19:47:17",
							"ModifiedTime": "2022-12-07T10:37:47",
							"SortKey": 0,
							"Serialized": true,
							"ObjectId": "0f283637-6b16-42fc-a225-03c0ff8094e2",
							"ParentObjectId": "",
							"ParentPropertyName": null,
							"ParentIndex": 0,
							"DirtyProperties": ["Birthday", "EntryDate", "DepartureDate", "ProfilePhotoUrl", "DingTalkAccount", "DingId", "DingTalkUnionId", "ExtAttr", "ViceParentIds", "ParentId", "Name", "Code", "ModifiedTime", "ParentObjectId"]
						},
						"DefaultValue": null,
						"DisplayName": "跟进人",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					}
				}, {
					"F34685c8b5e494255b73f944d259ec9f9.ObjectId": {
						"Type": 14,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": "c2bb827e-5319-4ca0-9aa0-daafa74f9329",
						"DefaultValue": null,
						"DisplayName": "ObjectId",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"F34685c8b5e494255b73f944d259ec9f9.Name": {
						"Type": 14,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": "钉钉-崔宇波    2022-12-30     werfsdf",
						"DefaultValue": null,
						"DisplayName": "数据标题",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"F34685c8b5e494255b73f944d259ec9f9.ParentObjectId": {
						"Type": 14,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": "ee49a5f6-1ae0-4ce8-b88b-452cf41e61d0",
						"DefaultValue": null,
						"DisplayName": "ParentObjectId",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"F34685c8b5e494255b73f944d259ec9f9.F0000041": {
						"Type": 5,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": "2022-12-30 00:00:00",
						"DefaultValue": "2022-12-30 00:00:00",
						"DisplayName": "跟进日期",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"F34685c8b5e494255b73f944d259ec9f9.F0000042": {
						"Type": 14,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": "钉钉",
						"DefaultValue": null,
						"DisplayName": "跟进方式",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"F34685c8b5e494255b73f944d259ec9f9.F0000043": {
						"Type": 13,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": "spring",
						"DefaultValue": null,
						"DisplayName": "跟进内容",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"F34685c8b5e494255b73f944d259ec9f9.F0000044": {
						"Type": 26,
						"Visible": true,
						"Editable": false,
						"Required": false,
						"Printable": true,
						"Value": {
							"_dirtyProperties": {
								"Birthday": 2,
								"EntryDate": 2,
								"DepartureDate": 2,
								"ProfilePhotoUrl": 2,
								"DingTalkAccount": 2,
								"DingId": 2,
								"DingTalkUnionId": 2,
								"ExtAttr": 2,
								"ViceParentIds": 2,
								"ParentId": 2,
								"Name": 2,
								"Code": 2,
								"ModifiedTime": 2,
								"SortKey": 2,
								"ParentObjectId": 2
							},
							"UnitType": 4,
							"FullName": "钉钉-崔宇波[dingding-cuiyubo1]",
							"Birthday": "1753-01-01T00:00:00",
							"Gender": 0,
							"EntryDate": "1753-01-01T00:00:00",
							"DepartureDate": "1753-01-01T00:00:00",
							"EmployeeNumber": null,
							"Title": null,
							"HomePhone": null,
							"OfficePhone": null,
							"QQ": null,
							"IdNumber": null,
							"EmployeeRank": 0,
							"ProfilePhotoUrl": "https://static-legacy.dingtalk.com/media/lADPDhJzyrRZv_nNAljNAlg_600_600.jpg",
							"DepartmentName": "四川怀信光业照明设备有限公司",
							"Password": null,
							"Mobile": null,
							"Email": null,
							"DingTalkAccount": "********-**********.dinge00194288e0da66cf5bf40eda33b7ba0",
							"DingId": "$:LWCP_v1:$v9azx9MKg8XlxKnLQuD9Ag==",
							"DingTalkUnionId": "URmK6o99wLgiE",
							"Position": null,
							"ExtAttr": "{}",
							"WeChatUserId": null,
							"ViceParentIds": [],
							"ParentIds": ["18f923a7-5a5e-426d-94ae-a55ad1a4b240"],
							"AuthorizedState": 1,
							"H3Id": null,
							"EntryType": "DingTalk",
							"EntryUserId": "********-**********",
							"ParentId": "18f923a7-5a5e-426d-94ae-a55ad1a4b240",
							"CompanyId": "18f923a7-5a5e-426d-94ae-a55ad1a4b240",
							"Visibility": 7,
							"State": 0,
							"DomainType": 1,
							"Name": "钉钉-崔宇波",
							"Description": null,
							"UnitId": "c4359bb6-4499-45c0-b5cf-98707ab6e731",
							"Code": "dingding-cuiyubo1",
							"ManagerId": null,
							"CreatedTime": "2022-09-13T17:22:43",
							"ModifiedTime": "2022-09-13T17:35:03",
							"SortKey": 176254917781886512,
							"Serialized": true,
							"ObjectId": "c4359bb6-4499-45c0-b5cf-98707ab6e731",
							"ParentObjectId": "",
							"ParentPropertyName": null,
							"ParentIndex": 0,
							"DirtyProperties": ["Birthday", "EntryDate", "DepartureDate", "ProfilePhotoUrl", "DingTalkAccount", "DingId", "DingTalkUnionId", "ExtAttr", "ViceParentIds", "ParentId", "Name", "Code", "ModifiedTime", "SortKey", "ParentObjectId"]
						},
						"DefaultValue": null,
						"DisplayName": "跟进人",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					}
				}],
				"T": {
					"F34685c8b5e494255b73f944d259ec9f9.F0000041": {
						"Type": 5,
						"Visible": true,
						"Editable": true,
						"Required": false,
						"Printable": true,
						"Value": null,
						"DefaultValue": null,
						"DisplayName": "跟进日期",
						"DataDictItemValue": null,
						"ComputationRuleFields": [],
						"ComputationRule": "$.fn.TODAY()",
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"F34685c8b5e494255b73f944d259ec9f9.F0000042": {
						"Type": 14,
						"Visible": true,
						"Editable": true,
						"Required": false,
						"Printable": true,
						"Value": null,
						"DefaultValue": null,
						"DisplayName": "跟进方式",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"F34685c8b5e494255b73f944d259ec9f9.F0000043": {
						"Type": 13,
						"Visible": true,
						"Editable": true,
						"Required": false,
						"Printable": true,
						"Value": null,
						"DefaultValue": null,
						"DisplayName": "跟进内容",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					},
					"F34685c8b5e494255b73f944d259ec9f9.F0000044": {
						"Type": 26,
						"Visible": true,
						"Editable": true,
						"Required": false,
						"Printable": true,
						"Value": null,
						"DefaultValue": null,
						"DisplayName": "跟进人",
						"DataDictItemValue": null,
						"ComputationRuleFields": null,
						"ComputationRule": null,
						"DisplayRuleFields": null,
						"DisplayRule": null,
						"ChildRepeatOption": 0,
						"IsFormula": null,
						"DefaultValueRuleType": 0,
						"AssociationFilterText": null,
						"DateDefaultValue": null,
						"AlwaysHiding": false,
						"ChildNameSchema": null
					}
				}
			},
			"DefaultValue": null,
			"DisplayName": "跟进记录",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": "{F0000041}"
		},
		"F0000040": {
			"Type": 13,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": null,
			"DefaultValue": null,
			"DisplayName": "备注",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"F0000009": {
			"Type": 14,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": "q",
			"DefaultValue": null,
			"DisplayName": "发票抬头",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"F0000010": {
			"Type": 14,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": "erwr",
			"DefaultValue": null,
			"DisplayName": "税号",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"F0000011": {
			"Type": 14,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": "sdf",
			"DefaultValue": null,
			"DisplayName": "开户行",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"F0000012": {
			"Type": 14,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": "e",
			"DefaultValue": null,
			"DisplayName": "银行账号",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"F0000021": {
			"Type": 14,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": "z",
			"DefaultValue": null,
			"DisplayName": "开票地址",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"F0000020": {
			"Type": 14,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": "fdsf",
			"DefaultValue": null,
			"DisplayName": "电话",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"F0000050": {
			"Type": 7,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": null,
			"DefaultValue": null,
			"DisplayName": "账户余额",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"F0000051": {
			"Type": 7,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": null,
			"DefaultValue": null,
			"DisplayName": "应收款",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		},
		"CreatedBy.FullName": {
			"Type": 14,
			"Visible": true,
			"Editable": false,
			"Required": false,
			"Printable": true,
			"Value": "叶云龙",
			"DefaultValue": null,
			"DisplayName": "CreatedBy.FullName",
			"DataDictItemValue": null,
			"ComputationRuleFields": null,
			"ComputationRule": null,
			"DisplayRuleFields": null,
			"DisplayRule": null,
			"ChildRepeatOption": 0,
			"IsFormula": null,
			"DefaultValueRuleType": 0,
			"AssociationFilterText": null,
			"DateDefaultValue": null,
			"AlwaysHiding": false,
			"ChildNameSchema": null
		}
	},
	"PrintConfig": {
		"PrintCompanyName": true,
		"PrintComment": true,
		"Printer": true,
		"PrintTime": true,
		"PrintQrCode": true
	},
	"Comments": null,
	"ActivityParticipants": null,
	"WorkItems": null,
	"RetrieveType": 0,
	"RequestParameters": {
		"SchemaCode": "Sahj8417jpmkya2ndghgvorg83",
		"BizObjectId": "ee49a5f6-1ae0-4ce8-b88b-452cf41e61d0",
		"Mode": "View",
		"IsExternalForm": "False",
		"IsExternalShare": "False"
	},
	"QrCodeUrl": "https://www.h3yun.com/index.html?CorpId=dinge00194288e0da66cf5bf40eda33b7ba0&sc=Sahj8417jpmkya2ndghgvorg83&bo=ee49a5f6-1ae0-4ce8-b88b-452cf41e61d0&mt=Task&IsIsv=1&sk=suitefcoyqxthbd1xwjiu&ec=tfop8m65zwq5ft8300pz2cbx2&et=DingTalk&QrFrom=data",
	"ReviewQrCodeUrl": "https://external.h3yun.com/FormExternal/ReviewQrCode/Sahj8417jpmkya2ndghgvorg83?EngineCode=tfop8m65zwq5ft8300pz2cbx2",
	"IsLight": false,
	"IsUnfinishedCirculate": false,
	"FormLayoutType": 0,
	"Successful": true,
	"IsMobile": false,
	"Message": "",
	"Infos": [],
	"IsExternalForm": false,
	"IsOpenQuery": false,
	"IsExternalShare": false,
	"ExternalFormUrl": null,
	"RecentChange": {
		"FormDataType": 2,
		"FormMode": 4,
		"SchemaDisplayName": "客户"
	},
	"MobileLayout": 0,
	"PCLayout": 1,
	"PCLayoutConfig": {
		"LayoutType": 1,
		"TitleFontWeight": 400,
		"TitleWidth": 84,
		"TitleAlign": "left",
		"BorderShow": false
	},
	"MobileLayoutConfig": {
		"LayoutType": 1,
		"TitleFontWeight": 400,
		"TitleWidth": 80,
		"TitleAlign": "left",
		"BorderShow": false
	},
	"Errors": [],
	"DebugTrack": null,
	"ChangeSet": null
}