
using System;
using System.Collections.Generic;
using System.Text;
using H3;
using H3.DataModel;
using System.Data;

public class D282605Sbces9w53iq957t4gc8vulc6y2_ListViewController: H3.SmartForm.ListViewController
{
    public D282605Sbces9w53iq957t4gc8vulc6y2_ListViewController(H3.SmartForm.ListViewRequest request): base(request)
    {
    }

    protected override void OnLoad(H3.SmartForm.LoadListViewResponse response)
    {
        base.OnLoad(response);
        if(this.Request.ListScene == H3.SmartForm.ListScene.NormalList)
        {
            //设置只在load列表数据时加载颜色
            // foreach(Dictionary < string, object > data in response.ReturnData)
            // {
            //     //设置列名为Mobile的颜色为Red
            //     //data["MaterialName"] = new H3.SmartForm.ListViewCustomCell(data["MaterialName"] == null ? "--" : data["MaterialName"].ToString(), H3.SmartForm.Color.None() );
            //     if(string.IsNullOrEmpty(data["DeliveryDate"] + string.Empty))
            //     {
            //         data["QuasiDelivery"] = "交期不详";
            //     }
            //     else
            //     {
            //         DateTime delivery = Convert.ToDateTime(data["DeliveryDate"]);
            //         data["QuasiDelivery"] = delivery.CompareTo(System.DateTime.Today) < 0 ? "超期未完" : "交期未到";
            //     }

            //     DPuXinProcessCard card = new DPuXinProcessCard(Convert.ToString(data["ProcessLine"]));
            //     if(!card.HasProcess("打包"))
            //     {
            //         data["QuasiDelivery"] = "子件物料";
            //     }
            // }
        }
    }

    protected override void OnSubmit(string actionName, H3.SmartForm.ListViewPostValue postValue, H3.SmartForm.SubmitListViewResponse response)
    {
        DPuXinExportPlan exportPlan = new DPuXinExportPlan(this.Engine, this.Request.UserContext.UserId);
        if(actionName == "clear")
        {
            string sql = "delete from i_D282605Sbces9w53iq957t4gc8vulc6y2 where 1 = 1";
            this.Engine.EngineConfig.CreateCommand().ExecuteScalar(sql);
            return;
        }
        if(actionName == "export_report")
        {
            Dictionary < string, string > queryParams = this.Deserialize<Dictionary<string, string>>(this.Request["ObjectId"]);
            List < Dictionary < string, object >> result =  exportPlan.ExportReport(queryParams);
            string resp = this.Serialize(result);

            response.ReturnData = new Dictionary<string, object>();
            response.ReturnData.Add("Data", resp);
            return;
        }
        if(actionName == "export_plan")
        {
            Dictionary < string, string > queryParams = this.Deserialize<Dictionary<string, string>>(this.Request["ObjectId"]);

            List < Dictionary < string, object >> result =  exportPlan.ExportPlan(queryParams);
            string resp = this.Serialize(result);

            response.ReturnData = new Dictionary<string, object>();
            response.ReturnData.Add("Data", resp);
            return;
        }
        base.OnSubmit(actionName, postValue, response);
    }
}


public class DPuXinExportPlan: DPuXinBaseSheet
{
    private DPuXinReport report;
    public DPuXinExportPlan(IEngine Engine, string UserId): base(Engine, UserId)
    {
        this.Engine = Engine;
        this.UserId = UserId;
        this.report = new DPuXinReport(Engine, UserId);
    }
    public List < Dictionary < string, object >> ExportPlan(Dictionary < string, string > queryParams)
    {
        List < Dictionary < string, object >> list = new List<Dictionary<string, object>>();
        BizObject[] plans = QueryPlan(queryParams);
        if(IsEmpty(plans))
        {
            return list;
        }
        foreach(BizObject plan in plans)
        {
            Dictionary < string, object > dict = ConvertBiz(plan);
            list.Add(dict);
        }
        return list;
    }

    public List < Dictionary < string, object >> ExportReport(Dictionary < string, string > queryParams)
    {
        List < Dictionary < string, object >> list = new List<Dictionary<string, object>>();
        bool onlyRemain = Convert.ToBoolean(queryParams["OnlyRemain"]);
        queryParams.Remove("OnlyRemain");
        string orderNo = Convert.ToString(queryParams["OrderNo"]);
        //queryParams["ProcessFlow"] = queryParams["ProcessFlow"] + ";";
        string[] queryProcess = Split(queryParams["ProcessFlow"], ';');
        if(IsEmpty(orderNo))
        {
            return list;
        }

        DataRowCollection reporRecord = this.report.QueryHaveApply(new string[]{ orderNo});

        BizObject[] plans = QueryPlan(queryParams);
        if(IsEmpty(plans))
        {
            return list;
        }

        string[] excludes = { this.planChildSchemaCode};
        //throw new Exception(plans.Length + "验证");
        foreach(BizObject plan in plans)
        {
            string planProcessFlow = Convert.ToString(plan["ProcessFlow"]);
            string[] processItems = planProcessFlow.Split(';');
            foreach(string process in processItems)
            {
                if(queryProcess != null && queryProcess.Length > 0 && !Exist(queryProcess, process))
                {
                    continue;
                }

                Dictionary < string, object > dict = ConvertBiz(plan, excludes);

                dict.Add("Process", process);
                int haveApply = FindCountOfHaveApply(reporRecord, plan, process);
                int count = ToInt(plan["Count"]);
                if(onlyRemain && count <= haveApply)
                {
                    continue;
                }
                dict.Add("Remain", count - haveApply);
                list.Add(dict);
            }
        }
        return list;
    }
    public BizObject[] QueryPlan(Dictionary < string, string > queryParams)
    {
        H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();  //根据物资编码查询库存
        H3.Data.Filter.And andMatcher = new H3.Data.Filter.And();    //构造And匹配器
        foreach(KeyValuePair < string, string > pair in queryParams)
        {
            string key = pair.Key;
            string value = pair.Value;
            if(string.IsNullOrEmpty(value))
            {
                continue;
            }
            if(value.Contains(";"))
            {

                string[] items = Split(value, ';');
                if(items.Length > 1)
                {
                    H3.Data.Filter.Or orMatcher = new H3.Data.Filter.Or();    //构造And匹配器
                    foreach(string item in items)
                    {
                        orMatcher.Add(new H3.Data.Filter.ItemMatcher(key, H3.Data.ComparisonOperatorType.Contains, item));
                    }
                    andMatcher.Add(orMatcher);
                }
                else
                {
                    andMatcher.Add(new H3.Data.Filter.ItemMatcher(key, H3.Data.ComparisonOperatorType.Contains, items[0]));
                }
            }
            else
            {
                andMatcher.Add(new H3.Data.Filter.ItemMatcher(key, H3.Data.ComparisonOperatorType.Contains, value));
            }
        }

        filter.Matcher = andMatcher;
        return GetList(this.planSchemaCode, filter);
    }

    public int FindCountOfHaveApply(DataRowCollection rows, BizObject obj, object process)
    {
        if(rows == null)
        {
            return 0;
        }
        foreach(DataRow row in rows)
        {
            if(Convert.ToString(row["OrderNo"]) == Convert.ToString(obj["OrderNo"])
                && Convert.ToString(row["MaterialNo"]) == Convert.ToString(obj["MaterialNo"])
                && Convert.ToString(row["Process"]) == Convert.ToString(process))
            {
                return this.ToInt(row["DeclaredQuantity"]);
            }
        }
        return 0;
    }
}