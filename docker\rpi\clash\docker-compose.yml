version: '3.7'
services:
  subweb:
    image: subweb-local:latest
    ports:
      - 58080:80
    links:
      - myurls
    deploy:
      restart_policy:
        condition: on-failure
  myurls:
    image: careywong/myurls:latest
    deploy:
      restart_policy:
        condition: on-failure
    ports:
      - 58002:8002
    depends_on:
      - myurls-redis
    entrypoint: ["/app/myurls","-domain", "api.yesky.xyz", "-conn", myurls-redis:6379, "-https", "0"]

  myurls-redis:
    image: arm64v8/redis:alpine
    deploy:
      restart_policy:
        condition: on-failure
    command: >
      bash -c "redis-server  --bind 0.0.0.0"
  subconverter:
    image: tindy2013/subconverter
    deploy:
      restart_policy:
        condition: on-failure
    ports:
      - 58500:25500
