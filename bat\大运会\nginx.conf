upstream study_pc{
    server **************:8026;
    server *************:8026;
}

server {
    listen       8026;
    client_max_body_size 1024M;

    location / {
        proxy_set_header Host $host;
        proxy_set_header X_Real_IP $remote_addr;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_pass http://study_pc;
    }
}


upstream study_app{
    server **************:8025;
    server *************:8025;
}

server {
    listen       8025;
    client_max_body_size 1024M;

    location / {
        proxy_set_header Host $host;
        proxy_set_header X_Real_IP $remote_addr;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_pass http://study_app;
    }
}

upstream schedule{
    server **************:8024;
    server *************:8024;
}

server {
    listen       8024;
    client_max_body_size 1024M;

    location / {
        proxy_set_header Host $host;
        proxy_set_header X_Real_IP $remote_addr;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_pass http://schedule;
    }
}