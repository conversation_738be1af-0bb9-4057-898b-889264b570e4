cd C:/Users/<USER>/IdeaProjects/schedule-web

git checkout dev
git pull

@rem npm package, build mode app.
call npm install
call npm run build

@rem winrar a -r  schedule.zip ./dist

7z a schedule.zip ./dist/*


@rem 开启ssh 隧道
ssh -CNfg -L 5550:**************:22 root@*************

@rem scp zip to server
scp -P 5550 ./schedule.zip root@127.0.0.1:/usr/share/nginx/html

echo "ssh nginx dir to unzip study, and rm zip"
ssh root@127.0.0.1 -p 5550 "cd /usr/share/nginx/html;rm -rf ./schedule;unzip -d schedule  schedule.zip;rm -rf ./schedule.zip"

echo "del local schedule zip"
del /f /q "./schedule.zip"


for /f "tokens=2" %%i in ('tasklist^|findstr ssh') do (
    echo "ssh 隧道PID:" %%i
    taskkill /F /PID %%i
)

pause