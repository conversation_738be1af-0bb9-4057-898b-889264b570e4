using System;
using System.Collections.Generic;
using System.Text;
using H3;
using H3.DataModel;
using System.Data;


public class D282605Sc2hh33czw2c9935a17pfux831_ListViewController : H3.SmartForm.ListViewController
{
    public D282605Sc2hh33czw2c9935a17pfux831_ListViewController(H3.SmartForm.ListViewRequest request) : base(request)
    {
    }

    protected override void OnLoad(H3.SmartForm.LoadListViewResponse response)
    {
        base.OnLoad(response);
    }

    protected override void OnSubmit(string actionName, H3.SmartForm.ListViewPostValue postValue, H3.SmartForm.SubmitListViewResponse response)
    {
        base.OnSubmit(actionName, postValue, response);
    }
}



public class DPuXinBoxCalcPrice
{
    private IEngine Engine;
    private string UserId;

    private List<DPuXinBasePrice> rules = new List<DPuXinBasePrice>();
    public DPuXinCalcPrice(IEngine engine, string userId)
    {
        this.Engine = engine;
        this.UserId = userId;

        this.rules.Add(new DPuXinBoxLaserPrice(engine, userId)); //激光
        this.rules.Add(new DPuXinBoxShearPrice(engine, userId)); //剪折冲
        this.rules.Add(new DPuXinBoxWeldingPrice(engine, userId)); //焊接
        this.rules.Add(new DPuXinBoxSprayPrice(engine, userId)); //喷塑
        this.rules.Add(new DPuXinBoxPackagePrice(engine, userId)); //装配
    }

    public void BatchCalcPrice(Dictionary<string, List<BizObject>> reportDict)
    {
        foreach (KeyValuePair<string, List<BizObject>> pair in reportDict)
        {
            this.BatchCalcPrice(pair.Value);
        }
    }

    public void BatchCalcPrice(List<BizObject> reports)
    {
        foreach (BizObject report in reports)
        {
            this.CalcPrice(report);
        }
    }
    public void CalcPrice(BizObject report)
    {
        string process = Convert.ToString(report["team"]);
        int declaredQuantity = Convert.ToInt32(report["DeclaredQuantity"]);
        foreach (DPuXinBasePrice rule in this.rules)
        {
            if (rule.Apply(process))
            {
                try
                {
                    decimal price = rule.Match(report);
                    if (price > 0)
                    {
                        report["Price"] = price;
                        report["SumPrice"] = price * declaredQuantity;
                    }
                    break;
                }
                catch (Exception ex)
                {
                }
            }
        }
    }
}