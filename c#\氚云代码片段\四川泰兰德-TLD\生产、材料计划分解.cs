
using System;
using System.Collections.Generic;
using System.Text;
using H3;

public class D149913Sz7noe4xwxhs16ohg1mfddu0e2 : H3.SmartForm.SmartFormController
{
    public D149913Sz7noe4xwxhs16ohg1mfddu0e2(H3.SmartForm.SmartFormRequest request) : base(request)
    {
    }

    protected override void OnLoad(H3.SmartForm.LoadSmartFormResponse response)
    {
        base.OnLoad(response);
    }

    protected override void OnSubmit(string actionName, H3.SmartForm.SmartFormPostValue postValue, H3.SmartForm.SubmitSmartFormResponse response)
    {
        if (actionName == "GetBOM")
        {
            string[] productIds = this.Deserialize<string[]>(this.Request["productIds"]);
            //查询bom单
            H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();
            filter.Matcher = new H3.Data.Filter.ItemMatcher("F0000001", H3.Data.ComparisonOperatorType.In, productIds);
            H3.DataModel.BizObject[] bomOrders = H3.DataModel.BizObject.GetList(this.Engine, this.Request.UserContext.UserId,
                this.Engine.BizObjectManager.GetPublishedSchema("D14991310b7cff7d2a443b19e8ba19a48994509"),
                H3.DataModel.GetListScopeType.GlobalAll, filter);
            List<Dictionary<string, object>> orders = new List<Dictionary<string, object>>();
            if (bomOrders != null && bomOrders.Length > 0)
            {
                foreach (H3.DataModel.BizObject bomOrder in bomOrders)
                {
                    Dictionary<string, object> order = new Dictionary<string, object>();
                    order.Add("F0000001", bomOrder["F0000001"]);
                    H3.DataModel.BizObject[] demandDetails = (H3.DataModel.BizObject[])bomOrder["D149913Fa5ace13d63c24085a595142f424a9826"];
                    List<Dictionary<string, object>> demands = new List<Dictionary<string, object>>();
                    if (demandDetails != null && demandDetails.Length > 0)
                    {
                        foreach (H3.DataModel.BizObject demandDetail in demandDetails)
                        {
                            Dictionary<string, object> demand = new Dictionary<string, object>();
                            demand.Add("F0000002", demandDetail["F0000002"]); //产品信息
                            demand.Add("F0000005", demandDetail["F0000005"]); //数量
                            demand.Add("F0000007", demandDetail["F0000007"]); //产品型号
                            demand.Add("F0000010", demandDetail["F0000010"]);//备注
                            demand.Add("F0000012", demandDetail["F0000012"]);//品牌
                            demand.Add("F0000013", demandDetail["F0000013"]);//单位
                            demands.Add(demand);
                        }
                    }
                    order.Add("D149913Fa5ace13d63c24085a595142f424a9826", demands);
                    orders.Add(order);
                }
            }

            response.ReturnData = new Dictionary<string, object>();
            response.ReturnData.Add("bomOrders", orders);
            return;
        }

        base.OnSubmit(actionName, postValue, response);
    }
    protected override void OnWorkflowInstanceStateChanged(H3.Workflow.Instance.WorkflowInstanceState oldState, H3.Workflow.Instance.WorkflowInstanceState newState)
    {
        //流程审批结束,业务规则执行。还有个审批流程结束（finished状态下直接删除），就直接删除也会触发业务规则执行。在submit里面处理
        if (oldState == H3.Workflow.Instance.WorkflowInstanceState.Running && newState == H3.Workflow.Instance.WorkflowInstanceState.Finished)
        {
            H3.DataModel.BizObject plan = this.Request.BizObject;
            H3.DataModel.BizObject[] materialDetails = (H3.DataModel.BizObject[])this.Request.BizObject["D149913Fqbt7kofydy0n11q8b875dckc6"];
            if (materialDetails != null && materialDetails.Length > 0)
            {
                foreach (H3.DataModel.BizObject materialDetail in materialDetails)
                {
                    string productId = materialDetail["F0000005"] + string.Empty;
                    //查询库存
                    string sqlpo = "select F0000006,F0000014 from i_D149913b779923207a14917a7e6b909ddcf3863 where F0000001 = '" + productId + "'";
                    System.Data.DataTable stockTable = this.Request.Engine.Query.QueryTable(sqlpo, null);
                    int stockNum = 0;
                    int warningNum = 0;
                    if (stockTable != null && stockTable.Rows.Count > 0)
                    {
                        stockNum = Convert.ToInt32(String.IsNullOrEmpty(stockTable.Rows[0]["F0000006"] + "") ? "0" + stockTable.Rows[0]["F0000006"] : stockTable.Rows[0]["F0000006"]);
                        warningNum = Convert.ToInt32(String.IsNullOrEmpty(stockTable.Rows[0]["F0000014"] + "") ? "0" + stockTable.Rows[0]["F0000014"] : stockTable.Rows[0]["F0000014"]);
                    }

                    int useNum = Convert.ToInt32(materialDetail["F0000006"]);
                    int avaiableNum = stockNum - warningNum;
                    //库存可用量 与待使用量比较
                    if (useNum > avaiableNum)
                    {
                        int purchaseNum = useNum - avaiableNum;
                        if (avaiableNum < 0)
                        { // 库存小于0 ， 则采购数为当前待使用数
                            purchaseNum = useNum;
                        }
                        H3.DataModel.BizObjectSchema purchasePlanSchema = this.Engine.BizObjectManager.GetPublishedSchema("D149913Sh59f0tsbpx9bnw37i9p1y5hk3");
                        H3.DataModel.BizObject purchasePlan = new H3.DataModel.BizObject(this.Engine, purchasePlanSchema, this.Request.UserContext.UserId);
                        purchasePlan["F0000016"] = "生产/材料计划"; //来源
                        purchasePlan["F0000017"] = plan["SeqNo"]; //来源单号
                        purchasePlan["F0000010"] = materialDetail["F0000024"]; //备注
                        purchasePlan["F0000018"] = productId; //产品信息
                        purchasePlan["F0000026"] = materialDetail["F0000019"];//品牌
                        purchasePlan["F0000021"] = materialDetail["F0000012"];//规格型号
                        purchasePlan["F0000022"] = materialDetail["F0000007"];//单位
                        purchasePlan["F0000023"] = purchaseNum; //需求数量
                        purchasePlan["F0000025"] = purchaseNum;//剩余采购数量
                                                               //purchasePlan["F0000028"] = materialDetail["F0000101"];//产品小类
                        purchasePlan["F0000029"] = plan["F0000017"];// 交货日期
                        purchasePlan["F0000011"] = plan["F0000004"]; //销售订单
                        purchasePlan.Status = H3.DataModel.BizObjectStatus.Effective; //设置状态生效
                        purchasePlan.Create(); //创建采购需求计划
                    }
                }

            }
        }
        base.OnWorkflowInstanceStateChanged(oldState, newState);
    }
}