import req  from '../util/request.js'
import Notify from '../util/notify.js'
const notify = new Notify()

let proxy = process.env.REQ_PROXY || '192.168.1.112:7890';
if (proxy) {
  if (/^\d{3}\..*?\d$/) {
    proxy = 'http://' + proxy
  }
}

let url = 'https://freemycloud.me'

let userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.54 Safari/537.36'

  ; (async () => {
    let resp = await req.post(`${url}/auth/login`, {
      headers: {
        'user-agent': userAgent
      },

      form: {
        email: "<EMAIL>",
        passwd: "lIZY0Ob!5ojx",
      }
    }, {_auto:false,  _proxy: proxy})
    let cookie = ''
    if (res.statusCode === 200) {
      console.log(resp.body);
      let body = JSON.parse(resp.body);
      if (body.ret === 1) {
        let cookies = res.headers["set-cookie"];

        cookie = cookies.join(";")
      }

    } else {
      console.log(_body);
    }
    if (!cookie) {
      console.log('获取cookie失败')
      return
    }
    resp = await req.post(`${url}/user/checkin`, {
      proxy: proxy,
      headers: {
        Cookie: cookie,
        'user-agent': userAgent
      },
    })
    console.log(resp);
    notify.sendText('freemycloud\n' + JSON.stringify(resp))
  })();
