io.swagger.enabled: false
server:
  port: 8024
spring:
  main:
    banner-mode: off
  application:
    name: schedule
  redis:
    #host: r-ww69f13ae47defb4.redis.rds.cdtocc-inc.gov
    host: *************
    port: 6379
    password: 1qaz2WSX
    database: 0
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      fail-on-empty-beans: false
  groovy:
    template:
      check-template-location: false

logging:
  config: /root/app/schedule/logback-spring.xml
  level:
    org.springframework.context.support.ResourceBundleMessageSource: ERROR


exception.enable-global-exception-handler: false

com:
  cloudpay:
    dingtalk:
      corp-id: ding24267832f03477a3f5bf40eda33b7ba0
      app-key: dinguql7mnupm5e3vnvx
      app-secret: DTzg5odW5CfnPIL0Oo825O_8JzXcWmXC1JIdFTIzPKQyFyi-kgjs_xVFWtDG0APA
      agent-id: **********
      logo-id: '@lALPDgCwQmo4lLTM8Mzw'
      js-api-url: 'http://************:6842/'
      ding-url:
        driver: http://www.4399.com # 跳转任务执行页
        volunteer: https://open-dev.dingtalk.com # 跳转任务执行页
      todo-url:
        confirm_task: http://************:6842?corp_id=${com.cloudpay.dingtalk.corp-id}&path=task-wait-check #跳转任务确认页
        scheduling: http://************:6842?corp_id=${com.cloudpay.dingtalk.corp-id}&path=dispatcher/overview # 跳转发车审核页
        driver_task: http://************:6842?corp_id=${com.cloudpay.dingtalk.corp-id}&path=driver/regularBus #司机的任务详情页面
        volunteer_task: http://************:6842?corp_id=${com.cloudpay.dingtalk.corp-id}&path=volunteer/bus #志愿者的任务详情页面
    auth:
      exclude-paths:
        - /token
        - /v1/open_api/**
    open-api:
      expire: 1800
      secret-key: xJSyeLIIgbGDfjbioDQmTHHSaMDmH41i
      public-key: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDt30AcO8CSAfzSa5L8ikVrfehH6aFw9KyL85NzOAduOfnPcbiAGLjLWEKOkOhkYrlSAfU5s+pa3OQTsgpfCkVVm56dEQh8sajIR4uyGbhv0/CdvPTZS5o3sP6Yi9TemWZ443+QNjajN6MSCTmTY86ZoR9jmTcJtV4kNTQWDov6qQIDAQAB
      default-api-url: http://192.167.241.10:9101
#      default-api-url: http://192.168.1.78:8026


valid-pattern:
  patterns:
    mobile: "^((13[0-9])|(14[0,1,4-9])|(15[0-3,5-9])|(16[2,5,6,7])|(17[0-8])|(18[0-9])|(19[0-3,5-9]))\\d{8}$"
    email: "^([a-z0-9A-Z]+[-|\\.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$"
