version: '3'
services:
    rabbitmq1:
        image: rabbitmq:management
        container_name: rabbitmq1
        hostname: rabbitmq1
        environment:
            - RABBITMQ_DEFAULT_USER=${RABBITMQ_DEFAULT_USER}
            - RABBITMQ_DEFAULT_PASS=${RABBITMQ_DEFAULT_PASS}
            - RABBITMQ_NODENAME=rabbitmq1
            - RABBITMQ_ERLANG_COOKIE=CURIOAPPLICATION
        volumes:
            - /srv/rabbit_cluster/master:/var/lib/rabbitmq
        networks:
            - rabbit
   
    rabbitmq2:
        image: rabbitmq:management
        container_name: rabbitmq2
        hostname: rabbitmq2
        environment:
            - RABBITMQ_DEFAULT_USER=${RABBITMQ_DEFAULT_USER}
            - RABBITMQ_DEFAULT_PASS=${RABBITMQ_DEFAULT_PASS}
            - RABBITMQ_NODENAME=rabbitmq2
            - RABBITMQ_ERLANG_COOKIE=CURIOAPPLICATION
        volumes:
            - /srv/rabbit_cluster/slave1:/var/lib/rabbitmq
            - ./cluster-entrypoint.sh:/usr/local/bin/cluster-entrypoint.sh
        entrypoint: /usr/local/bin/cluster-entrypoint.sh
        networks:
            - rabbit
        depends_on:
            - rabbitmq1
       
    rabbitmq3:
        image: rabbitmq:management
        container_name: rabbitmq3
        hostname: rabbitmq3
        environment:
            - RABBITMQ_DEFAULT_USER=${RABBITMQ_DEFAULT_USER}
            - RABBITMQ_DEFAULT_PASS=${RABBITMQ_DEFAULT_PASS}
            - RABBITMQ_NODENAME=rabbitmq3
            - RABBITMQ_ERLANG_COOKIE=CURIOAPPLICATION
        volumes:
            - /srv/rabbit_cluster/slave2:/var/lib/rabbitmq
            - ./cluster-entrypoint.sh:/usr/local/bin/cluster-entrypoint.sh
        entrypoint: /usr/local/bin/cluster-entrypoint.sh
        networks:
            - rabbit
        depends_on:
            - rabbitmq1
    haproxy:
        image: haproxy
        container_name: haproxy
        volumes:
            - ./haproxy.cfg:/usr/local/etc/haproxy/haproxy.cfg:ro
        depends_on:
            - rabbitmq1
            - rabbitmq2
            - rabbitmq3
        networks:
            - rabbit
        ports:
            - 15672:15672
            - 5672:5672
networks:
    rabbit:
        external:
            name: rabbitmqnet
