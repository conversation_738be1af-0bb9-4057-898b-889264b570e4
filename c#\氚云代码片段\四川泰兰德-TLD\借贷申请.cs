
using System;
using System.Collections.Generic;
using System.Text;
using H3;

public class D149913c6f7b04b72e24b0e8d22e9b0eaa05f83 : H3.SmartForm.SmartFormController
{
    public D149913c6f7b04b72e24b0e8d22e9b0eaa05f83(H3.SmartForm.SmartFormRequest request) : base(request)
    {
    }

    protected override void OnLoad(H3.SmartForm.LoadSmartFormResponse response)
    {
        base.OnLoad(response);
    }

    protected override void OnSubmit(string actionName, H3.SmartForm.SmartFormPostValue postValue, H3.SmartForm.SubmitSmartFormResponse response)
    {
        H3.DataModel.BizObject[] details = (H3.DataModel.BizObject[])this.Request.BizObject["D149913F4c0aa09bb34747e0a633eb77a43ccb91"]; // 库存明细

        Dictionary<string, int> dict = new Dictionary<string, int>();
        // 合并同类产品
        foreach (H3.DataModel.BizObject detail in details)
        {
            string productId = detail["F0000008"] + string.Empty; // 产品id
            string remark = detail["F0000016"] + string.Empty; // 参数备注
            int outNum = Convert.ToInt32(String.IsNullOrEmpty(detail["F0000009"] + "") ? "0" + detail["F0000009"] : detail["F0000009"]);
            dict.Add(productId + "_" + remark, outNum);
        }
        // 库存校验
        foreach (KeyValuePair<string, int> pair in dict)
        {
            //查询库存,产品id + 参数备注
            string[] result = pair.Key.Split('_');
            string sqlpo = "select F0000007,F0000014,F0000011 from i_D149913b779923207a14917a7e6b909ddcf3863 where F0000001 = '" + result[0] + "' and F0000011 = '" + result[1] + "'"; // 查询库存实际量
            System.Data.DataTable stockTable = this.Request.Engine.Query.QueryTable(sqlpo, null);
            int stockNum = 0;
            int warningNum = 0;
            if (stockTable != null && stockTable.Rows.Count > 0)
            {
                stockNum = Convert.ToInt32(String.IsNullOrEmpty(stockTable.Rows[0]["F0000007"] + "") ? "0" + stockTable.Rows[0]["F0000007"] : stockTable.Rows[0]["F0000007"]);
            }
            if (stockNum<=0 ||stockNum < pair.Value)
            {
                rollBackWorkflow(response, "存在产品出库数量超过库存数量");
                return;
            }
        }
        base.OnSubmit(actionName, postValue, response);
    }
    private void rollBackWorkflow(H3.SmartForm.SubmitSmartFormResponse response, string error)
    {
        string code = this.Request.ActivityCode;
        if (code != "Activity2")
        {
            H3.Workflow.Messages.CancelActivityMessage cancelMessage = new H3.Workflow.Messages.CancelActivityMessage(this.Request.InstanceId, this.Request.ActivityCode, true);
            this.Request.Engine.WorkflowInstanceManager.SendMessage(cancelMessage);
        }

        response.ReturnData = new Dictionary<string, object>();
        response.ReturnData.Add("error", error);
    }
}