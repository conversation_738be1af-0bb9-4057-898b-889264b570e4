
using System;
using System.Collections.Generic;
using System.Text;
using H3;
using H3.DataModel;
using H3.Data.Filter;
using System.Data;
using System.Text.RegularExpressions;

public class D282605d63617ddbc1848fb8c93f4a3fa4e59c3: H3.SmartForm.SmartFormController
{
    public D282605d63617ddbc1848fb8c93f4a3fa4e59c3(H3.SmartForm.SmartFormRequest request): base(request)
    {
    }

    protected override void OnLoad(H3.SmartForm.LoadSmartFormResponse response)
    {
        base.OnLoad(response);
    }

    protected override void OnSubmit(string actionName, H3.SmartForm.SmartFormPostValue postValue, H3.SmartForm.SubmitSmartFormResponse response)
    {

        DPuXinNewProcessSheet sheet = new DPuXinNewProcessSheet(this.Engine, this.Request.UserContext.UserId);

        DPuXinError error = sheet.Validate(this.Request.BizObject);
        if(error.HasError())
        {
            response.ReturnData = BuildResp(error);
            return;
        }

        base.OnSubmit(actionName, postValue, response);
    }

    protected override void OnWorkflowInstanceStateChanged(H3.Workflow.Instance.WorkflowInstanceState oldState, H3.Workflow.Instance.WorkflowInstanceState newState)
    {

        //流程审批结束,业务规则执行。还有个审批流程结束（finished状态下直接删除），就直接删除也会触发业务规则执行。在submit里面处理
        if(oldState == H3.Workflow.Instance.WorkflowInstanceState.Running && newState == H3.Workflow.Instance.WorkflowInstanceState.Finished)
        {
            DPuXinNewProcessSheet sheet = new DPuXinNewProcessSheet(this.Engine, this.Request.UserContext.UserId);
            //获取销售订单
            string objectId = this.Request.BizObjectId;
            sheet.WriteToMaterial(objectId);
            sheet.WriteToPlanBySheet(objectId);
        }

        base.OnWorkflowInstanceStateChanged(oldState, newState);
    }
    public Dictionary < string, object > BuildResp(DPuXinError xinError)
    {
        Dictionary < string, object > resp = new Dictionary<string, object>();
        if(!string.IsNullOrEmpty(xinError.GetError()))
        {
            resp.Add("Error", xinError.GetError());
        }
        if(xinError.GetErrors() != null && xinError.GetErrors().Count > 0)
        {
            resp.Add("Errors", this.Serialize(xinError.GetErrors()));
        }
        return resp;
    }
}

public class DPuXinBaseSheet
{
    protected IEngine Engine;
    protected string UserId;
    protected string saleOrderSchemaCode = "D28260598e613342c0d4ca9ad13fc1e15a43824"; // 销售订单
    protected string saleOrderChildSchemaCode = "D282605Fb33056b961f84fb0bdf718683d9a87fb"; // 销售订单明细
    protected string changeProcessSheetSchemaCode = "D2826056a932d51574547fab837914c63520a03"; // 工艺变更单
    protected string changeProcessSheetChildSchemaCode = "D282605F7738bb0709f242e6a312f25edc1bcd9b"; // 工艺变更单明细
    protected string processSheetSchemaCode = "D282605d63617ddbc1848fb8c93f4a3fa4e59c3"; // 工艺单
    protected string processSheetChildSchemaCode = "D282605Ff555d078d0d54575aab7339874c31848"; // 工艺单子明细
    protected string processSheetDetailSchemaCode = "D282605Sz5g9cj2b7tj1pr4ax1ao7prg4"; // 工艺单明细
    protected string planSchemaCode = "D282605Sbces9w53iq957t4gc8vulc6y2";// 货架主计划
    protected string planChildSchemaCode = "D282605F80a2dc5e16c74b21813e7f1b5ad3615b"; //货架主计划进度
    protected string materialSchemaCode = "D282605suten5clu70qwo9gkhtlp";//物料基础信息
    protected string reportSchemaCode = "D2826052468b3ca3f39406a80a5ee6055d35ec1"; //货架报工
    protected string reportChildSchemaCode = "D282605Fd8a58da6e52d4efe942395b6048e06f0"; // 货架报工子明细
    protected string reportDetailSchemaCode = "D282605swgcbfrfszeetpkvlahrz"; //货架报工明细
    protected string inventorySchemaCode = "D282605Swtn30nwzlozmh2upo2qsnvmp0"; // 库存
    protected string inventoryChildSchemaCode = "D282605F76c8c2580d3244c4b8fe0774cde6f917"; // 库存明细
    protected string timeReportSchemaCode = "D282605bc3261154c0e47909559ff20cb5c6539";//计时报工
    protected string reworkReportSchemaCode = "D282605Spr8bymjy1zp6rol2sumj00cw4"; //返工报工
    protected string boxReportSchemaCode = "D282605Seibgfbqg1mf1id55ejcjoz6f3"; // 表箱报工
    protected string boxPlanSchemaCode = "D282605Skmj5js0xdpn3oxyqjy56mw0u3"; //表箱计划
    protected string boxReportChildSchemaCode = "D282605F9ca39816aecd46baba26c8004d2d8716";//表箱报子明细
    protected string boxReportDetailSchemaCode = "D282605Sc2hh33czw2c9935a17pfux831";//表箱报工明细

    public DPuXinBaseSheet(IEngine Engine, string UserId)
    {
        this.Engine = Engine;
        this.UserId = UserId;
    }

    public BizObject Create(string schemaCode)
    {
        BizObjectSchema schema = this.Engine.BizObjectManager.GetPublishedSchema(schemaCode);
        BizObject obj = new BizObject(this.Engine, schema, this.UserId);
        obj.Status = BizObjectStatus.Effective; //设置状态生效
        return obj;
    }

    public BizObject CreateChild(string schemaCode, string childSchemaCode)
    {
        BizObjectSchema schema = this.Engine.BizObjectManager.GetPublishedSchema(schemaCode);
        return new BizObject(this.Engine, schema.GetChildSchema(childSchemaCode), this.UserId);
    }


    public BizObject Load(string schema, object id)
    {
        return BizObject.Load(this.UserId,
            this.Engine, schema, Convert.ToString(id), false);
    }
    public BizObject[] GetList(string schema, H3.Data.Filter.And andMatcher)
    {
        H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();
        filter.Matcher = andMatcher;
        return GetList(schema, filter);
    }

    public BizObject[] GetList(string schema, Filter filter)
    {
        BizObjectSchema bizObjectSchema = this.Engine.BizObjectManager.GetPublishedSchema(schema);
        BizObject[] result = BizObject.GetList(this.Engine, this.UserId, bizObjectSchema, GetListScopeType.GlobalAll, filter);
        return result;
    }

    public void SyncData(BizObject from, BizObject to)
    {
        string[] excludes = { "ParentObjectId", "State", "ObjectId"};
        this.SyncData(from, to, excludes);
    }
    public void SyncData(BizObject from, BizObject to, string[] excludes)
    {
        Dictionary < string, object > fromTable = from.GetValueTable();
        Dictionary < string, object > toTable = to.GetValueTable();
        Dictionary < string, object > table = new Dictionary<string, object>();
        foreach(KeyValuePair < string, object > pair in toTable)
        {
            string key = pair.Key;
            if(key.Contains("."))
            {
                key = key.Substring(key.IndexOf(".") + 1);
            }
            if(!table.ContainsKey(key))
            {
                table.Add(key, pair.Value);
            }
        }
        foreach(KeyValuePair < string, object > pair in fromTable)
        {
            string key = pair.Key;
            if(key.Contains("."))
            {
                key = key.Substring(key.IndexOf(".") + 1);
            }
            object value = pair.Value;

            if(excludes != null && excludes.Length > 0 && Exist(excludes, key))
            {
                continue;
            }
            if(!table.ContainsKey(key))
            {
                continue;
            }
            try
            {
                to[key] = value;
            } catch(Exception e)
            { }
        }
    }
    public string GetField(BizObject obj, string key)
    {
        try
        {
            return Convert.ToString(obj[key]);
        }
        catch(Exception ex)
        {
            return "";
        }
    }
    public bool Contains(object obj, string key)
    {
        string result = Convert.ToString(obj);
        return result.Contains(key);
    }
    public bool Exist(string[] array, string key)
    {
        foreach(string a in array)
        {
            if(a == key)
            {
                return true;
            }
        }
        return false;
    }
    public BizObject FindByKey(List < BizObject > list, string key, object value)
    {
        foreach(BizObject obj in list)
        {
            if(Convert.ToString(obj[key]) == Convert.ToString(value))
            {
                return obj;
            }
        }
        return null;
    }
    public List<BizObject> GetChild(BizObject parent, string childSchemaCode)
    {
        BizObject[] children = (BizObject[]) parent[childSchemaCode];
        if(IsEmpty(children))
        {
            return new List<BizObject>();
        }
        return new List<BizObject>(children);
    }

    public int ToInt(object obj)
    {
        return Convert.ToInt32(String.IsNullOrEmpty(obj + "") ? "0" + obj : obj);
    }
    public bool IsEmpty(object obj)
    {
        if(obj == null)
        {
            return true;
        }
        var type = obj.GetType();
        if(obj.GetType().IsArray && (obj as Array).Length == 0)
        {
            return true;
        }

        if(type.ToString().Contains("System.Collections.Generic.List"))
        {
            var property = type.GetProperty("Count");
            int count = Convert.ToInt32(property.GetValue(obj));
            if(count == 0)
            {
                return true;
            }
        }
        if(string.IsNullOrEmpty(obj + string.Empty))
        {
            return true;
        }
        return false;
    }

    public void DeleteById(string schema, object id)
    {
        string sql = "delete from i_{0} where ObjectId = '{1}';";
        this.Engine.EngineConfig.CreateCommand().ExecuteScalar(string.Format(sql, schema, id));
    }

    public Dictionary<string, object> ConvertBiz(BizObject obj, string[] excludes)
    {
        Dictionary < string, object > objTable = obj.GetValueTable();
        Dictionary < string, object > table = new Dictionary<string, object>();
        foreach(KeyValuePair < string, object > pair in objTable)
        {
            string key = pair.Key;
            if(key.Contains("."))
            {
                key = key.Substring(key.IndexOf(".") + 1);
            }
            if(excludes != null && excludes.Length > 0 && Exist(excludes, key))
            {
                continue;
            }
            if(!table.ContainsKey(key))
            {
                var value = pair.Value;
                if(value != null)
                {
                    var type = value.GetType();
                    if(type.ToString().Contains("H3.DataModel.BizObject[]"))
                    {
                        BizObject[] items = (BizObject[]) value;
                        List < Dictionary < string, object >> list= new List<Dictionary<string, object>>();
                        foreach(BizObject item in items)
                        {
                            Dictionary < string, object > result = this.ConvertBiz(item, excludes);
                            list.Add(result);
                        }
                        value = list;
                    }
                }

                table.Add(key, value);
            }
        }
        return table;
    }

    public Dictionary<string, object> ConvertBiz(BizObject obj)
    {
        return this.ConvertBiz(obj, null);
    }

    public string[] Split(object obj, char splitter)
    {
        if(IsEmpty(obj))
        {
            return null;
        }
        string src = Convert.ToString(obj);
        src = Regex.Replace(src, @"\s", "");
        string s = splitter.ToString();
        if(src.EndsWith(s))
        {
            src = src.Substring(0, src.Length - 1);
        }
        return src.Split(splitter);
    }
}

public class DPuXinNewProcessSheet: DPuXinBaseSheet
{
    private DPuXinMaterialCode materialCode;
    private DPuXinSheetPlan sheetPlan;
    public DPuXinNewProcessSheet(IEngine Engine, string UserId): base(Engine, UserId)
    {
        this.materialCode = new DPuXinMaterialCode(Engine, UserId);
        this.sheetPlan = new DPuXinSheetPlan(Engine, UserId);
    }

    public DPuXinError Validate(BizObject sheet)
    {
        DPuXinError error = new DPuXinError();
        try
        {
            string orderNo = sheet["OrderNo"] + string.Empty;
            BizObject old = QueryProcessSheet(orderNo);
            if(old != null && GetField(sheet, "ObjectId") != GetField(old, "ObjectId"))
            {
                throw new Exception("当前已存在该订单号工艺单，请勿重复提交");
            }
            BizObject[] details = (BizObject[]) sheet[this.processSheetChildSchemaCode];
            decimal sum = 0;
            foreach(BizObject detail in details)
            {
                string materialName = detail["MaterialName"] + string.Empty;

                try
                {
                    string serialNo = detail["SerialNo"] + string.Empty;
                    if(IsEmpty(serialNo))
                    {
                        throw new Exception("物料没有序号");
                    }
                    bool isMatch = Regex.IsMatch(serialNo, @"^([0-9]{1,}\.)*?[0-9]{1,}$");
                    if(!isMatch) {
                        throw new Exception("物料序号不正确");
                    }
                    string processFlow = detail["ProcessFlow"] + string.Empty;
                    if(processFlow.Contains("打包"))
                    {
                        Dictionary < string, string > dict = QuerySaleOrder(orderNo, materialName);
                        if(dict == null)
                        {
                            throw new Exception("未找到对应的成品物料");
                        }
                        detail["MaterialNo"] = dict["MaterialNo"];
                        detail["MaterialType"] = "成品";
                        sum += Convert.ToDecimal(detail["NetSumWeight"]);
                    }
                    else
                    {
                        detail["MaterialType"] = "半成品";
                    }
                }
                catch(Exception ex)
                {
                    error["OrderNo"] = orderNo + string.Empty;
                    error["MaterialName"] = detail["MaterialName"] + string.Empty;
                    error["Error"] = ex.Message;
                    error.PutError();
                }
            }
            Array.Sort(details, CompareToSort);
            sheet["SumWeight"] = sum / 1000;
            sheet["ProjectFinishedDate"] = "--";
            sheet[this.processSheetChildSchemaCode] = details;
        }
        catch(Exception ex)
        {
            error.Error(ex.Message);
        }
        return error;
    }
    // 工艺单写入物料信息
    public void WriteToMaterial(string objectId)
    {
        BizObject sheet = Load(this.processSheetSchemaCode, objectId);
        List < BizObject > details = GetChild(sheet, this.processSheetChildSchemaCode);
        string orderNo = sheet["OrderNo"] + string.Empty;

        foreach(BizObject detail in details)
        {
            SaveToMaterial(orderNo, detail);
            SetMaterialNo(details, detail);
        }

        RepairRelation(details);
        sheet[this.processSheetChildSchemaCode] = details.ToArray();
        sheet.Update();
    }

    public void SaveToMaterial(string orderNo, BizObject detail)
    {
        // 查询基础物料信息，是否已存在
        if(Contains(detail["ProcessFlow"], "打包"))
        {
            Dictionary < string, string > dict = QuerySaleOrder(orderNo, GetField(detail, "MaterialName"));
            if(dict == null)
            {
                throw new Exception("未找到对应的成品物料");
            }
            detail["MaterialNo"] = dict["MaterialNo"];
        }
        string materialNo = detail["MaterialNo"] + string.Empty;
        if(string.IsNullOrEmpty(materialNo))
        {
            materialNo = this.materialCode.Create();
            detail["MaterialNo"] = materialNo;
        }
        BizObject material = QueryMaterial(materialNo);
        bool create = false;
        if(material == null)
        {
            material = Create(this.materialSchemaCode);
            create = true;
            material["CreateDate"] = System.DateTime.Now;
            material["Creater"] = this.Engine.Organization.GetName(this.UserId, H3.Organization.NameType.Name);
            material["CreateOrg"] = "四川普鑫物流自动化设备工程有限公司";
            material["Source"] = "氚云";
            material["UseOrg"] = "四川普鑫物流自动化设备工程有限公司";
            SyncData(detail, material);
        }

        var r = create ? material.Create() : material.Update();
        detail["Material"] = material["ObjectId"];
    }

    public void SetMaterialNo(List < BizObject > details, BizObject obj)
    {
        foreach(BizObject detail in details)
        {
            if(Convert.ToString(detail["MaterialName"]) == Convert.ToString(obj["MaterialName"]))
            {
                detail["MaterialNo"] = obj["MaterialNo"];
            }
            if(Convert.ToString(detail["ParentSerialNo"]) == Convert.ToString(obj["SerialNo"]))
            {
                detail["ParentMaterialNo"] = obj["MaterialNo"];
            }
        }
    }
    public void RepairRelation(List < BizObject > details)
    {
        //details.Sort(CompareToSort);
        foreach(BizObject detail in details)
        {
            string serialNo = GetField(detail, "SerialNo");
            detail["Sort"] = Regex.Replace(serialNo, @"\.", "").PadRight(6, '0');
            int index = serialNo.LastIndexOf(".");
            if(index < 0)
            {
                detail["ParentSerialNo"] = null;
                detail["ParentMaterialNo"] = null;
                continue;
            }

            string parentSerialNo = serialNo.Substring(0, index);

            foreach(BizObject next in details)
            {
                string nextSerialNo = GetField(next, "SerialNo");
                if(nextSerialNo == parentSerialNo)
                {
                    detail["ParentSerialNo"] = parentSerialNo;
                    detail["ParentMaterialNo"] = next["MaterialNo"];
                    break;
                }
            }
        }
    }
    private int CompareToSort(BizObject x, BizObject y)
    {
        int a = Convert.ToInt32(x["Sort"]);
        int b = Convert.ToInt32(y["Sort"]);
        return a > b ? 1 : (a == b ? 0 : -1);
    }

    //写入主计划
    public void WriteToPlanBySheet(string objectId)
    {
        //查询工艺单根据订单号
        BizObject sheet = Load(this.processSheetSchemaCode, objectId);
        BizObject[] details = (BizObject[]) sheet[this.processSheetChildSchemaCode];

        List < BizObject > plans = new List<BizObject>();
        //数据合并
        foreach(BizObject detail in details)
        {
            BizObject exist = FindByKey(plans, "MaterialNo", GetField(detail, "MaterialNo"));
            if(exist == null)
            {
                exist = Create(this.planSchemaCode);
                exist["Customer"] = sheet["Customer"];
                exist["ProjectName"] = sheet["ProjectName"];
                exist["OrderNo"] = sheet["OrderNo"];
                exist["DeliveryDate"] = sheet["DeliveryDate"];
                exist["Weight"] = detail["NetWeight"];
                exist["SumWeight"] = detail["NetSumWeight"];
                SyncData(detail, exist);
                plans.Add(exist);
            }
            else
            {
                exist["Count"] = this.ToInt(exist["Count"]) + this.ToInt(detail["Count"]);
                //exist["SprayingSumArea"] = Convert.ToDecimal(exist["SprayingSumArea"]) + Convert.ToDecimal(detail["SprayingSumArea"]);
                exist["SumWeight"] = Convert.ToDecimal(exist["SumWeight"]) + Convert.ToDecimal(detail["NetSumWeight"]);
            }
            SaveToDetail(detail, sheet);
        }
        // 批量写入计划
        foreach(BizObject plan in plans)
        {
            this.WriteToPlan(plan);
        }
    }
    public BizObject SaveToDetail(BizObject obj, BizObject root)
    {
        BizObject detail = Load(this.processSheetDetailSchemaCode, obj["ObjectId"] + string.Empty);
        bool isCreate = false;
        if(detail == null)
        {
            detail = Create(this.processSheetDetailSchemaCode);
            isCreate = true;
        }
        detail["Customer"] = root["Customer"];
        detail["ProjectName"] = root["ProjectName"];
        detail["OrderNo"] = root["OrderNo"];
        detail["DeliveryDate"] = root["DeliveryDate"];
        SyncData(obj, detail);
        detail["ObjectId"] = obj["ObjectId"];
        var r = isCreate ? detail.Create() : detail.Update();
        return detail;
    }


    // 生成计划
    public void WriteToPlan(BizObject plan)
    {
        // 查询计划中是否已存在该物料
        bool create = false;
        BizObject src = this.QueryPlan(GetField(plan, "OrderNo"), GetField(plan, "MaterialNo"));
        if(src == null)
        {
            create = true;
            src = plan;
            src["ReceiptDate"] = System.DateTime.Today;
        }
        else
        {
            SyncData(plan, src);
        }

        this.sheetPlan.Init(src);

        var r = create ? src.Create() : src.Update();
    }

    // 查询工艺单是否已存在
    private bool CheckInProcessSheet(string serialNo, string orderNo, string materialNo)
    {
        if(string.IsNullOrEmpty(materialNo) || string.IsNullOrEmpty(orderNo)) return false;

        string sql = String.Format("select SerialNo, OrderNo, MaterialNo from i_{0} where SerialNo = '{1}' and OrderNo = '{2}' and MaterialNo = '{3}'",
            this.processSheetSchemaCode, serialNo, orderNo, materialNo);
        DataTable table = this.Engine.Query.QueryTable(sql, null);

        if(table != null && table.Rows.Count > 0)
        {
            return true;
        }
        return false;
    }
    // 查询物料是否已存在
    public Dictionary < string, string > QuerySaleOrder(string orderNo, string materialName)
    {
        if(string.IsNullOrEmpty(materialName) || string.IsNullOrEmpty(orderNo)) return null;
        string sql = String.Format("select o.OrderNo OrderNo, c.MaterialNo MaterialNo from i_{0} o inner join i_{1} c on o.ObjectId = c.ParentObjectId where OrderNo = '{2}' and MaterialName = '{3}'",
            this.saleOrderSchemaCode, this.saleOrderChildSchemaCode, orderNo, materialName);
        DataTable table = this.Engine.Query.QueryTable(sql, null);
        if(table == null || table.Rows.Count == 0)
        {
            return null;
        }
        var row = table.Rows[0];
        Dictionary < string, string > obj = new Dictionary<string, string>();
        obj["OrderNo"] = row["OrderNo"] + string.Empty;
        obj["MaterialNo"] = row["MaterialNo"] + string.Empty;
        return obj;
    }

    // 查询物料是否已存在
    private BizObject QueryMaterial(string materialNo)
    {
        H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();
        filter.Matcher = new H3.Data.Filter.ItemMatcher("MaterialNo", H3.Data.ComparisonOperatorType.Equal, materialNo);

        BizObject[] materials = BizObject.GetList(this.Engine, this.UserId,
            this.Engine.BizObjectManager.GetPublishedSchema(this.materialSchemaCode),
            GetListScopeType.GlobalAll, filter);

        if(materials == null || materials.Length == 0)
        {
            return null;
        }
        return materials[0];
    }

    public BizObject QueryPlan(string orderNo, string materialNo)
    {
        H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();

        H3.Data.Filter.And andMatcher = new H3.Data.Filter.And();
        andMatcher.Add(new H3.Data.Filter.ItemMatcher("OrderNo", H3.Data.ComparisonOperatorType.Equal, orderNo));
        andMatcher.Add(new H3.Data.Filter.ItemMatcher("MaterialNo", H3.Data.ComparisonOperatorType.Equal, materialNo));
        filter.Matcher = andMatcher;

        BizObject[] plans = BizObject.GetList(this.Engine, this.UserId,
            this.Engine.BizObjectManager.GetPublishedSchema(this.planSchemaCode),
            GetListScopeType.GlobalAll, filter);

        if(plans == null || plans.Length == 0)
        {
            return null;
        }
        return plans[0];
    }
    private BizObject QueryProcessSheet(string orderNo)
    {
        H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();

        H3.Data.Filter.And andMatcher = new H3.Data.Filter.And();
        andMatcher.Add(new H3.Data.Filter.ItemMatcher("OrderNo", H3.Data.ComparisonOperatorType.Equal, orderNo));
        filter.Matcher = andMatcher;

        BizObject[] sheets = BizObject.GetList(this.Engine, this.UserId,
            this.Engine.BizObjectManager.GetPublishedSchema(this.processSheetSchemaCode),
            GetListScopeType.GlobalAll, filter);

        if(sheets == null || sheets.Length == 0)
        {
            return null;
        }
        return sheets[0];
    }
    // 查询计划是否已存在
    private bool CheckInPlan(string orderNo, string materialNo)
    {
        if(string.IsNullOrEmpty(materialNo) || string.IsNullOrEmpty(orderNo)) return false;
        string sql = String.Format("select OrderNo, MaterialNo from i_{0} where OrderNo = '{1}' and MaterialNo = '{2}'", this.planSchemaCode, orderNo, materialNo);
        DataTable table = this.Engine.Query.QueryTable(sql, null);

        if(table != null && table.Rows.Count > 0)
        {
            return true;
        }
        return false;
    }

    public void Delete(string[] objectIds)
    {
        // 取工艺单
        H3.Data.Filter.And andMatcher = new H3.Data.Filter.And();
        andMatcher.Add(new H3.Data.Filter.ItemMatcher("ObjectId", H3.Data.ComparisonOperatorType.In, objectIds));
        BizObject[] list = GetList(this.processSheetSchemaCode, andMatcher);
        // 明细表；
        foreach(BizObject sheet in list)
        {
            List < BizObject > details = GetChild(sheet, this.processSheetChildSchemaCode);
            foreach(BizObject detail in details)
            {
                string detailSql = string.Format("delete from i_{0} where ObjectId='{1}';", this.processSheetDetailSchemaCode, GetField(detail, "ObjectId"));

                string planSql = string.Format("delete from i_{0} where OrderNo = '{1}' and MaterialNo = '{2}';", this.planSchemaCode, GetField(sheet, "OrderNo"), GetField(detail, "MaterialNo"));
                string materialSql = string.Format("delete from i_{0} where MaterialNo = '{1}' and Source = '氚云';", this.materialSchemaCode, GetField(detail, "MaterialNo"));
                //throw new Exception("验证" + detailSql + planSql + materialSql);
                this.Engine.Query.QueryTable(detailSql + planSql + materialSql, null);
            }
            sheet.Remove();
        }

    }

    public void CheckStatus()
    {
        string initSql = string.Format("select * from i_{0} where SumWeight is null or SumWeight = ''", this.processSheetSchemaCode);
        DataTable initTable = this.Engine.Query.QueryTable(initSql, null);

        if(initTable != null && initTable.Rows.Count > 0)
        {
            foreach(DataRow row in initTable.Rows)
            {
                BizObject obj = Load(this.processSheetSchemaCode, row["ObjectId"].ToString());
                List < BizObject > children = GetChild(obj, this.processSheetChildSchemaCode);
                decimal sum = 0;
                foreach(BizObject item in children)
                {
                    if(GetField(item, "MaterialType") == "成品")
                    {
                        sum += Convert.ToDecimal(item["NetSumWeight"]);
                    }
                }
                obj["SumWeight"] = sum / 1000;
                if(IsEmpty(obj["ProjectFinishedDate"]))
                {
                    obj["ProjectFinishedDate"] = "--";
                }
                obj.Update();
            }
        }
        string sqla = string.Format("update i_{0} set ProjectStatus = '未完成' where ProjectStatus is null or ProjectStatus=''", this.processSheetSchemaCode);
        this.Engine.Query.QueryTable(sqla, null);
        string sql = string.Format("select distinct(OrderNo) from i_{0} where ProjectStatus = '未完成'", this.processSheetSchemaCode);
        DataTable table = this.Engine.Query.QueryTable(sql, null);
        if(table == null || table.Rows.Count == 0)
        {
            return;
        }

        string orderNos = GetOrderNos(table);

        string allStatSql = string.Format("select OrderNo, Count(*) Count,sum(Weight * Count) SumWeight, sum(Weight * Finished) CurrentSumWeight,Sum(Weight * QuantitySent) SentWeight  from i_{0} where OrderNo in ({1}) and MaterialType = '成品' group by OrderNo", this.planSchemaCode, orderNos);
        DataTable allStat = this.Engine.Query.QueryTable(allStatSql, null);
        string statSql = string.Format("select OrderNo, Count(*) Count from i_{0} where OrderNo in ({1}) and MaterialType = '成品' and ProcessStatus='发货结案' group by OrderNo", this.planSchemaCode, orderNos);
        DataTable stat = this.Engine.Query.QueryTable(statSql, null);
        if(allStat == null || allStat.Rows.Count == 0)
        {
            return;
        }

        string updateSql = "update i_{0} set ProductProgress='{1}',SentProgress='{2}'";
        foreach(DataRow row in allStat.Rows)
        {
            string orderNo = Convert.ToString(row["OrderNo"]);
            decimal sumWeight = ConvertDecimal(row["SumWeight"]);
            decimal currentSumWeight = ConvertDecimal(row["CurrentSumWeight"]);
            decimal productProgress = currentSumWeight / (sumWeight <= 0 ? 1 : sumWeight);
            decimal sentWeight = ConvertDecimal(row["SentWeight"]);
            decimal sentProgress = sentWeight / (sumWeight <= 0 ? 1 : sumWeight);
            string execSql = string.Format(updateSql, this.processSheetSchemaCode, productProgress, sentProgress);
            int src = ToInt(row["Count"]);
            int count = FindFinished(stat, orderNo);
            if(count >= src)
            {
                DateTime now = System.DateTime.Now;
                execSql += string.Format(",ProjectStatus='已完成',ProjectFinishedDate='{0}'", now.ToString("yyyy-MM-dd"));
            }
            else
            {
                execSql += ",ProjectFinishedDate='--'";
            }
            execSql += string.Format(" where OrderNo='{0}'", orderNo);
            //throw new Exception("验证" + execSql + "|" + src + "|" + count + "|" + sumWeight + "|" + currentSumWeight + "|" + sentWeight);
            this.Engine.Query.QueryTable(execSql, null);
        }

    }
    private decimal ConvertDecimal(object obj)
    {
        if(DBNull.Value.Equals(obj))
        {
            return 0;
        }
        if(IsEmpty(obj))
        {
            return 0;
        }
        return Math.Round(Convert.ToDecimal(obj) / 1000, 1);
    }

    private int FindFinished(DataTable table, object orderNo)
    {
        if(table == null || table.Rows.Count == 0)
        {
            return 0;
        }
        foreach(DataRow row in table.Rows)
        {
            int count = Convert.ToInt32(row["Count"]);
            if(Convert.ToString(row["OrderNo"]) == Convert.ToString(orderNo))
            {
                return count;
            }
        }
        return 0;
    }

    private Dictionary < string, int > ConvertToDict(DataTable table)
    {
        Dictionary < string, int > dict = new Dictionary<string, int>();
        foreach(DataRow row in table.Rows)
        {
            string orderNo = Convert.ToString(row["OrderNo"]);
            if(!dict.ContainsKey(orderNo))
            {
                dict.Add(orderNo, Convert.ToInt32(row["Count"]));
            }
        }
        return dict;
    }


    private string GetOrderNos(DataTable table)
    {
        string result = "";
        foreach(DataRow row in table.Rows)
        {
            string orderNo = row["OrderNo"] + string.Empty;
            if(string.IsNullOrEmpty(orderNo))
            {
                continue;
            }
            result += string.Format("'{0}',", row["OrderNo"]);
        }
        if(string.IsNullOrEmpty(result))
        {
            return result;
        }
        return result.Substring(0, result.Length - 1);
    }

}