/*
* $.ListView.GetSelected()获取选中的记录
* $.ListView.RefreshView()刷新列表
* $.ListView.Post()请求后台
* $.ListView.InitQueryItems()修改过滤条件
* $.ListView.RefreshView()刷新页面
* $.ListView.ActionPreDo() 按钮执行之前的事件
*/

$.ListView.ActionPreDo = function (actionCode) {
    if (actionCode == "dispatch") { //分配
        let selected = $.ListView.GetSelected()
        if(selected.length === 0) {
            $.IShowWarn("请先选择待分配数据");
            return
        }
        let data = selected.map(e => {
            return {
                'F0000001': e['ObjectId'],
            }
        })
        $.IShowForm("D2781517a1b8ea1d27a4be5bca9ed15b415c59f",
            null,
            {
                data: data,
                ListView: $.ListView
            },
            true,
            false, {
            showInModal: false,
            height: 500, width: 600, OnShowCallback: function (da) { }, onHiddenCallback: function (data) { }
        })
    }

};