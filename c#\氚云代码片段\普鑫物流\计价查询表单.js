/* 控件接口说明：
 * 1. 读取控件: this.***,*号输入控件编码;
 * 2. 读取控件的值： this.***.GetValue();
 * 3. 设置控件的值： this.***.SetValue(???);
 * 4. 绑定控件值变化事件： this.***.Bind<PERSON><PERSON>e(key,function(){})，key是定义唯一的方法名;
 * 5. 解除控件值变化事件： this.***.UnbindChange(key);
 * 6. CheckboxList、DropDownList、RadioButtonList: $.***.AddItem(value,text),$.***.ClearItems();
 */
/* 公共接口：
 * 1. ajax：$.SmartForm.PostForm(actionName,data,callBack,errorBack,async),
 *          actionName:提交的ActionName;data:提交后台的数据;callback:回调函数;errorBack:错误回调函数;async:是否异步;
 * 2. 打开表单：$.IShowForm(schemaCode, objectId, checkIsChange)，
 *          schemaCode:表单编码;objectId;表单数据Id;checkIsChange:关闭时，是否感知变化;
 * 3. 定位接口：$.ILocation();
 */
function findSelector(selector, element) {
  if (!element) element = document.body;
  if (!selector) return;
  if (element.className && (element.className + "").indexOf(selector) >= 0)
    return element;
  if (element.id == selector) return element;

  let children = element.children;

  if (children && children.length > 0) {
    for (let child of children) {
      let e = findSelector(selector, child);
      if (e) return e;
    }
  }
}
// 表单插件代码
$.extend($.JForm, {
  // 加载事件
  OnLoad: function () {},

  // 按钮事件
  OnLoadActions: function (actions) {},

  // 提交校验
  OnValidate: function (actionControl) {
    if (actionControl.Action == "Submit") {
      let ListView = $.IGetParams("ListView");

      let OrderNo = this.OrderNo.GetValue();
      let Team = this.Team.GetValue();
      let StartDate = this.StartDate.GetValue();
      let EndDate = this.EndDate.GetValue();
      if (!OrderNo && !StartDate) {
        $.IShowError("订单号或汇报开始日期必须选填一项");
        return false;
      }
      if (!StartDate && !EndDate) {
        if (StartDate < EndDate) {
          $.IShowError("汇报开始日期必须小于汇报结束日期");
          return false;
        }
      }
      let button = findSelector("submit-button");
      button.disabled = true;
      button.style.background = "#777";
      try {
        ListView.Post(
          "calc_price",
          { ObjectId: JSON.stringify({ OrderNo, Team, StartDate, EndDate }) },
          ({ Successful, ErrorMessage, ReturnData }) => {
            button.disabled = false;
            button.style.background = "#107fff";
            if (Successful) {
              let error = ReturnData?.Error;
              if (error) {
                $.IShowError(error);
              } else {
                ListView.RefreshView();
                $.IShowSuccess("计价完成");
                setTimeout(() => {
                  this.ClosePage();
                }, 1000);
              }
            } else {
              $.IShowError(ErrorMessage || "计价失败");
            }
          },
          () => {
            button.disabled = false;
            button.style.background = "#107fff";
          },
          false
        );
      } catch (error) {
        $.IShowError(error.message);
        button.disabled = false;
        button.style.background = "#107fff";
      }

      return false;
    }
  },

  // 提交前事件
  BeforeSubmit: function (action, postValue) {},

  // 提交后事件
  AfterSubmit: function (action, responseValue) {}
});
