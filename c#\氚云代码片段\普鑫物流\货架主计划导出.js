/* 控件接口说明：
 * 1. 读取控件: this.***,*号输入控件编码;
 * 2. 读取控件的值： this.***.GetValue();
 * 3. 设置控件的值： this.***.SetValue(???);
 * 4. 绑定控件值变化事件： this.***.Bind<PERSON><PERSON><PERSON>(key,function(){})，key是定义唯一的方法名;
 * 5. 解除控件值变化事件： this.***.UnbindChange(key);
 * 6. CheckboxList、DropDownList、RadioButtonList: $.***.AddItem(value,text),$.***.ClearItems();
 */
/* 公共接口：
 * 1. ajax：$.SmartForm.PostForm(actionName,data,callBack,errorBack,async),
 *          actionName:提交的ActionName;data:提交后台的数据;callback:回调函数;errorBack:错误回调函数;async:是否异步;
 * 2. 打开表单：$.IShowForm(schemaCode, objectId, checkIsChange)，
 *          schemaCode:表单编码;objectId;表单数据Id;checkIsChange:关闭时，是否感知变化;
 * 3. 定位接口：$.ILocation();
 */

!( function( window ) {
    function DocumentApi() {
      this.doc = document
    }
    DocumentApi.prototype.findSelector = function( selector, element ) {
      if( !selector ) return
      if( !element ) element = this.doc.body
      if( element.className && ( element.className + "" ).indexOf( selector ) >= 0 )
        return element
      if( element.id == selector ) return element

      let children = element.children

      if( children && children.length > 0 ) {
        for( let child of children ) {
          let e = this.findSelector( selector, child )
          if( e ) return e
        }
      }
    }

    DocumentApi.prototype.insertHeadStyle = function( css, className = "" ) {
      if( this.findSelector( className, this.doc.head ) ) return
      let headFrag = this.doc.createDocumentFragment()
      headFrag.appendChild( this.createCSSNode( css, className ) )
      this.doc.head.appendChild( headFrag )
    }
    DocumentApi.prototype.insertScript = function( src ) {
      let script = this.doc.createElement( "script" )
      script.type = "text/javascript"
      script.src = src
      this.doc.body.appendChild( script )
    }

    DocumentApi.prototype.createCSSNode = function( css, className = "", initType = "text/css" ) {
      let cssNode = this.doc.createElement( "style" )
      if( className ) {
        cssNode.className = className
        const xclass = "." + className.split( " " ).join( "." )
        cssNode.dataset.xclass = xclass
      }

      cssNode.setAttribute( "type", initType )
      cssNode.appendChild( this.doc.createTextNode( css ) )
      return cssNode
    }

    DocumentApi.prototype.createElement = function( content ) {
      let node = this.doc.createElement( "div" )
      node.innerHTML = content
      return node.children[ 0 ]
    }

    DocumentApi.prototype.download = function( url, saveName ) {
      if( typeof url == 'object' && url instanceof Blob ) {
        url = URL.createObjectURL( url ) // 创建blob地址
      }
      if( !url ) return
      var aLink = this.createElement( '<a></a>' )
      aLink.href = url
      aLink.download = saveName || '' // HTML5新增的属性，指定保存文件名，可以不要后缀，注意，file:///模式下不会生效
      var event
      if( window.MouseEvent ) event = new MouseEvent( 'click' )
      else {
        event = document.createEvent( 'MouseEvents' )
        event.initMouseEvent( 'click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null )
      }
      console.log( 'download', saveName )
      aLink.dispatchEvent( event )
    }
    window.docApi = new DocumentApi()
    docApi.insertHeadStyle( ".code-check-warn {display:none} ", "code-check" )
  })( window )

  !( function( window ) {
    function ExcelJSApi() {
      if( !window[ "ExcelJS" ] ) {
        docApi.insertScript(
          "https://www.h3yun.com/Form/DoPreview/?AttachmentID=9d8b3475-35c9-4a45-9a86-2251513b68f5"
        )
      }
    }
    ExcelJSApi.prototype.loadTemplate = async function( template ) {
      let data = await this.getTemplate( template )
      let excel = await this.getExcel()
      let wb = new excel.Workbook()
      await wb.xlsx.load( data )
      this.workbook = wb
      return wb
    }

    ExcelJSApi.prototype.getTemplate = function( template ) {
      return new Promise(( resolve, reject ) => {
        window.$.ajax( {
          type: "POST",
          url: "/App/OnAction/",
          data: { PostData: JSON.stringify( { Command: "Download", ActionName: "DoAction", QueryCode: "D2826050ff2122070c84a41be2f42c1ac623b4a", ObjectId: template }) },
          dataType: "json",
          async: false,
          success: function( resp ) {
            if( resp && resp.Successful ) {
              let data = resp ?.ReturnData ?.Response ?.ReturnData
                          if ( !data ) return resolve()
              window.axios.get( data[ "Url" ], { responseType: "blob" })
                .then(( resp ) => resolve( resp ) )
                .catch(( e ) => resolve() )
            } else {
              resolve()
            }
          }
        })
      })
    }
    ExcelJSApi.prototype.getExcel = function() {
      let count = 1
      function wait( resolve ) {
        if( window[ 'ExcelJS' ] ) resolve( window[ 'ExcelJS' ] )
        else {
          if( count > 50 ) resolve()
          else {
            count++
            setTimeout(() => {
              wait( resolve )
            }, 100 )
          }
        }
      }
      return new Promise(( resolve ) => {
        wait( resolve )
      })
    }
    ExcelJSApi.prototype.writeExcel = async function( filename ) {
      let wbout = await this.workbook.xlsx.writeBuffer()
      let blob = new Blob( [ wbout ], {
        type: "application/octet-stream"
      })

      docApi.download( blob, filename )
    }
    ExcelJSApi.prototype.removeRows = async function( sheet, from = 1, to ) {
      if( !to ) to = sheet.rowCount
      for( let i = from;i <= to;i++ ) {
        sheet.spliceRows( from, 1 )
      }
    }
    window.excelJSApi = new ExcelJSApi()
  })( window )

  // 表单插件代码
  $.extend( $.JForm, {
    // 加载事件
    OnLoad: function() {
    },

    // 按钮事件
    OnLoadActions: function( actions ) {

    },

    // 提交校验
    OnValidate: function( actionControl ) {
      if( actionControl.Action == 'Submit' ) {
        let _this = this

        setTimeout( async () => {
          let OrderNo = _this.OrderNo.GetValue()
          let ProcessStatus = _this.ProcessStatus.GetValue()
          let {Successful, ErrorMessage, ReturnData} = await Post( 'export_plan', { OrderNo, ProcessStatus })


          if( Successful ) {
            let error = ReturnData ?.Error
                      if ( error ) {
              $.IShowError( error )
            } else {
              await GenerateTemplate( _this, JSON.parse( ReturnData.Data ) )
              $.IShowSuccess( "导出报工单完成" )
              setTimeout(() => {
                _this.ClosePage()
              }, 1000 )
            }
          } else {
            $.IShowError( ErrorMessage || "导出报工单失败" )
          }
        })

        return false
      }
    },

    // 提交前事件
    BeforeSubmit: function( action, postValue ) {

    },

    // 提交后事件
    AfterSubmit: function( action, responseValue ) {
    }
  })

  async function Post( name, body ) {
    let ListView = H3PluginDeveloper.IGetParams( "ListView" )
    let button = docApi.findSelector( 'submit-button' )
    button.disabled = true
    button.style.background = '#777'
    return new Promise(( resolve ) => {
      ListView.Post( name, { ObjectId: body }, async ( resp ) => {
        button.disabled = false
        button.style.background = '#107fff'
        resolve( resp )

      }, ( error ) => {
        button.disabled = false
        button.style.background = '#107fff'
        console.log( error )
        resolve( { Successful: false, ErrorMessage: '请求失败' })
      }, false )

    })

  }


  function resetExcelHead( sheet ) {
    let row4 = sheet.getRow( 2 )
    let row5 = sheet.getRow( 3 )
    let merges = sheet._merges
    let head = {}
    for( let i = 1;i <= row4.cellCount;i++ ) {
      let cell4 = row4.getCell( i )

      let merge = merges[ cell4.address ]
      if( merge ) {
        if( merge.model.left != merge.model.right ) {
          for( let j = merge.model.left;j <= merge.model.right;j++ ) {
            let cell5 = row5.getCell( j )
            let key = ( cell4.value || '' ).trim().substring(1) + "-" + ( cell5.value || '' ).trim()
            if( key ) head[ key ] = j - 1
          }
          i = i + merge.model.right - merge.model.left
        } else {
          let cell5 = row5.getCell( i )
          if( cell5.value ) {
            let key = cell5.value.replace( /\s{1,}/g, '' )
            if( key ) head[ key ] = i - 1
          }
        }

        continue
      }
      let cell5 = row5.getCell( i )
      if( cell5.value ) {
        let key = cell5.value.replace( /\s{1,}/g, '' )
        if( key ) head[ key ] = i - 1
      }
    }
    console.log( "head", head )
    return head
  }



  async function GenerateTemplate( form, data ) {
    let wb = await excelJSApi.loadTemplate( "ShelfPlanTemplate" )
    let sheet = wb.getWorksheet( "主计划" )

    let head = resetExcelHead( sheet )

    excelJSApi.removeRows( sheet, 4 )

    let index = 1
    for( let item of data ) {
      let row = new Array( sheet.getRow( 3 ).cellCount )
      row[ 0 ] = index++
      row[ head[ `客户名称` ] ] = item[ 'Customer' ]
      row[ head[ `项目名称` ] ] = item[ 'ProjectName' ]
      row[ head[ `订单号` ] ] = item[ 'OrderNo' ]
      row[ head[ `产品名称` ] ] = item[ 'MaterialName' ]
      row[ head[ `BOM序号` ]] = item['SerialNo']
      row[ head[ `型号` ] ] = item[ 'MaterialModel' ]
      row[ head[ `长(mm)` ] ] = item[ 'Length' ]
      row[ head[ `宽(mm)` ] ] = item[ 'Width' ]
      row[ head[ `高/厚(mm)` ] ] = item[ 'Height' ]?.toFixed( 1 )
      row[ head[ `图号` ]] = item['DrawSuffix']
      row[ head[ `数量（件）` ] ] = item[ 'Count' ]
      row[ head[ `单重(KG)` ] ] = item[ 'Weight' ]?.toFixed( 1 )
      row[ head[ `总重(T)` ] ] = ( (item[ 'SumWeight' ] || 0) / 1000 ).toFixed( 3 )
      row[ head[ '喷塑颜' ] ] = item[ 'SprayColor' ]
      row[ head[ '接单日期' ] ] = item[ 'ReceiptDate' ] ? new Date(item[ 'ReceiptDate' ]):''
      row[ head[ '订单交期' ] ] = item[ 'DeliveryDate' ]?new Date(item['DeliveryDate']):''
      row[ head[ '已发数量' ] ] = item[ 'QuantitySent' ]
      row[ head[ '发货日期' ] ] = item[ 'SentDate' ]?new Date(item['SentDate']):''
      row[ head[ '准交' ] ] = item[ 'QuasiDelivery' ]
      row[ head[ '总状态' ] ] = item[ 'ProcessStatus' ]
      row[ head[ '状态信息' ] ] = item[ 'ProcessStatusInfo' ]
      row[ head[ '生产工艺路线' ] ] = item[ 'ProcessFlow' ]
      let children = item['D282605F80a2dc5e16c74b21813e7f1b5ad3615b']
      console.log('---child')
      if(!children || children.length == 0) continue

      for(let child of children) {
        let process = child['ProcessName']
        row[head[`${process}-计划`]] = child['PlanDate'] ? new Date(child["PlanDate"]):''
        row[head[`${process}-完成数`]] = child['Finished']
        row[head[`${process}-尾数`]] = child['Remain']
        row[head[`${process}-实际`]] = child['ActualDate'] ? new Date(child['ActualDate']):''
      }

      sheet.addRow( row )
    }
    let to = sheet.rowCount
    for( let i = 4;i <= to;i++ ) {
        let row = sheet.getRow(i)
        let quasiCell = row.getCell(head['准交'] + 1)
        let quasiDelivery = quasiCell.value
        if(quasiDelivery == '按期完成') quasiCell.fill = {type: 'pattern',pattern:'solid',fgColor: {argb: '52C97A'}}
        if(quasiDelivery == '超期完成') quasiCell.fill = {type: 'pattern',pattern:'solid',fgColor: {argb: 'FDFC45'}}
        if(quasiDelivery == '超期未完') quasiCell.fill = {type: 'pattern',pattern:'solid',fgColor: {argb: 'FF4D4F'}}
        if(quasiDelivery == '交期未到') quasiCell.fill = {type: 'pattern',pattern:'solid',fgColor: {argb: 'B5F5EC'}}
        if(quasiDelivery == '子件物料') quasiCell.fill = {type: 'pattern',pattern:'solid',fgColor: {argb: 'E4EAC3'}}

        let statusCell = row.getCell(head['总状态'] + 1)
        let processStatus = statusCell.value
        if(processStatus == '在制中') statusCell.fill = {type: 'pattern',pattern:'solid',fgColor: {argb: 'e8e8e8'}}
        if(processStatus == '生产结案') statusCell.fill = {type: 'pattern',pattern:'solid',fgColor: {argb: 'fbce00'}}
        if(processStatus == '发货结案') statusCell.fill = {type: 'pattern',pattern:'solid',fgColor: {argb: '73d13d'}}
    }

    await excelJSApi.writeExcel( "货架主计划.xlsx" )
  }