version: '3.7'
services:
    redis:
      image: redis:alpine
      ports:
        - 6379:6379
      logging:
        driver: "json-file"
        options:
          max-size: "5M"
          max-file: "2"
      volumes:
        - /root/docker/redis:/data
      command: redis-server --requirepass zGHgHsm*X988 --bind 0.0.0.0

    ui:
      image: rediscommander/redis-commander
      ports:
        - 16379:8081
      environment:
         - HTTP_USER=admin
         - HTTP_PASSWORD=123456
         - REDIS_HOST=redis
         - REDIS_PASSWORD=12345678
      logging:
        driver: "json-file"
        options:
          max-size: "5M"
          max-file: "2"
