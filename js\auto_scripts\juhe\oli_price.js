import req  from '../util/request.js'

export default class <PERSON><PERSON>_<PERSON>li {
  constructor() {
    this.key = "d18f0e7f21479ef9ad1cea4ad47d3222";
    this.url = `http://apis.juhe.cn/gnyj/query?key=${this.key}`;
  }

  async whole_price() {
    let items = await this.price();
    return this.text(items);
  }

  async price() {
    return this.req(this.url);
  }

  async req(url) {
    let body =  await req.get(url)
    return body["error_code"] === 0?body['result']:undefined;
  }

  text(items) {
    let text = "## **今日油价** \n";
    if (items) {
      let item = items.find((e) => e["city"] === "四川");
      if (item) {
        text += ` - 92号：${item["92h"]} 元\n`;
        text += ` - 95号：${item["95h"]} 元\n`;
        text += ` - 98号：${item["98h"]} 元\n`;
      }
    }
    return text;
  }
}
