/*
* $.ListView.GetSelected()获取选中的记录
* $.ListView.RefreshView()刷新列表
* $.ListView.Post()请求后台
* $.ListView.InitQueryItems()修改过滤条件
* $.ListView.RefreshView()刷新页面
* $.ListView.ActionPreDo() 按钮执行之前的事件
*/

; ( function( window ) {
    function CSS_Table() {
        this.mutations = {}
        wait(() => {
            return document.querySelector( "#gridview .tg-pane-bodyer .tg-canvas-top-left" )
        }).then( target_node => {
            // 当观察到突变时执行的回调函数
            let _this = this
            exec(_this)
            let callback = function( mutationsList ) {
                if( _this.IsEmpty( _this.mutations ) ) return
                mutationsList.forEach( async function( item, index ) {
                    exec(_this,async () => true)
                });
            };
            try {

                // 创建一个链接到回调函数的观察者实例
                let observer = new MutationObserver( callback );
                // 开始观察已配置突变的目标节点
                let config = { subtree: true, childList: true, attributes: true, attributeFilter: [ 'style' ] };
                observer.observe( target_node, config );
            } catch( e ) {
                console.log( e )
            }

        })
        async function exec(_this, callback) {
            for( let key of Object.keys( _this.mutations ) ) {
                try {
                    //let e = await wait(() => target.querySelector( `.tg-row` ) )
                    if(callback && typeof callback === 'function') {
                        let result = await callback()
                        if(!result) continue
                    }
                    let mutation = _this.mutations[ key ]
                    let throttle = mutation[ 0 ]
                    throttle( mutation[ 1 ], mutation[ 2 ] )
                } catch( e ) {
                    console.log( e )
                }
            }
        }
    }


    CSS_Table.prototype.row = function(callback, options ) {
        let headers = $( '.tg-header-item .tg-column-header' ).toArray()
        let fields = headers.map(e=> ({name:$(e).attr('data'), index:$(e).attr('index')}))
        fields = fields.filter(e => !['tg_checkbox_column', 'tg_blank_column'].includes(e.name))
        let rows = $('.tg-pane-top-left .tg-canvas .tg-row').toArray()

        let result = [], controller ={}
        for(let row of rows) {
            let row_val = {}, i = $(row).attr('index')
            controller[i] = [$(row)]
            for(let field of fields) {
                let cell = $(row).find(`.tg-cell[index="${field.index}"]`)
                if(cell && cell.length > 0) {
                   let text= cell.text().trim()
                   if(text === '--') text = ''
                   row_val[field.name]  = text
                } else {
                  cell =  $( `.tg-pane-top-right .tg-canvas .tg-row[index="${i}"] .tg-cell[index="${ field.index }"` )
                  if(cell && cell.length > 0) {
                    let text= cell.text().trim()
                    if(text === '--') text = ''
                    row_val[field.name]  = text
                    if(controller[i].length <=1) {
                        controller[i].push(cell.parent('.tg-row'))
                    }
                  }
                }
            }
            result.push(row_val)
        }
        for(let data of result) {
            if(callback(data) ) {
                let controls = controller[data.index]

                for(let $control of controls) {
                    if(options && options.css) {
                        $control.css(options.css)
                    }
                }
            }
        }

        if( !this.mutations[ 'row' ] ) {
            let _throttle = this.Throttle( this.row, 500 )
            this.mutations[ 'row' ] = [ _throttle, callback, options ]
        }
    }
    CSS_Table.prototype.Throttle = function( fn, delay ) {
        let timer, last = 0
        let _this = this
        return function() {
            if( !timer ) {
                fn.apply( _this, arguments )
            }
            if( timer ) {

                timer = setTimeout(() => {
                    timer = null
                }, delay )
            }
        }
    }

    CSS_Table.prototype.IsEmpty = function( obj ) {
        if( !obj ) return true
        return Object.keys( obj ).length === 0
    }
    function wait( callBack ) {
        let count = 0
        function doResolve( resolve ) {
            let item = callBack()
            if( item ) {
                return resolve( item )
            } else {
                if( count >= 10 ) {
                    resolve( item )
                } else {
                    count += 1
                    setTimeout(() => doResolve( resolve ), 500 );
                }
            }
        }
        return new Promise( doResolve )
    }


    window.CSS_Table = CSS_Table

})( window );
; ( function() {

    let css_table = new CSS_Table()
    css_table.row( ( val ) => {
        let sub= Number(val.F0000006||0) - Number(val.F0000014||0)
        return sub < 0
    }, {css:{ 'background-color': '#ffccc7' }})
})();




