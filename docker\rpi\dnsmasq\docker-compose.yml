version: '3'
services:
    dnsmasq:
        image: yiyetianxiang/dnsmasq
        container_name: dnsmasq
        ports:
            - "53:53/tcp"
            - "53:53/udp"
        cap_add:
            - NET_ADMIN
        volumes:
            - /home/<USER>/docker/dnsmasq/dnsmasq.d:/etc/dnsmasq.d
            - /home/<USER>/docker/dnsmasq/dnsmasq.conf:/etc/dnsmasq.conf
            - /home/<USER>/docker/dnsmasq/resolv.conf:/etc/resolv.conf
            - /home/<USER>/docker/dnsmasq/hosts:/etc/hosts
        restart: always        
