
using System;
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;
using H3;
using H3.DataModel;
using System.Data;
public class D28260598e613342c0d4ca9ad13fc1e15a43824_ListViewController: H3.SmartForm.ListViewController
{
    private string schemaCode = "D28260598e613342c0d4ca9ad13fc1e15a43824";
    private string childSchema = "D282605Fb33056b961f84fb0bdf718683d9a87fb";

    private string planSchema = "D282605Skmj5js0xdpn3oxyqjy56mw0u3";
    public D28260598e613342c0d4ca9ad13fc1e15a43824_ListViewController(H3.SmartForm.ListViewRequest request): base(request)
    {
    }

    protected override void OnLoad(H3.SmartForm.LoadListViewResponse response)
    {
        base.OnLoad(response);
    }

    protected override void OnSubmit(string actionName, H3.SmartForm.ListViewPostValue postValue, H3.SmartForm.SubmitListViewResponse response)
    {
        if(actionName == "clear")
        {
            string sql = string.Format("delete from i_{0} where 1 = 1", this.schemaCode);
            this.Engine.EngineConfig.CreateCommand().ExecuteScalar(sql);
            return;
        }
        if(actionName == "create_plan")
        {

            string[] objs = this.Deserialize<string[]>(this.Request["ObjectIds"]);
            BizObject[] bizObjects = this.QueryOrderByIds(objs);
            if(bizObjects == null || bizObjects.Length == 0)
            {
                response.Errors.Add("没有表箱类数据");
                return;
            }

            this.CreatePlan(bizObjects);
            response.Message = "计划生成完成";
            return;
        }
        base.OnSubmit(actionName, postValue, response);
    }

    private void CreatePlan(BizObject[] orders)
    {
        foreach(BizObject order in orders)
        {
            BizObject[] details = (BizObject[]) order[this.childSchema];
            if(details == null || details.Length == 0)
            {
                continue;
            }
            foreach(BizObject detail in details)
            {

                string materialName = detail["MaterialName"] + string.Empty;
                if(!materialName.Contains("表箱") && !materialName.Contains("分水器"))
                {
                    continue;
                }
                string orderNo = order["OrderNo"] + string.Empty;
                string materialNo = detail["MaterialNo"] + string.Empty;

                if(this.CheckInPlan(orderNo, materialNo))
                {
                    continue;
                }
                BizObjectSchema planSchema = this.Engine.BizObjectManager.GetPublishedSchema(this.planSchema);
                BizObject plan = new H3.DataModel.BizObject(this.Engine, planSchema, this.Request.UserContext.UserId);
                plan["Saler"] = order["Saler"];
                plan["Customer"] = order["Customer"];
                plan["OrderNo"] = orderNo;
                plan["ProductName"] = detail["MaterialName"];
                plan["Material"] = detail["MaterialRequire"];
                string materialModel = detail["MaterialModel"] + string.Empty;
                int count = this.ToInt(detail["Count"]);
                plan["Count"] = count;
                if(!string.IsNullOrEmpty(materialModel))
                {
                    var matches = Regex.Matches(materialModel, @"(\d+)[路|表]");
                    int tableNum = 0;
                    if(matches.Count > 0)
                    {
                        string digit = matches[0].Groups[1] + string.Empty;
                        tableNum = Convert.ToInt32(digit);
                    }
                    plan["TableNum"] = tableNum;
                    plan["TotalTableNum"] = tableNum * count;
                }
                plan["SizeModel"] = materialModel;
                plan["DeliveryDate"] = detail["DeliveryDate"];
                plan["ReceiptDate"] = System.DateTime.Now;
                plan["MaterialNo"] = materialNo;
                plan["Color"] = detail["Color"];

                DPuXinBoxQuasiState.ConfirmState(plan);
                plan.Status = BizObjectStatus.Effective; //设置状态生效
                plan.Create();
            }
        }
    }

    private BizObject[] QueryOrderByIds(string[] objectIds)
    {
        if(objectIds == null || objectIds.Length == 0)
        {
            return null;
        }
        H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();  //根据物资编码查询库存
        H3.Data.Filter.And andMatcher = new H3.Data.Filter.And();    //构造And匹配器
        andMatcher.Add(new H3.Data.Filter.ItemMatcher("ObjectId", H3.Data.ComparisonOperatorType.In, objectIds));
        filter.Matcher = andMatcher;

        BizObjectSchema schema = this.Engine.BizObjectManager.GetPublishedSchema(this.schemaCode);
        BizObject[] result = BizObject.GetList(this.Engine, this.Request.UserContext.UserId,
            schema, GetListScopeType.GlobalAll, filter);
        return result;
    }

    private bool CheckInPlan(string orderNo, string materialNo)
    {
        if(string.IsNullOrEmpty(materialNo) || string.IsNullOrEmpty(orderNo)) return false;
        string sql = String.Format("select OrderNo, MaterialNo from i_{0} where OrderNo = '{1}' and MaterialNo = '{2}'", this.planSchema, orderNo, materialNo);
        DataTable table = this.Engine.Query.QueryTable(sql, null);

        if(table != null && table.Rows.Count > 0)
        {
            return true;
        }
        return false;
    }

    private int ToInt(object obj)
    {
        return Convert.ToInt32(String.IsNullOrEmpty(obj + "") ? "0" + obj : obj);
    }
}

//表箱准交状态
public class DPuXinBoxQuasiState
{
    private static string childSchema = "D282605F1014606df8364cb7aade7ffa7b57f756";
    public static void ConfirmState(BizObject bizObject)
    {
        BizObject[] details = (BizObject[]) bizObject[childSchema];
        BizObject obj = Find(details);
        //总状态设置
        if(obj == null)
        {
            bizObject["ProcessStatus"] = "在制中";
        }
        else
        {
            int finished = ToInt(obj["Finished"]);
            int count = ToInt(bizObject["Count"]);
            if(finished >= count)
            {
                bizObject["ProcessStatus"] = "生产结案";
            }
            else
            {
                bizObject["ProcessStatus"] = "在制中";
            }
            int quantitySent = ToInt(bizObject["QuantitySent"]);
            if(quantitySent >= count)
            {
                bizObject["ProcessStatus"] = "发货结案";
            }
        }
        // 准交状态设置
        string deliveryDateStr = bizObject["DeliveryDate"] + string.Empty;
        if(string.IsNullOrEmpty(deliveryDateStr))
        {
            bizObject["QuasiDelivery"] = "无交期";
        }
        else
        {
            DateTime deliveryDate = Convert.ToDateTime(deliveryDateStr);
            if(obj != null)
            {
                string actualDateStr = obj["ActualDate"] + string.Empty;
                if(!string.IsNullOrEmpty(actualDateStr))
                {
                    DateTime actualDate = Convert.ToDateTime(actualDateStr);
                    bizObject["QuasiDelivery"] = actualDate.CompareTo(deliveryDate) <= 0 ? "达成" : "超期完工";
                    return;
                }
            }
            DateTime now = System.DateTime.Now;
            if(deliveryDate.CompareTo(now) >= 0)
            {
                bizObject["QuasiDelivery"] = "交期未到";
            }
            else
            {
                bizObject["QuasiDelivery"] = "超期未完";
            }
        }
    }

    private static BizObject Find(BizObject[] array)
    {
        if(array == null || array.Length == 0)
        {
            return null;
        }
        foreach(BizObject obj in array)
        {
            if(Convert.ToString(obj["ProcessName"]) == "装配")
            {
                return obj;
            }
        }
        return null;
    }
    private static int ToInt(object obj)
    {
        return Convert.ToInt32(String.IsNullOrEmpty(obj + "") ? "0" + obj : obj);
    }
}