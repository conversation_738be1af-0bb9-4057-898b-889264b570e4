global
    log 127.0.0.1 local0
    maxconn 4096

defaults
    log     global
    mode    tcp
    option  tcplog
    retries 3
    option  redispatch
    maxconn 2000
    timeout connect 5000
    timeout client 50000
    timeout server 50000

listen stats
    bind *:1080 # haproxy容器1080端口显示代理统计页面，映射到宿主51080端口
    mode http
    stats enable
    stats hide-version
    stats realm Haproxy\ Statistics
    stats uri /
    stats auth admin:admin

listen rabbitmq
    bind *:5672 # haproxy容器5672端口代理多个rabbitmq服务，映射到宿主56729端口
    mode tcp
    balance roundrobin
    timeout client 1h
    timeout server 1h
    option  clitcpka
    # server  rmqha_node0 rmqha_node0:5672  check inter 5s rise 2 fall 3
    server  rmqha_node1 rmqha_node1:5672  check inter 5s rise 2 fall 3
    server  rmqha_node2 rmqha_node2:5672  check inter 5s rise 2 fall 3