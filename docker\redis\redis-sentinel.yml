version: '3.7'
services:
    sentinel1:
      image: redis:alpine
      volumes:
        - redis_conf:/tmp
      ports:
        - 26379:26379
      command: 
        - "redis-sentinel"
        - "/tmp/sentinel1.conf"
    sentinel2:
      image: redis:alpine
      volumes:
        - redis_conf:/tmp
      ports:
        - 26380:26380
      command: 
        - "redis-sentinel"
        - "/tmp/sentinel2.conf"
    sentinel3:
      image: redis:alpine
      volumes:
        - redis_conf:/tmp
      ports:
        - 26381:26381
      command: 
        - "redis-sentinel"
        - "/tmp/sentinel3.conf"
volumes:
  redis_conf:
    driver_opts:
      device: "${REDIS_CONF_PATH:-:/opt/nfs_data/redis}"
      o: "addr=192.168.1.200,vers=4,rw,noatime,rsize=8192,wsize=8192,tcp,timeo=14"
      type: "nfs"
