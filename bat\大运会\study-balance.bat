cd C:/Users/<USER>/IdeaProjects/study_service

git checkout dev
git pull

call  mvn -B -U -DskipTests clean compile package

scp ./target/*.jar root@*************:/root/app/study_app

echo "restart"

set balance_addr=root@**************

set deploy_cmd=cd /root/app;^
cp ./study_app/*.jar ./study_pc;^
cd ./study_app;^
sh ./restart.sh;^
cd ../study_pc;^
sh ./restart.sh;^
echo \"restart app and pc\";

set cp_cmd=scp ./*.jar %balance_addr%:/root/app/study_app;echo \"copy jar to balance addr\";
set exit_cmd=exit;

set balance_cmd=ssh %balance_addr% \"%deploy_cmd%%exit_cmd%\";

@rem use ssh exec cmd
@rem deploy local server
@rem cp jar to balance server
@rem deploy balance server
@rem exit
ssh root@************* "%deploy_cmd%%cp_cmd%%balance_cmd%%exit_cmd%"
echo "deploy finish"
pause