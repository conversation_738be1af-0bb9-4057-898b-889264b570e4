import req  from '../util/request.js'
import date from '../util/date.js'

let url = 'http://kod.r.to'
    ; (async () => {
        let csrf = await getCsrf()
        let info = await login(csrf)
        let result = await doTask(info, csrf)
        console.log(date.format(new Date(), '%Y-%m-%d %H:%M:%S'), JSON.stringify(result));
    })();

async function getCsrf() {

    let resp = await req.get(`${url}`)
    let cookies = resp.headers['set-cookie']
    let cookie = delRepeat(cookies.join(';'))

    let reg = /CSRF_TOKEN=(.*?);/mg
    let match = reg.exec(cookie)
    let csrf_token = match[1]
    return { cookie, csrf_token }
}


async function login(csrf) {
    let resp = await req.post(`${url}/?user/index/loginSubmit`, {
        form: {
            'name': 'admin',
            'password': 'iBDC08325JXCH64jHAeLcVTGfo8K3wz8OglELuBVzjZW5rGetUhv-cHy8gA',
            'rememberPassword': '0',
            'salt': '1',
            'CSRF_TOKEN':  csrf.csrf_token
        },
        headers: {
            cookie: csrf.cookie
        }
    }, {_auto:false})
    let cookies = resp.headers['set-cookie']
    let cookie = delRepeat(cookies.join(';'))
    return {cookie}
}


async function doTask(info, csrf) {
    let resp = await req.post(`${url}/?admin/autoTask/run`, {
        form: {
            id: 5,
            CSRF_TOKEN: csrf.csrf_token,
        },
        headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.0.0 Safari/537.36',
            'cookie':`${info.cookie}CSRF_TOKEN=${csrf.csrf_token}`
        }
    })
    return resp
}

function delRepeat(cookie) {
    let cookies = cookie.split(';')
    let map = {}
    for (const _cookie of cookies) {
        let items = _cookie.split('=')
        if(!(items[0] in map)) {
            map[items[0]] = _cookie
        }
    }
    return Object.values(map).join(';') + ';'
}