import Notify from './util/notify.js'
import C<PERSON><PERSON><PERSON> from './caiyun/index.js'
import Juhe_Weather from './juhe/weather.js'


const notify = new Notify();

weather();
async function weather() {
  const caiyun = new CaiYun();
  let text = await caiyun.weather();
  notify.sendMarkdown("彩云天气预报", text);

  const juhe_Weather = new Juhe_Weather()
  text = await juhe_Weather.whole_weather()

  notify.sendMarkdown("聚合天气预报", text);
}