[common]
server_addr = frps.yourway.top
server_port = 5050
privilege_token = yousee

[ye_ssh]
type = tcp
local_ip = 127.0.0.1
local_port = 22
remote_port = 58451
use_encryption = true
use_compression = true

[ye2_ssh]
type = tcp
local_ip = 127.0.0.1
local_port = 6800
remote_port = 58452
use_encryption = true
use_compression = true

[ye3_ssh]
type = tcp
local_ip = 127.0.0.1
local_port = 58453 
remote_port = 58453
use_encryption = true
use_compression = true

[ye_web_git]
type = http
local_port = 3000
custom_domains = git.frps.yourway.top
use_encryption = true
use_compression = true

[ye_web_exploer]
type = http
local_port = 8080
custom_domains = yyl.frps.yourway.top
use_encryption = true
use_compression = true

[ye_web_verysync]
type = http
local_port = 8886
custom_domains = vs.frps.yourway.top
use_encryption = true
use_compression = true

[ye_web_pihole]
type = http
local_port = 80081
custom_domains = poihole.frps.yourway.top
use_encryption = true
use_compression = true