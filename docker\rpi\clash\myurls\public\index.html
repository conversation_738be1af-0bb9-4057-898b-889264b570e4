<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>MyUrls</title>

  <link rel="stylesheet" href="https://unpkg.com/element-ui@2.13.0/lib/theme-chalk/index.css">
  <script src="https://unpkg.com/vue@2.6.11/dist/vue.min.js"></script>
  <script src="https://unpkg.com/axios@0.19.2/dist/axios.min.js"></script>
  <script src="https://unpkg.com/element-ui@2.13.0/lib/index.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/vue-clipboard2@0.3.1/dist/vue-clipboard.min.js"></script>
</head>

<body>
  <div id="app">
    <el-container>
      <el-header></el-header>
      <el-main>
        <div :class="[isPc ? 'body-center body-width-pc' : 'body-center body-width-mb']">
          <img width="300" src="https://cdn.jsdelivr.net/gh/CareyWang/MyUrls@master/public/logo.png" @click="goToGayHub">
          <el-input ref="long" v-model="longUrl" size="medium" @keyup.enter.native="enterToDoShort">
            <el-button slot="append" icon="el-icon-magic-stick" @click="doShort" :loading="loading"></el-button>
          </el-input>
          <el-input ref="shortUrl" @dblclick.native="changeDisableStatus" class="copy-content" v-model="shortUrl" size="medium">
            <el-button slot="append" v-clipboard:copy="shortUrl" v-clipboard:success="onCopy" ref="copy-btn"
              icon="el-icon-document-copy"></el-button>
          </el-input>
        </div>
      </el-main>
    </el-container>
  </div>

  <script>
    const repo = 'https://github.com/CareyWang/MyUrls'
    const backend = 'http://example.com'

    let app = new Vue({
      el: "#app",
      data() {
        return {
          isPc: true,

          loading: false,
          longUrl: '',
          shortUrl: ''
        }
      },
      created() {
        const os = this.getOS()
        if (os.isPc !== true) {
          this.isPc = false
        }
      },
      mounted() {
        this.$refs.long.focus()
      },
      methods: {
        enterToDoShort(ev) {
          ev.keyCode === 13 && this.doShort()
        },
        doShort() {
          let re = new RegExp('http(s*)://[^\s]*')
          if (re.exec(this.longUrl) === null) {
            this.$message.warning('请输入正确格式的长链接')
            return
          }

          this.loading = true

          let data = new FormData();
          data.append("longUrl", btoa(this.longUrl));
          data.append("shortKey", this.shortUrl.indexOf('http') < 0 ? this.shortUrl : '');
          axios.post(backend + '/short', data, {
            header: {
              "Content-Type": "application/form-data; charset=utf-8"
            }
          })
            .then(res => {
              if (res.data.Code === 1 && res.data.ShortUrl !== "") {
                this.shortUrl = res.data.ShortUrl;
                this.$copyText(this.shortUrl)
                this.$refs.shortUrl.disabled = true
                this.$message.success("短链接已复制到剪贴板");
              } else {
                this.$message.error("短链接获取失败：" + res.data.Message);
              }
            })
            .catch(() => {
              this.$message.error("短链接获取失败");
            })
            .finally(() => {
              this.loading = false;
            });
        },
        goToGayHub() {
          window.open(repo)
        },
        getOS() {
          let ua = navigator.userAgent,
            isWindowsPhone = /(?:Windows Phone)/.test(ua),
            isSymbian = /(?:SymbianOS)/.test(ua) || isWindowsPhone,
            isAndroid = /(?:Android)/.test(ua),
            isFireFox = /(?:Firefox)/.test(ua),
            isChrome = /(?:Chrome|CriOS)/.test(ua),
            isTablet = /(?:iPad|PlayBook)/.test(ua) || (isAndroid && !/(?:Mobile)/.test(ua)) || (isFireFox && /(?:Tablet)/.test(ua)),
            isPhone = /(?:iPhone)/.test(ua) && !isTablet,
            isPc = !isPhone && !isAndroid && !isSymbian;

          return {
            isTablet: isTablet,
            isPhone: isPhone,
            isAndroid: isAndroid,
            isPc: isPc
          };
        },
        getBodyClass() {
          return this.isPc ? 'body-center body-width-pc' : 'body-center'
        },
        onCopy() {
          this.$message.success("Copied!");
        },
        changeDisableStatus(event) {
          this.$refs.shortUrl.disabled = false
        }
      },
    })
  </script>

  <style>
    .body-center {
      width: 40%;
      position: absolute;
      left: 50%;
      top: 30%;
      transform: translate(-50%, -50%);
      text-align: center;
    }

    .body-width-pc {
      width: 40%;
    }

    .body-width-mb {
      width: 90%;
    }

    .el-input {
      margin-top: 20px;
    }
  </style>
</body>

</html>