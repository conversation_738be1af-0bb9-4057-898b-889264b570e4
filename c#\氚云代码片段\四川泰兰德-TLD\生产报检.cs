
using System;
using System.Collections.Generic;
using System.Text;
using H3;
using System.Text.RegularExpressions;
public class D149913d59edb3243294d16a9a50a092204cb88 : H3.SmartForm.SmartFormController
{
    public D149913d59edb3243294d16a9a50a092204cb88(H3.SmartForm.SmartFormRequest request) : base(request)
    {
    }

    protected override void OnLoad(H3.SmartForm.LoadSmartFormResponse response)
    {
        base.OnLoad(response);
    }

    protected override void OnSubmit(string actionName, H3.SmartForm.SmartFormPostValue postValue, H3.SmartForm.SubmitSmartFormResponse response)
    {
        if (actionName == "Submit")
        {
            H3.DataModel.BizObject bo = this.Request.BizObject;
            //报检明细
            H3.DataModel.BizObject[] details = (H3.DataModel.BizObject[])bo["D149913Fc24b018dd68b4476b7b9aaf2d7eb4955"];
            if (details != null && details.Length > 0)
            {
                // 订单类型
                string orderType = bo["F0000016"] + string.Empty;
                string seqNo = "";
                if (orderType == "销售")
                {
                    //销售订单
                    string saleOrderId = bo["F0000003"] + string.Empty;
                    if (!string.IsNullOrWhiteSpace(saleOrderId))
                    {
                        //获取销售订单
                        H3.DataModel.BizObject saleOrder = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId,
                               this.Engine, "D149913Sno9vwxo1ym57slwtoxwojn4h5", saleOrderId, false);
                        if (saleOrder != null)
                        {
                            seqNo = saleOrder["SeqNo"] + string.Empty;//销售订单流水号
                        }
                    }
                }
                if (orderType == "囤货")
                {
                    //囤货订单
                    string stockOrderId = bo["F0000018"] + string.Empty;
                    if (!string.IsNullOrWhiteSpace(stockOrderId))
                    {
                        //获取囤货单
                        H3.DataModel.BizObject stockOrder = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId,
                                this.Engine, "D149913Srvarz01bsmaeiekjb93knjpb3", stockOrderId, false);
                        if (stockOrder != null)
                        {
                            seqNo = stockOrder["SeqNo"] + string.Empty;//囤货流水号
                        }
                    }
                }

                if (!string.IsNullOrWhiteSpace(seqNo))
                {
                    foreach (H3.DataModel.BizObject detail in details)
                    {
                        string isSn = detail["F0000011"] + string.Empty;
                        if (isSn != "仅出库启用")
                        {
                            continue;
                        }
                        string sn = detail["********"] + string.Empty; //sn码

                        if (!string.IsNullOrWhiteSpace(sn) && sn != "系统自动生成")
                        {
                            continue;
                        }
                        string productId = detail["F0000005"] + string.Empty;//产品信息
                        // 从产品信息中查询该 出厂类别
                        string sqlProduct = "select F0000016 from i_D149913de32da0b7b9342ae9ab01d801d2897f8 where ObjectId='"+productId+"'";
                        System.Data.DataTable productTable = this.Request.Engine.Query.QueryTable(sqlProduct, null);

                        if (productTable == null || productTable.Rows.Count == 0)
                        {
                            continue;
                        }
                        //出厂类别
                        string product = Convert.ToString(productTable.Rows[0]["F0000016"]);
                        if (string.IsNullOrWhiteSpace(product))
                        {
                            continue;
                        }
                        //查询sn累计表是否有该产品类型
                        string sqlpo = "select code,num from i_D14991350d8e76bf0384f78a821042759dffb35 where product = '"
                         + product + "'";
                        System.Data.DataTable snTable = this.Request.Engine.Query.QueryTable(sqlpo, null);

                        if (snTable == null || snTable.Rows.Count == 0)
                        {
                            continue;
                        }
                        string code = Convert.ToString(snTable.Rows[0]["code"]);
                        int num = Convert.ToInt32(snTable.Rows[0]["num"]);
                        // 更新
                        string sql = "update i_D14991350d8e76bf0384f78a821042759dffb35 set num=num+1 where product = '"
                         + product + "'";
                        this.Engine.EngineConfig.CreateCommand().ExecuteScalar(sql);

                        sn = this.generateSn(code, num, seqNo);
                        detail["********"] = sn;
                        detail.Update();
                    }
                }
            }
        }
        base.OnSubmit(actionName, postValue, response);
    }

    private string generateSn(string code, int num, string seqNo)
    {
        int first = num / 1000;
        int last = num % 1000;

        string sn = "-T" + code;
        sn += this.NumToChar(first) + (last.ToString().PadLeft(3, '0'));
        string seq = Regex.Replace(seqNo, @"[^\d]*", "");

        sn += seq.Substring(2);
        return sn;
    }

    private string NumToChar(int number)
    {
        if (10 <= number && 35 >= number)
        {
            int num = number + 55;
            System.Text.ASCIIEncoding asciiEncoding = new System.Text.ASCIIEncoding();
            byte[] btNumber = new byte[] { (byte)num };
            return asciiEncoding.GetString(btNumber);
        }
        return number.ToString();
    }
}