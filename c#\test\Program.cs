﻿// See https://aka.ms/new-console-template for more information
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;

using System;
using System.Data;

class Program
{
    static void Main(string[] args)
    {
        decimal t = (decimal)(25*10.5);

        Console.WriteLine(t/(11m));
    }

    private static async Task func1()
    {
        Console.WriteLine("Func1 proccess - start");
        await Task.Run(() => Thread.Sleep(1000));
        Console.WriteLine("Func1 proccess - end");
    }

    private static async Task func2()
    {
        Console.WriteLine("Func2 proccess - start");
        await Task.Run(() => Thread.Sleep(3000));
        Console.WriteLine("Func2 proccess - end");
    }

    private static async Task func3()
    {
        Console.WriteLine("Func3 proccess - start");
        await Task.Run(() => Thread.Sleep(5000));
        Console.WriteLine("Func3 proccess - end");
    }
}