/**
 * AccountPanel组件样式
 * 从SCSS转换为可注入的CSS字符串
 */

export const accountPanelStyles = `
/* CSS自定义属性定义 */
:host {
  /* 基础颜色调色板 */
  --ac-primary-400: #38bdf8;
  --ac-primary-500: #0ea5e9;
  --ac-primary-600: #0284c7;

  --ac-gray-50: #fafafa;
  --ac-gray-200: #e4e4e7;
  --ac-gray-300: #d4d4d8;
  --ac-gray-400: #a1a1aa;
  --ac-gray-600: #52525b;
  --ac-gray-900: #18181b;

  --ac-success: #22c55e;
  --ac-danger: #ef4444;
  --ac-warning: #f59e0b;
  --ac-info: #3b82f6;

  /* 语义化颜色系统 */
  --ac-bg-primary: #fff;
  --ac-bg-secondary: var(--ac-gray-50);
  --ac-bg-glass: rgba(255, 255, 255, 0.95);
  --ac-bg-overlay: rgba(0, 0, 0, 0.3);

  --ac-text-primary: var(--ac-gray-900);
  --ac-text-secondary: var(--ac-gray-600);
  --ac-text-muted: var(--ac-gray-400);
  --ac-text-inverse: #fff;

  --ac-border-light: var(--ac-gray-200);
  --ac-border-medium: var(--ac-gray-300);

  /* 尺寸系统 */
  --ac-radius-sm: 0.375rem;
  --ac-radius-md: 0.5rem;
  --ac-radius-lg: 0.75rem;
  --ac-radius-xl: 1rem;
  --ac-radius-full: 9999px;

  --ac-space-1: 0.25rem;
  --ac-space-2: 0.5rem;
  --ac-space-3: 0.75rem;
  --ac-space-4: 1rem;
  --ac-space-5: 1.25rem;
  --ac-space-6: 1.5rem;
  --ac-space-8: 2rem;

  /* 动画系统 */
  --ac-duration-fast: 0.15s;
  --ac-duration-normal: 0.2s;
  --ac-duration-slow: 0.3s;
  --ac-ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ac-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ac-ease-spring: cubic-bezier(0.34, 1.56, 0.64, 1);

  /* 阴影系统 */
  --ac-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --ac-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --ac-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --ac-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* 字体系统 */
  --ac-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'SF Pro Display', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --ac-font-mono: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  --ac-font-size-xs: 0.75rem;
  --ac-font-size-sm: 0.875rem;
  --ac-font-size-base: 1rem;
  --ac-font-size-lg: 1.125rem;
  --ac-font-size-xl: 1.25rem;

  /* Z-index系统 */
  --ac-z-floating: 10000;
  --ac-z-modal: 10001;
  --ac-z-modal-detail: 10005;
}

/* 暗色主题支持 */
:host([theme="dark"]) {
  --ac-bg-primary: #1f2937;
  --ac-bg-secondary: #374151;
  --ac-bg-glass: rgba(31, 41, 55, 0.95);
  --ac-bg-overlay: rgba(0, 0, 0, 0.5);

  --ac-text-primary: #f9fafb;
  --ac-text-secondary: #d1d5db;
  --ac-text-muted: #9ca3af;

  --ac-border-light: #4b5563;
  --ac-border-medium: #6b7280;
}

/* 表单系统 */
.account-form {
  font-family: var(--ac-font-family);
  font-size: var(--ac-font-size-sm);
  color: var(--ac-text-primary);
}

.form-section {
  margin-bottom: var(--ac-space-2);
  padding: var(--ac-space-2);
  background: var(--ac-bg-secondary);
  border-radius: var(--ac-radius-md);
  border: 1px solid var(--ac-border-light);
}

.form-row {
  margin-bottom: var(--ac-space-3);
}

.form-label {
  display: block;
  margin-bottom: var(--ac-space-2);
  font-weight: 500;
  font-size: var(--ac-font-size-sm);
  color: var(--ac-text-primary);
}

.form-input {
  width: 100%;
  padding: var(--ac-space-2) var(--ac-space-3);
  border: 1px solid var(--ac-border-light);
  border-radius: var(--ac-radius-md);
  background: var(--ac-bg-primary);
  color: var(--ac-text-primary);
  font-size: var(--ac-font-size-sm);
  font-family: var(--ac-font-family);
  transition: border-color var(--ac-duration-normal) var(--ac-ease-out),
              box-shadow var(--ac-duration-normal) var(--ac-ease-out);
}

.form-input:focus {
  outline: none;
  border-color: var(--ac-primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input::placeholder {
  color: var(--ac-text-muted);
}

.form-actions {
  display: flex;
  gap: var(--ac-space-3);
  margin-top: var(--ac-space-1);
  flex-wrap: wrap;
}

/* 按钮系统 */
.action-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--ac-space-1);
  padding: var(--ac-space-1) var(--ac-space-2);
  border: 1px solid transparent;
  border-radius: var(--ac-radius-md);
  cursor: pointer;
  font-size: var(--ac-font-size-sm);
  font-weight: 500;
  font-family: var(--ac-font-family);
  text-decoration: none;
  transition: all var(--ac-duration-normal) var(--ac-ease-out);
  user-select: none;
  white-space: nowrap;
}

.action-btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.action-btn.secondary {
  background: var(--ac-bg-primary);
  color: var(--ac-text-primary);
  border-color: var(--ac-border-light);
}

.action-btn.secondary:hover {
  background: var(--ac-bg-secondary);
  border-color: var(--ac-border-medium);
}

.action-btn.danger {
  background: var(--ac-danger);
  color: var(--ac-text-inverse);
  border-color: var(--ac-danger);
}

.action-btn.danger:hover {
  background: #dc2626;
  border-color: #dc2626;
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-icon {
  font-size: var(--ac-font-size-sm);
}

.btn-text {
  display: inline;
}

/* 加载动画 */
.loading {
  width: 14px;
  height: 14px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Token显示区域 */
.token-display {
  margin-bottom: var(--ac-space-3);
}

.token-label {
  display: block;
  margin-bottom: var(--ac-space-2);
  font-weight: 500;
  font-size: var(--ac-font-size-sm);
  color: var(--ac-text-primary);
}

.token-value {
  padding: var(--ac-space-2);
  background: var(--ac-bg-secondary);
  border: 1px solid var(--ac-border-light);
  border-radius: var(--ac-radius-md);
  font-family: var(--ac-font-mono);
  font-size: var(--ac-font-size-xs);
  color: var(--ac-text-secondary);
  word-break: break-all;
  cursor: pointer;
  transition: background-color var(--ac-duration-normal) var(--ac-ease-out);
}

.token-value:hover {
  background: var(--ac-bg-primary);
}

/* 响应式设计 */
@media (max-width: 480px) {
  .form-actions {
    flex-direction: column;
  }
  
  .action-btn {
    justify-content: center;
  }
}
`;
