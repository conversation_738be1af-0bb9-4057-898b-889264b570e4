{"format": 1, "restore": {"d:\\Data\\Sync\\Log\\c#\\test\\test.csproj": {}}, "projects": {"d:\\Data\\Sync\\Log\\c#\\test\\test.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "d:\\Data\\Sync\\Log\\c#\\test\\test.csproj", "projectName": "test", "projectPath": "d:\\Data\\Sync\\Log\\c#\\test\\test.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "d:\\Data\\Sync\\Log\\c#\\test\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204/PortableRuntimeIdentifierGraph.json"}}}}}