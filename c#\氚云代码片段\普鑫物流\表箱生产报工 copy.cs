
using System;
using System.Collections.Generic;
using System.Text;
using H3;
using H3.DataModel;
using System.Data;
public class D282605Seibgfbqg1mf1id55ejcjoz6f3 : H3.SmartForm.SmartFormController
{
    public D282605Seibgfbqg1mf1id55ejcjoz6f3(H3.SmartForm.SmartFormRequest request) : base(request)
    {
    }

    protected override void OnLoad(H3.SmartForm.LoadSmartFormResponse response)
    {
        base.OnLoad(response);
    }

    protected override void OnSubmit(string actionName, H3.SmartForm.SmartFormPostValue postValue, H3.SmartForm.SubmitSmartFormResponse response)
    {
        DPuXinBoxReport boxReport = new DPuXinBoxReport(this.Engine, this.Request.UserContext.UserId);
        DPuXinError error = boxReport.Validate(this.Request.BizObject);
        if (error.HasError())
        {
            response.ReturnData = BuildResp(error);
            return;
        }
        base.OnSubmit(actionName, postValue, response);
    }

    protected override void OnWorkflowInstanceStateChanged(H3.Workflow.Instance.WorkflowInstanceState oldState, H3.Workflow.Instance.WorkflowInstanceState newState)
    {

        //流程审批结束,业务规则执行。还有个审批流程结束（finished状态下直接删除），就直接删除也会触发业务规则执行。在submit里面处理
        if (oldState == H3.Workflow.Instance.WorkflowInstanceState.Running && newState == H3.Workflow.Instance.WorkflowInstanceState.Finished)
        {
            //获取销售订单
            string reportId = this.Request.BizObjectId;
            DPuXinBoxReport boxReport = new DPuXinBoxReport(this.Engine, this.Request.UserContext.UserId);
            boxReport.WriteToDetail(reportId);
        }

        base.OnWorkflowInstanceStateChanged(oldState, newState);
    }

    public Dictionary<string, object> BuildResp(DPuXinError xinError)
    {
        Dictionary<string, object> resp = new Dictionary<string, object>();
        if (!string.IsNullOrEmpty(xinError.GetError()))
        {
            resp.Add("Error", xinError.GetError());
        }
        if (xinError.GetErrors() != null && xinError.GetErrors().Count > 0)
        {
            resp.Add("Errors", this.Serialize(xinError.GetErrors()));
        }
        return resp;
    }
}

public class DPuXinError
{
    private bool hasError;
    private string error;
    private Dictionary<string, object> temp = new Dictionary<string, object>();

    public DPuXinError()
    { }
    public void Error(string error)
    {
        this.error = error;
        this.hasError = true;
    }

    public string this[string key]
    {
        set
        {
            temp[key] = value;
        }
    }

    public string GetError()
    {
        return this.error;
    }

    public void PutError()
    {
        errors.Add(this.temp);
        this.temp = new Dictionary<string, object>();
        this.hasError = true;
    }
    public bool HasError()
    {
        return this.hasError;
    }
    public List<Dictionary<string, object>> GetErrors()
    {
        return this.errors;
    }

    private List<Dictionary<string, object>> errors = new List<Dictionary<string, object>>();

}

public class DPuXinBoxReport
{
    private string reportSchema = "D282605Seibgfbqg1mf1id55ejcjoz6f3";
    private string reportChildSchema = "D282605F9ca39816aecd46baba26c8004d2d8716";

    private string reportDetailSchema = "D282605Sc2hh33czw2c9935a17pfux831";

    private string boxPlanSchema = "D282605Skmj5js0xdpn3oxyqjy56mw0u3";

    private string boxPlanChildSchema = "D282605F1014606df8364cb7aade7ffa7b57f756";

    private IEngine Engine;
    private string UserId;

    private DPuXinBoxCalcPrice boxCalcPrice;
    public DPuXinBoxReport(IEngine Engine, string UserId)
    {
        this.Engine = Engine;
        this.UserId = UserId;
        this.boxCalcPrice = new DPuXinBoxCalcPrice(Engine, UserId);
    }

    public DPuXinError Validate(BizObject obj)
    {
        DPuXinError error = new DPuXinError();
        try
        {
            string team = obj["Team"] + string.Empty;
            BizObject[] children = (BizObject[])obj[this.reportChildSchema];
            if (children == null || children.Length == 0)
            {
                throw new Exception("请填写报工明细数据");
            }
            string[] orders = FilterOrders(children);
            DataRowCollection haveApply = this.QueryHaveApply(orders);
            foreach (BizObject child in children)
            {
                try
                {
                    int declaredQuantity = ToInt(child["DeclaredQuantity"]);
                    if (declaredQuantity <= 0)
                    {
                        throw new Exception("没有申报数量");
                    }
                    SyncPlan(child); // 同步计划数据
                    int hasNum = FindCountOfHaveApply(haveApply, child);
                    int count = ToInt(child["Count"]);

                    if (count < (hasNum + declaredQuantity))
                    {
                        throw new Exception(string.Format("超过可以提交数据量, 当前可提交数量：{0}", count - hasNum));
                    }
                }
                catch (Exception ex)
                {
                    error["OrderNo"] = child["OrderNo"] + string.Empty;
                    error["ProductName"] = child["ProductName"] + string.Empty;
                    error["SizeModel"] = child["SizeModel"] + string.Empty;
                    error["Error"] = ex.Message;
                    error.PutError();
                }
            }
        }
        catch (Exception ex)
        {
            error.Error(ex.Message);
        }
        return error;
    }

    public void WriteToDetail(string reportId)
    {
        BizObject report = Load(this.reportSchema, reportId);
        if (report == null)
        {
            return;
        }
        BizObject[] children = (BizObject[])report[this.reportChildSchema];
        if (children == null || children.Length == 0)
        {
            return;
        }

        foreach (BizObject child in children)
        {
            SaveToDetail(child, report);
        }

    }
    public void Rollback(string[] reportIds)
    {
        H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();  //根据物资编码查询库存
        H3.Data.Filter.And andMatcher = new H3.Data.Filter.And();    //构造And匹配器
        andMatcher.Add(new H3.Data.Filter.ItemMatcher("ObjectId", H3.Data.ComparisonOperatorType.In, reportIds));
        filter.Matcher = andMatcher;
        BizObjectSchema schema = this.Engine.BizObjectManager.GetPublishedSchema(this.reportSchema);
        BizObject[] reports = BizObject.GetList(this.Engine, this.UserId,
            schema, GetListScopeType.GlobalAll, filter);
        if (reports != null && reports.Length > 0)
        {
            foreach (BizObject report in reports)
            {
                BizObject[] details = (BizObject[])report[this.reportChildSchema];
                foreach (BizObject detail in details)
                {
                    BizObject reportDetail = Load(this.reportDetailSchema, detail["ObjectId"] + string.Empty);
                    if (reportDetail != null)
                    {
                        reportDetail.Remove();
                    }
                }
                report.Remove();
            }
        }

    }
    private BizObject Load(string schema, string id)
    {
        return BizObject.Load(this.UserId,
            this.Engine, schema, id, false);
    }

    private void SaveToDetail(BizObject obj, BizObject root)
    {
        string[] excludes = { "ParentObjectId", "State" };
        Dictionary<string, object> table = obj.GetValueTable();

        BizObject detail = Load(this.reportDetailSchema, obj["ObjectId"] + string.Empty);
        bool isCreate = false;
        if (detail == null)
        {
            BizObjectSchema detailSchema = this.Engine.BizObjectManager.GetPublishedSchema(this.reportDetailSchema);
            detail = new BizObject(this.Engine, detailSchema, this.UserId);
            detail.Status = BizObjectStatus.Effective; //设置状态生效
            isCreate = true;
        }
        string team = Convert.ToString(root["Team"]);
        detail["Team"] = team;
        detail["ReportDate"] = root["ReportDate"];
        detail["Process"] = ConvertProcess(team);
        foreach (KeyValuePair<string, object> pair in table)
        {
            string key = pair.Key;
            if (key.Contains("."))
            {
                key = key.Substring(key.IndexOf(".") + 1);
            }
            object value = pair.Value;

            if (Array.Exists(excludes, e => e == key))
            {
                continue;
            }
            detail[key] = value;
        }
        this.boxCalcPrice.CalcPrice(detail); // 计算价格

        if (isCreate)
        {
            detail.Create();
        }
        else
        {
            detail.Update();
        }
    }
    private string ConvertProcess(string team)
    {
        if (team == "激光组" || team == "剪折冲组")
        {
            return "钣金";
        }
        if (team == "焊接组")
        {
            return "焊接";
        }
        if (team == "喷塑组")
        {
            return "喷涂";
        }
        if (team == "装配组")
        {
            return "装配";
        }
        return "";
    }

    private string[] FilterOrders(BizObject[] bizObjects)
    {
        List<string> orders = new List<string>();

        foreach (BizObject obj in bizObjects)
        {
            string orderNo = obj["OrderNo"] + string.Empty;
            if (!orders.Contains(orderNo))
            {
                orders.Add(orderNo);
            }
        }
        return orders.ToArray();
    }

    private int FindCountOfHaveApply(DataRowCollection rows, BizObject obj)
    {
        if (rows == null)
        {
            return 0;
        }
        foreach (DataRow row in rows)
        {
            if (Convert.ToString(row["OrderNo"]) == Convert.ToString(obj["OrderNo"])
                && Convert.ToString(row["ProductName"]) == Convert.ToString(obj["ProductName"])
                && Convert.ToString(row["SizeModel"]) == Convert.ToString(obj["SizeModel"]))
            {
                return this.ToInt(row["DeclaredQuantity"]);
            }
        }
        return 0;
    }
    private DataRowCollection QueryHaveApply(string[] orderNos)
    {
        if (orderNos == null || orderNos.Length == 0)
        {
            return null;
        }
        string query = "";
        foreach (string orderNo in orderNos)
        {
            query = query + string.Format("'{0}',", orderNo);
        }
        query = query.Substring(0, query.Length - 1);
        string sql = String.Format("select p.OrderNo OrderNo, p.ProductName ProductName,p.SizeModel SizeModel, sum(p.DeclaredQuantity) DeclaredQuantity from i_{0} p " +
            "where p.OrderNo in ({1}) group by p.OrderNo, p.MaterialNo", this.reportDetailSchema, query);

        DataTable dt = this.Engine.Query.QueryTable(sql, null);
        if (dt == null || dt.Rows == null || dt.Rows.Count == 0)
        {
            return null;
        }

        return dt.Rows;
    }

    private void SyncPlan(BizObject detail)
    {
        string productType = detail["ProductType"] + string.Empty;
        if (string.IsNullOrEmpty(productType))
        {
            productType = "箱体";
        }
        if (productType != "箱体")
        {
            return;
        }
        string boxPlan = detail["BoxPlan"] + string.Empty;
        BizObject plan = null;
        if (string.IsNullOrEmpty(boxPlan))
        {
            H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();
            H3.Data.Filter.And andMatcher = new H3.Data.Filter.And();
            andMatcher.Add(new H3.Data.Filter.ItemMatcher("OrderNo", H3.Data.ComparisonOperatorType.Equal, detail["OrderNo"] + string.Empty));
            andMatcher.Add(new H3.Data.Filter.ItemMatcher("ProductName", H3.Data.ComparisonOperatorType.Equal, detail["ProductName"] + string.Empty));
            andMatcher.Add(new H3.Data.Filter.ItemMatcher("SizeModel", H3.Data.ComparisonOperatorType.Equal, detail["SizeModel"] + string.Empty));
            filter.Matcher = andMatcher;
            BizObjectSchema schema = this.Engine.BizObjectManager.GetPublishedSchema(this.boxPlanSchema);
            BizObject[] plans = BizObject.GetList(this.Engine, this.UserId,
                schema, GetListScopeType.GlobalAll, filter);
            if (plans != null && plans.Length > 0)
            {
                plan = plans[0];
            }
        }
        else
        {
            plan = Load(this.boxPlanSchema, boxPlan);
        }

        if (plan == null)
        {
            throw new Exception("当前报工没有对应的计划");
        }
        string[] waiting = { "OrderNo", "MaterialNo", "ProductName", "Material", "SizeModel", "Count", "TotalTableNum", "TableNum" };
        foreach (string key in waiting)
        {
            detail[key] = plan[key];
        }
        detail["BoxPlan"] = plan["ObjectId"];
    }

    private int ToInt(object obj)
    {
        return Convert.ToInt32(String.IsNullOrEmpty(obj + "") ? "0" + obj : obj);
    }

}

public class DPuXinBoxCalcPrice
{
    private IEngine Engine;
    private string UserId;

    private List<DPuXinBasePrice> rules = new List<DPuXinBasePrice>();
    public DPuXinCalcPrice(IEngine engine, string userId)
    {
        this.Engine = engine;
        this.UserId = userId;

        this.rules.Add(new DPuXinBoxLaserPrice(engine, userId)); //激光
        this.rules.Add(new DPuXinBoxShearPrice(engine, userId)); //剪折冲
        this.rules.Add(new DPuXinBoxWeldingPrice(engine, userId)); //焊接
        this.rules.Add(new DPuXinBoxSprayPrice(engine, userId)); //喷塑
        this.rules.Add(new DPuXinBoxPackagePrice(engine, userId)); //装配
    }

    public void BatchCalcPrice(Dictionary<string, List<BizObject>> reportDict)
    {
        foreach (KeyValuePair<string, List<BizObject>> pair in reportDict)
        {
            this.BatchCalcPrice(pair.Value);
        }
    }

    public void BatchCalcPrice(List<BizObject> reports)
    {
        foreach (BizObject report in reports)
        {
            this.CalcPrice(report);
        }
    }
    public void CalcPrice(BizObject report)
    {
        string process = Convert.ToString(report["team"]);
        int declaredQuantity = Convert.ToInt32(report["DeclaredQuantity"]);
        foreach (DPuXinBasePrice rule in this.rules)
        {
            if (rule.Apply(process))
            {
                try
                {
                    decimal price = rule.Match(report);
                    if (price > 0)
                    {
                        report["Price"] = price;
                        report["SumPrice"] = price * declaredQuantity;
                    }
                    break;
                }
                catch (Exception ex)
                {
                }
            }
        }
    }
}