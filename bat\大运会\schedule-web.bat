cd C:/Users/<USER>/IdeaProjects/schedule-web

git checkout dev
git pull

@rem npm package, build mode app.
call npm install
call npm run build

@rem winrar a -r  schedule.zip ./dist

7z a schedule.zip ./dist/*

scp ./schedule.zip root@**************:/usr/share/nginx/html

echo "ssh nginx dir to unzip study, and rm zip"
ssh root@************** "cd /usr/share/nginx/html;rm -rf ./schedule;unzip -d schedule  schedule.zip;rm -rf ./schedule.zip"

echo "del local schedule zip"
del /f /q "./schedule.zip"

pause