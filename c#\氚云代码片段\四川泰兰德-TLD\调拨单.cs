
using System;
using System.Collections.Generic;
using System.Text;
using H3;

public class D149913650abcebd79e495a940a9aa05d4b9a89 : H3.SmartForm.SmartFormController
{
    public D149913650abcebd79e495a940a9aa05d4b9a89(H3.SmartForm.SmartFormRequest request) : base(request)
    {
    }

    protected override void OnLoad(H3.SmartForm.LoadSmartFormResponse response)
    {
        base.OnLoad(response);
    }

    protected override void OnSubmit(string actionName, H3.SmartForm.SmartFormPostValue postValue, H3.SmartForm.SubmitSmartFormResponse response)
    {
       H3.DataModel.BizObject[] details = (H3.DataModel.BizObject[])this.Request.BizObject["D149913Fcb1c25a59dc2476592b8993a6e1f56f5"]; // 库存明细

        Dictionary<string, int> dict = new Dictionary<string, int>();
        // 合并同类产品
        foreach (H3.DataModel.BizObject detail in details)
        {
            string productId = detail["F0000003"] + string.Empty; // 产品id
            string remark = detail["F0000021"] + string.Empty; // 参数备注
            int outNum = Convert.ToInt32(String.IsNullOrEmpty(detail["F0000008"] + "") ? "0" + detail["F0000008"] : detail["F0000008"]);
            dict.Add(productId + "_" + remark, outNum);
        }
        // 库存校验
        foreach (KeyValuePair<string, int> pair in dict)
        {
            //查询库存,产品id + 参数备注
            string[] result = pair.Key.Split('_');
            string sqlpo = "select F0000007,F0000014,F0000011 from i_D149913b779923207a14917a7e6b909ddcf3863 where F0000001 = '" + result[0] + "' and F0000011 = '" + result[1] + "'"; // 查询库存实际量
            System.Data.DataTable stockTable = this.Request.Engine.Query.QueryTable(sqlpo, null);
            int stockNum = 0;
            int warningNum = 0;
            if (stockTable != null && stockTable.Rows.Count > 0)
            {
                stockNum = Convert.ToInt32(String.IsNullOrEmpty(stockTable.Rows[0]["F0000007"] + "") ? "0" + stockTable.Rows[0]["F0000007"] : stockTable.Rows[0]["F0000007"]);
            }
            if (stockNum<=0 ||stockNum < pair.Value)
            {
                response.ReturnData = new Dictionary<string, object>();
                response.ReturnData.Add("error", "存在产品出库数量超过库存数量，已撤销申请");

                return;
            }
        }
        base.OnSubmit(actionName, postValue, response);
    }
}