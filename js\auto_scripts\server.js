
import http from 'http'
import {exec} from 'child_process'

let ports = 8888;

http
  .createServer(async (req, resp) => {
    try {
      resp.setHeader("Content-Type", "application/json;utf-8");
      switch (req.url) {
        case "/refresh":
          let error;
          await getBody(req).catch((msg) => {
            error = Result.fail(400, msg, resp);
          });
          if (error) break;
          exec("docker_entrypoint.sh", (err, stdout, stderr) => {
            console.log(stdout);
          });
          Result.ok("处理完成", resp);
          break;
        default:
          Result.fail(404, "请求资源不存在", resp);
          return;
      }
    } catch (e) {
      console.log(e.message);
      Result.fail(500, "服务器内部错误", resp);
    }
  })
  .listen(ports);

async function getBody(req) {
  return new Promise((resolve, reject) => {
    let data = [];
    req.on("data", (chunk) => {
      data.push(chunk);
    });
    req.on("end", () => {
      try {
        console.log(
          `${new Date()
            .toLocaleString()
            .replace(/\//g, "-")
            .replace("下午", "")} 请求：${req.url}`
        );
        //let body = JSON.parse(data.toString())
        resolve();
      } catch (e) {
        console.log(`异常:${e.message}`);
        reject("JSON解析失败");
      }
    });
  });
}

class Result {
  code = 200;
  message = "操作成功";

  constructor() {}

  static ok(msg, resp) {
    let result = new Result();
    if (msg) {
      result.message = msg;
    }
    let res = JSON.stringify(result);
    if (resp) {
      resp.end(res);
    }
  }

  static fail(code, msg, resp) {
    let result = new Result();
    if (code) {
      result.code = code;
    }
    if (msg) {
      result.message = msg;
    }
    let res = JSON.stringify(result);
    if (resp) {
      resp.writeHead(code);
      resp.end(res);
    }
    return res;
  }
}

console.log("Server running at http://127.0.0.1:8888/");
