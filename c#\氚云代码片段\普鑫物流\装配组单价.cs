
using System;
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;
using H3;
using H3.DataModel;
using System.Data;
public class D2826055f8adfea96774c7dbd80e0033ec472c3: H3.SmartForm.SmartFormController
{
    public D2826055f8adfea96774c7dbd80e0033ec472c3(H3.SmartForm.SmartFormRequest request): base(request)
    {
    }

    protected override void OnLoad(H3.SmartForm.LoadSmartFormResponse response)
    {
        base.OnLoad(response);
    }

    protected override void OnSubmit(string actionName, H3.SmartForm.SmartFormPostValue postValue, H3.SmartForm.SubmitSmartFormResponse response)
    {
        base.OnSubmit(actionName, postValue, response);
    }
}

public class DPuXinBoxPackagePrice: DPuXinBasePrice
{
    private string ChildSchemaCode = "D282605Ff785a29ec0e2490bb3207b8ddbfb58d9";
    private string AppendSchemaCode = "D282605F55953997a92f41bc90c0ee14ae339344";
    public DPuXinBoxPackagePrice(IEngine engine, string userId): base(engine, userId)
    {
        this.SchemaCode = "D2826055f8adfea96774c7dbd80e0033ec472c3";
    }

    public override List < BizObject > Query()
    {
        // 加载规则
        H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();
        filter.AddSortBy("Sort", H3.Data.Filter.SortDirection.Ascending);
        BizObjectSchema schema = this.Engine.BizObjectManager.GetPublishedSchema(this.SchemaCode);
        BizObject[] items = BizObject.GetList(this.Engine, this.UserId,
            schema, GetListScopeType.GlobalAll, filter);
        return new List<BizObject>(items);
    }


    public override decimal Match(BizObject item)
    {
        List < BizObject > rules = LoadRule();
        string productName = Convert.ToString(item["ProductName"]);
        string keyword = productName;
        decimal price = 0;
        BizObject defaultRule = null;
        foreach(BizObject rule in Rules)
        {
            if(KeyWordMatch(rule, keyword))
            {
                defaultRule = rule;
                break;
            }
            string masterKeyWord = Convert.ToString(rule["MasterKeyWord"]);
            if(masterKeyWord == "*") defaultRule = rule;
        }
        if(defaultRule != null)
        {
            price = RangeRule(defaultRule, item);
            price += CalcAppend(defaultRule, item);
        }


        return price;
    }

    private decimal RangeRule(BizObject rule, BizObject item)
    {
        int tableNum = Convert.ToInt32(item["TableNum"]);
        string productName = Convert.ToString(item["ProductName"]);
        string sizeModel = Convert.ToString(item["SizeModel"]);//规格
        string remark = Convert.ToString(item["Remark"]);
        string keyword = "";

        if(productName.Contains("防冻") || productName.Contains("保温"))
        {
            keyword += "防冻";
        }
        else
        {
            keyword += sizeModel.Contains("集中") ? "集中式" : "条形";
            keyword += productName.Contains("镀锌") ? "镀锌板" : "不锈钢";
        }

        BizObject[] children = (BizObject[]) rule[this.ChildSchemaCode];
        decimal defaultPrice = Convert.ToDecimal(rule["Price"]);
        if(children == null || children.Length == 0)
        {
            return defaultPrice;
        }
        foreach(BizObject child in children)
        {
            string childPrice = child["Price"] + string.Empty;
            if(string.IsNullOrEmpty(childPrice))
            {
                continue;
            }
            string category = Convert.ToString(child["Category"]);
            string material = Convert.ToString(child["Material"]);
            if(keyword != (category + material))
            {
                continue;
            }

            int start = Convert.ToInt32(child["StartTableNum"]);
            int end = Convert.ToInt32(child["EndTableNum"]);
            if(end <= start)
            {
                end = 9999999;
            }
            if(tableNum > start && tableNum <= end)
            {
                return Convert.ToDecimal(child["Price"]);
            }
        }
        return defaultPrice;
    }

    private decimal CalcAppend(BizObject rule, BizObject item) {
        BizObject[] children = (BizObject[]) rule[this.AppendSchemaCode];
        decimal defaultPrice = 0;
        if(children == null || children.Length == 0)
        {
            return defaultPrice;
        }
        foreach(BizObject child in children)
        {
            string childPrice = child["Price"] + string.Empty;
            if(string.IsNullOrEmpty(childPrice))
            {
                continue;
            }
            decimal rulePrice = Convert.ToDecimal(child["Price"]);

            string appendType = Convert.ToString(child["AppendType"]);
            switch(appendType)
            {
                case "按表位数":
                    if(MatchAppend(child, item))
                    {
                        int tableNum = Convert.ToInt32(item["TableNum"]);
                        defaultPrice += rulePrice * tableNum;
                    }

                    break;
                case "按箱数":
                    if(MatchAppend(child, item))
                    {
                        defaultPrice += rulePrice;
                    }

                    break;
                case "按备注数":
                    string remark = Convert.ToString(item["Remark"]);
                    string masterKeyWord = Convert.ToString(rule["MasterKeyWord"]);
                    if(!string.IsNullOrEmpty(remark))
                    {
                        string pattern = string.Format("{0}(\\d*)", masterKeyWord);
                        var matches = Regex.Matches(remark, @pattern);
                        if(matches.Count > 0)
                        {
                            int num = Convert.ToInt32(matches[0].Groups[1] + string.Empty);
                            defaultPrice += rulePrice * num;
                        }
                    }
                    break;
            }
        }
        return defaultPrice;
    }

    private bool MatchAppend(BizObject rule,BizObject item){
        string productName = Convert.ToString(item["ProductName"]);
        if(!KeyWordMatch(rule, productName))
        {
            return false;
        }
        string ruleCustomer = Convert.ToString(rule["Customer"]);
        string customer = Convert.ToString(item["Customer"]);
        if(!string.IsNullOrEmpty(ruleCustomer) && !customer.Contains(ruleCustomer))
        {
            return false;
        }
        return true;
    }

    public override bool Apply(string team)
    {
        return team == "装配组";
    }
}