FROM arm64v8/golang:alpine AS dependencies
WORKDIR /app
RUN go env -w GO111MODULE="on" && go env -w GOPROXY="https://goproxy.cn"

COPY go.sum go.mod ./
RUN go mod tidy

FROM dependencies as build
WORKDIR /app
COPY main.go ./
RUN go get github.com/gin-gonic/gin \
    && go get github.com/gomodule/redigo/redis \
    && go get github.com/sirupsen/logrus \
    && go build -o myurls main.go

FROM scratch
WORKDIR /app
COPY --from=build /app/myurls ./
COPY public/* ./public/
EXPOSE 8002
ENTRYPOINT ["/app/myurls"]