/*! AdminLTE app.js
* ================
* Main JS application file for AdminLTE v2. This file
* should be included in all pages. It controls some layout
* options and implements exclusive AdminLTE plugins.
*
* <AUTHOR> Studio
* @Support <https://www.almsaeedstudio.com>
* @Email   <<EMAIL>>
* @version 2.4.8
* @repository git://github.com/almasaeed2010/AdminLTE.git
* @license MIT <http://opensource.org/licenses/MIT>
*/
function Base64(){angular.module("utf8-base64",[]).constant("base64",function(){/*
         Copyright Vassilis Petroulias [DRDigit]

         Licensed under the Apache License, Version 2.0 (the "License");
         you may not use this file except in compliance with the License.
         You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

         Unless required by applicable law or agreed to in writing, software
         distributed under the License is distributed on an "AS IS" BASIS,
         WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
         See the License for the specific language governing permissions and
         limitations under the License.
         */
var e={alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",lookup:null,ie:/MSIE /.test(navigator.userAgent),ieo:/MSIE [67]/.test(navigator.userAgent),encode:function(t){var n,r,o,i,a=e.toUtf8(t),s=-1,l=a.length,u=[,,,];if(e.ie){for(n=[];++s<l;)r=a[s],o=a[++s],u[0]=r>>2,u[1]=(3&r)<<4|o>>4,isNaN(o)?u[2]=u[3]=64:(i=a[++s],u[2]=(15&o)<<2|i>>6,u[3]=isNaN(i)?64:63&i),n.push(e.alphabet.charAt(u[0]),e.alphabet.charAt(u[1]),e.alphabet.charAt(u[2]),e.alphabet.charAt(u[3]));return n.join("")}for(n="";++s<l;)r=a[s],o=a[++s],u[0]=r>>2,u[1]=(3&r)<<4|o>>4,isNaN(o)?u[2]=u[3]=64:(i=a[++s],u[2]=(15&o)<<2|i>>6,u[3]=isNaN(i)?64:63&i),n+=e.alphabet[u[0]]+e.alphabet[u[1]]+e.alphabet[u[2]]+e.alphabet[u[3]];return n},decode:function(t){if(t=t.replace(/\s/g,""),t.length%4)throw new Error("InvalidLengthError: decode failed: The string to be decoded is not the correct length for a base64 encoded string.");if(/[^A-Za-z0-9+\/=\s]/g.test(t))throw new Error("InvalidCharacterError: decode failed: The string contains characters invalid in a base64 encoded string.");var n,r=e.fromUtf8(t),o=0,i=r.length;if(e.ieo){for(n=[];o<i;)r[o]<128?n.push(String.fromCharCode(r[o++])):r[o]>191&&r[o]<224?n.push(String.fromCharCode((31&r[o++])<<6|63&r[o++])):n.push(String.fromCharCode((15&r[o++])<<12|(63&r[o++])<<6|63&r[o++]));return n.join("")}for(n="";o<i;)r[o]<128?n+=String.fromCharCode(r[o++]):r[o]>191&&r[o]<224?n+=String.fromCharCode((31&r[o++])<<6|63&r[o++]):n+=String.fromCharCode((15&r[o++])<<12|(63&r[o++])<<6|63&r[o++]);return n},toUtf8:function(e){var t,n=-1,r=e.length,o=[];if(/^[\x00-\x7f]*$/.test(e))for(;++n<r;)o.push(e.charCodeAt(n));else for(;++n<r;)t=e.charCodeAt(n),t<128?o.push(t):t<2048?o.push(t>>6|192,63&t|128):o.push(t>>12|224,t>>6&63|128,63&t|128);return o},fromUtf8:function(t){var n,r=-1,o=[],i=[,,,];if(!e.lookup){for(n=e.alphabet.length,e.lookup={};++r<n;)e.lookup[e.alphabet.charAt(r)]=r;r=-1}for(n=t.length;++r<n&&(i[0]=e.lookup[t.charAt(r)],i[1]=e.lookup[t.charAt(++r)],o.push(i[0]<<2|i[1]>>4),i[2]=e.lookup[t.charAt(++r)],64!==i[2])&&(o.push((15&i[1])<<4|i[2]>>2),i[3]=e.lookup[t.charAt(++r)],64!==i[3]);)o.push((3&i[2])<<6|i[3]);return o}},t={decode:function(t){t=t.replace(/-/g,"+").replace(/_/g,"/");var n=t.length%4;if(n){if(1===n)throw new Error("InvalidLengthError: Input base64url string is the wrong length to determine padding");t+=new Array(5-n).join("=")}return e.decode(t)},encode:function(t){return e.encode(t).replace(/\+/g,"-").replace(/\//g,"_").split("=",1)[0]}};return{decode:e.decode,encode:e.encode,urldecode:t.decode,urlencode:t.encode}}())}if("undefined"==typeof jQuery)throw new Error("AdminLTE requires jQuery");+function(e){"use strict";function t(t){return this.each(function(){var o=e(this),a=o.data(n);if(!a){var s=e.extend({},r,o.data(),"object"==typeof t&&t);o.data(n,a=new i(o,s))}if("string"==typeof a){if(void 0===a[t])throw new Error("No method named "+t);a[t]()}})}var n="lte.boxrefresh",r={source:"",params:{},trigger:".refresh-btn",content:".box-body",loadInContent:!0,responseType:"",overlayTemplate:'<div class="overlay"><div class="fa fa-refresh fa-spin"></div></div>',onLoadStart:function(){},onLoadDone:function(e){return e}},o={data:'[data-widget="box-refresh"]'},i=function(t,n){if(this.element=t,this.options=n,this.$overlay=e(n.overlay),""===n.source)throw new Error("Source url was not defined. Please specify a url in your BoxRefresh source option.");this._setUpListeners(),this.load()};i.prototype.load=function(){this._addOverlay(),this.options.onLoadStart.call(e(this)),e.get(this.options.source,this.options.params,function(t){this.options.loadInContent&&e(this.options.content).html(t),this.options.onLoadDone.call(e(this),t),this._removeOverlay()}.bind(this),""!==this.options.responseType&&this.options.responseType)},i.prototype._setUpListeners=function(){e(this.element).on("click",o.trigger,function(e){e&&e.preventDefault(),this.load()}.bind(this))},i.prototype._addOverlay=function(){e(this.element).append(this.$overlay)},i.prototype._removeOverlay=function(){e(this.element).remove(this.$overlay)};var a=e.fn.boxRefresh;e.fn.boxRefresh=t,e.fn.boxRefresh.Constructor=i,e.fn.boxRefresh.noConflict=function(){return e.fn.boxRefresh=a,this},e(window).on("load",function(){e(o.data).each(function(){t.call(e(this))})})}(jQuery),function(e){"use strict";function t(t){return this.each(function(){var o=e(this),i=o.data(n);if(!i){var a=e.extend({},r,o.data(),"object"==typeof t&&t);o.data(n,i=new s(o,a))}if("string"==typeof t){if(void 0===i[t])throw new Error("No method named "+t);i[t]()}})}var n="lte.boxwidget",r={animationSpeed:500,collapseTrigger:'[data-widget="collapse"]',removeTrigger:'[data-widget="remove"]',collapseIcon:"fa-minus",expandIcon:"fa-plus",removeIcon:"fa-times"},o={data:".box",collapsed:".collapsed-box",header:".box-header",body:".box-body",footer:".box-footer",tools:".box-tools"},i={collapsed:"collapsed-box"},a={collapsed:"collapsed.boxwidget",expanded:"expanded.boxwidget",removed:"removed.boxwidget"},s=function(e,t){this.element=e,this.options=t,this._setUpListeners()};s.prototype.toggle=function(){e(this.element).is(o.collapsed)?this.expand():this.collapse()},s.prototype.expand=function(){var t=e.Event(a.expanded),n=this.options.collapseIcon,r=this.options.expandIcon;e(this.element).removeClass(i.collapsed),e(this.element).children(o.header+", "+o.body+", "+o.footer).children(o.tools).find("."+r).removeClass(r).addClass(n),e(this.element).children(o.body+", "+o.footer).slideDown(this.options.animationSpeed,function(){e(this.element).trigger(t)}.bind(this))},s.prototype.collapse=function(){var t=e.Event(a.collapsed),n=this.options.collapseIcon,r=this.options.expandIcon;e(this.element).children(o.header+", "+o.body+", "+o.footer).children(o.tools).find("."+n).removeClass(n).addClass(r),e(this.element).children(o.body+", "+o.footer).slideUp(this.options.animationSpeed,function(){e(this.element).addClass(i.collapsed),e(this.element).trigger(t)}.bind(this))},s.prototype.remove=function(){var t=e.Event(a.removed);e(this.element).slideUp(this.options.animationSpeed,function(){e(this.element).trigger(t),e(this.element).remove()}.bind(this))},s.prototype._setUpListeners=function(){var t=this;e(this.element).on("click",this.options.collapseTrigger,function(n){return n&&n.preventDefault(),t.toggle(e(this)),!1}),e(this.element).on("click",this.options.removeTrigger,function(n){return n&&n.preventDefault(),t.remove(e(this)),!1})};var l=e.fn.boxWidget;e.fn.boxWidget=t,e.fn.boxWidget.Constructor=s,e.fn.boxWidget.noConflict=function(){return e.fn.boxWidget=l,this},e(window).on("load",function(){e(o.data).each(function(){t.call(e(this))})})}(jQuery),function(e){"use strict";function t(t){return this.each(function(){var o=e(this),i=o.data(n);if(!i){var a=e.extend({},r,o.data(),"object"==typeof t&&t);o.data(n,i=new s(o,a))}"string"==typeof t&&i.toggle()})}var n="lte.controlsidebar",r={slide:!0},o={sidebar:".control-sidebar",data:'[data-toggle="control-sidebar"]',open:".control-sidebar-open",bg:".control-sidebar-bg",wrapper:".wrapper",content:".content-wrapper",boxed:".layout-boxed"},i={open:"control-sidebar-open",fixed:"fixed"},a={collapsed:"collapsed.controlsidebar",expanded:"expanded.controlsidebar"},s=function(e,t){this.element=e,this.options=t,this.hasBindedResize=!1,this.init()};s.prototype.init=function(){e(this.element).is(o.data)||e(this).on("click",this.toggle),this.fix(),e(window).resize(function(){this.fix()}.bind(this))},s.prototype.toggle=function(t){t&&t.preventDefault(),this.fix(),e(o.sidebar).is(o.open)||e("body").is(o.open)?this.collapse():this.expand()},s.prototype.expand=function(){this.options.slide?e(o.sidebar).addClass(i.open):e("body").addClass(i.open),e(this.element).trigger(e.Event(a.expanded))},s.prototype.collapse=function(){e("body, "+o.sidebar).removeClass(i.open),e(this.element).trigger(e.Event(a.collapsed))},s.prototype.fix=function(){e("body").is(o.boxed)&&this._fixForBoxed(e(o.bg))},s.prototype._fixForBoxed=function(t){t.css({position:"absolute",height:e(o.wrapper).height()})};var l=e.fn.controlSidebar;e.fn.controlSidebar=t,e.fn.controlSidebar.Constructor=s,e.fn.controlSidebar.noConflict=function(){return e.fn.controlSidebar=l,this},e(document).on("click",o.data,function(n){n&&n.preventDefault(),t.call(e(this),"toggle")})}(jQuery),function(e){"use strict";function t(t){return this.each(function(){var r=e(this),o=r.data(n);o||r.data(n,o=new i(r)),"string"==typeof t&&o.toggle(r)})}var n="lte.directchat",r={data:'[data-widget="chat-pane-toggle"]',box:".direct-chat"},o={open:"direct-chat-contacts-open"},i=function(e){this.element=e};i.prototype.toggle=function(e){e.parents(r.box).first().toggleClass(o.open)};var a=e.fn.directChat;e.fn.directChat=t,e.fn.directChat.Constructor=i,e.fn.directChat.noConflict=function(){return e.fn.directChat=a,this},e(document).on("click",r.data,function(n){n&&n.preventDefault(),t.call(e(this),"toggle")})}(jQuery),function(e){"use strict";function t(t){return this.each(function(){var o=e(this),i=o.data(n);if(!i){var s=e.extend({},r,o.data(),"object"==typeof t&&t);o.data(n,i=new a(s))}if("string"==typeof t){if(void 0===i[t])throw new Error("No method named "+t);i[t]()}})}var n="lte.layout",r={slimscroll:!0,resetHeight:!0},o={wrapper:".wrapper",contentWrapper:".content-wrapper",layoutBoxed:".layout-boxed",mainFooter:".main-footer",mainHeader:".main-header",sidebar:".sidebar",controlSidebar:".control-sidebar",fixed:".fixed",sidebarMenu:".sidebar-menu",logo:".main-header .logo"},i={fixed:"fixed",holdTransition:"hold-transition"},a=function(e){this.options=e,this.bindedResize=!1,this.activate()};a.prototype.activate=function(){this.fix(),this.fixSidebar(),e("body").removeClass(i.holdTransition),this.options.resetHeight&&e("body, html, "+o.wrapper).css({height:"auto","min-height":"100%"}),this.bindedResize||(e(window).resize(function(){this.fix(),this.fixSidebar(),e(o.logo+", "+o.sidebar).one("webkitTransitionEnd otransitionend oTransitionEnd msTransitionEnd transitionend",function(){this.fix(),this.fixSidebar()}.bind(this))}.bind(this)),this.bindedResize=!0),e(o.sidebarMenu).on("expanded.tree",function(){this.fix(),this.fixSidebar()}.bind(this)),e(o.sidebarMenu).on("collapsed.tree",function(){this.fix(),this.fixSidebar()}.bind(this))},a.prototype.fix=function(){e(o.layoutBoxed+" > "+o.wrapper).css("overflow","hidden");var t=e(o.mainFooter).outerHeight()||0,n=e(o.mainHeader).outerHeight()||0,r=n+t,a=e(window).height(),s=e(o.sidebar).height()||0;if(e("body").hasClass(i.fixed))e(o.contentWrapper).css("min-height",a-t);else{var l;a>=s?(e(o.contentWrapper).css("min-height",a-r),l=a-r):(e(o.contentWrapper).css("min-height",s),l=s);var u=e(o.controlSidebar);void 0!==u&&u.height()>l&&e(o.contentWrapper).css("min-height",u.height())}},a.prototype.fixSidebar=function(){if(!e("body").hasClass(i.fixed))return void(void 0!==e.fn.slimScroll&&e(o.sidebar).slimScroll({destroy:!0}).height("auto"));this.options.slimscroll&&void 0!==e.fn.slimScroll&&e(o.sidebar).slimScroll({height:e(window).height()-e(o.mainHeader).height()+"px"})};var s=e.fn.layout;e.fn.layout=t,e.fn.layout.Constuctor=a,e.fn.layout.noConflict=function(){return e.fn.layout=s,this},e(window).on("load",function(){t.call(e("body"))})}(jQuery),function(e){"use strict";function t(t){return this.each(function(){var o=e(this),i=o.data(n);if(!i){var a=e.extend({},r,o.data(),"object"==typeof t&&t);o.data(n,i=new s(a))}"toggle"===t&&i.toggle()})}var n="lte.pushmenu",r={collapseScreenSize:767,expandOnHover:!1,expandTransitionDelay:200},o={collapsed:".sidebar-collapse",open:".sidebar-open",mainSidebar:".main-sidebar",contentWrapper:".content-wrapper",searchInput:".sidebar-form .form-control",button:'[data-toggle="push-menu"]',mini:".sidebar-mini",expanded:".sidebar-expanded-on-hover",layoutFixed:".fixed"},i={collapsed:"sidebar-collapse",open:"sidebar-open",mini:"sidebar-mini",expanded:"sidebar-expanded-on-hover",expandFeature:"sidebar-mini-expand-feature",layoutFixed:"fixed"},a={expanded:"expanded.pushMenu",collapsed:"collapsed.pushMenu"},s=function(e){this.options=e,this.init()};s.prototype.init=function(){(this.options.expandOnHover||e("body").is(o.mini+o.layoutFixed))&&(this.expandOnHover(),e("body").addClass(i.expandFeature)),e(o.contentWrapper).click(function(){e(window).width()<=this.options.collapseScreenSize&&e("body").hasClass(i.open)&&this.close()}.bind(this)),e(o.searchInput).click(function(e){e.stopPropagation()})},s.prototype.toggle=function(){var t=e(window).width(),n=!e("body").hasClass(i.collapsed);t<=this.options.collapseScreenSize&&(n=e("body").hasClass(i.open)),n?this.close():this.open()},s.prototype.open=function(){e(window).width()>this.options.collapseScreenSize?e("body").removeClass(i.collapsed).trigger(e.Event(a.expanded)):e("body").addClass(i.open).trigger(e.Event(a.expanded))},s.prototype.close=function(){e(window).width()>this.options.collapseScreenSize?e("body").addClass(i.collapsed).trigger(e.Event(a.collapsed)):e("body").removeClass(i.open+" "+i.collapsed).trigger(e.Event(a.collapsed))},s.prototype.expandOnHover=function(){e(o.mainSidebar).hover(function(){e("body").is(o.mini+o.collapsed)&&e(window).width()>this.options.collapseScreenSize&&this.expand()}.bind(this),function(){e("body").is(o.expanded)&&this.collapse()}.bind(this))},s.prototype.expand=function(){setTimeout(function(){e("body").removeClass(i.collapsed).addClass(i.expanded)},this.options.expandTransitionDelay)},s.prototype.collapse=function(){setTimeout(function(){e("body").removeClass(i.expanded).addClass(i.collapsed)},this.options.expandTransitionDelay)};var l=e.fn.pushMenu;e.fn.pushMenu=t,e.fn.pushMenu.Constructor=s,e.fn.pushMenu.noConflict=function(){return e.fn.pushMenu=l,this},e(document).on("click",o.button,function(n){n.preventDefault(),t.call(e(this),"toggle")}),e(window).on("load",function(){t.call(e(o.button))})}(jQuery),function(e){"use strict";function t(t){return this.each(function(){var o=e(this),i=o.data(n);if(!i){var s=e.extend({},r,o.data(),"object"==typeof t&&t);o.data(n,i=new a(o,s))}if("string"==typeof i){if(void 0===i[t])throw new Error("No method named "+t);i[t]()}})}var n="lte.todolist",r={onCheck:function(e){return e},onUnCheck:function(e){return e}},o={data:'[data-widget="todo-list"]'},i={done:"done"},a=function(e,t){this.element=e,this.options=t,this._setUpListeners()};a.prototype.toggle=function(e){if(e.parents(o.li).first().toggleClass(i.done),!e.prop("checked"))return void this.unCheck(e);this.check(e)},a.prototype.check=function(e){this.options.onCheck.call(e)},a.prototype.unCheck=function(e){this.options.onUnCheck.call(e)},a.prototype._setUpListeners=function(){var t=this;e(this.element).on("change ifChanged","input:checkbox",function(){t.toggle(e(this))})};var s=e.fn.todoList;e.fn.todoList=t,e.fn.todoList.Constructor=a,e.fn.todoList.noConflict=function(){return e.fn.todoList=s,this},e(window).on("load",function(){e(o.data).each(function(){t.call(e(this))})})}(jQuery),function(e){"use strict";function t(t){return this.each(function(){var o=e(this);if(!o.data(n)){var i=e.extend({},r,o.data(),"object"==typeof t&&t);o.data(n,new s(o,i))}})}var n="lte.tree",r={animationSpeed:500,accordion:!0,followLink:!1,trigger:".treeview a"},o={tree:".tree",treeview:".treeview",treeviewMenu:".treeview-menu",open:".menu-open, .active",li:"li",data:'[data-widget="tree"]',active:".active"},i={open:"menu-open",tree:"tree"},a={collapsed:"collapsed.tree",expanded:"expanded.tree"},s=function(t,n){this.element=t,this.options=n,e(this.element).addClass(i.tree),e(o.treeview+o.active,this.element).addClass(i.open),this._setUpListeners()};s.prototype.toggle=function(e,t){var n=e.next(o.treeviewMenu),r=e.parent(),a=r.hasClass(i.open);r.is(o.treeview)&&(this.options.followLink&&"#"!==e.attr("href")||t.preventDefault(),a?this.collapse(n,r):this.expand(n,r))},s.prototype.expand=function(t,n){var r=e.Event(a.expanded);if(this.options.accordion){var s=n.siblings(o.open),l=s.children(o.treeviewMenu);this.collapse(l,s)}n.addClass(i.open),t.slideDown(this.options.animationSpeed,function(){e(this.element).trigger(r)}.bind(this))},s.prototype.collapse=function(t,n){var r=e.Event(a.collapsed);n.removeClass(i.open),t.slideUp(this.options.animationSpeed,function(){e(this.element).trigger(r)}.bind(this))},s.prototype._setUpListeners=function(){var t=this;e(this.element).on("click",this.options.trigger,function(n){t.toggle(e(this),n)})};var l=e.fn.tree;e.fn.tree=t,e.fn.tree.Constructor=s,e.fn.tree.noConflict=function(){return e.fn.tree=l,this},e(window).on("load",function(){e(o.data).each(function(){t.call(e(this))})})}(jQuery),/*! Copyright (c) 2011 Piotr Rochala (http://rocha.la)
 * Dual licensed under the MIT (http://www.opensource.org/licenses/mit-license.php)
 * and GPL (http://www.opensource.org/licenses/gpl-license.php) licenses.
 *
 * Version: 1.3.8
 *
 */
function(e){e.fn.extend({slimScroll:function(n){var r=e.extend({width:"auto",height:"250px",size:"7px",color:"#000",position:"right",distance:"1px",start:"top",opacity:.4,alwaysVisible:!1,disableFadeOut:!1,railVisible:!1,railColor:"#333",railOpacity:.2,railDraggable:!0,railClass:"slimScrollRail",barClass:"slimScrollBar",wrapperClass:"slimScrollDiv",allowPageScroll:!1,wheelStep:20,touchScrollStep:200,borderRadius:"7px",railBorderRadius:"7px"},n);return this.each(function(){function o(t){if(u){t=t||window.event;var n=0;t.wheelDelta&&(n=-t.wheelDelta/120),t.detail&&(n=t.detail/3),e(t.target||t.srcTarget||t.srcElement).closest("."+r.wrapperClass).is(y.parent())&&i(n,!0),t.preventDefault&&!v&&t.preventDefault(),v||(t.returnValue=!1)}}function i(e,t,n){v=!1;var o=y.outerHeight()-w.outerHeight();t&&(t=parseInt(w.css("top"))+e*parseInt(r.wheelStep)/100*w.outerHeight(),t=Math.min(Math.max(t,0),o),t=0<e?Math.ceil(t):Math.floor(t),w.css({top:t+"px"})),g=parseInt(w.css("top"))/(y.outerHeight()-w.outerHeight()),t=g*(y[0].scrollHeight-y.outerHeight()),n&&(t=e,e=t/y[0].scrollHeight*y.outerHeight(),e=Math.min(Math.max(e,0),o),w.css({top:e+"px"})),y.scrollTop(t),y.trigger("slimscrolling",~~t),s(),l()}function a(){h=Math.max(y.outerHeight()/y[0].scrollHeight*y.outerHeight(),30),w.css({height:h+"px"});var e=h==y.outerHeight()?"none":"block";w.css({display:e})}function s(){a(),clearTimeout(f),g==~~g?(v=r.allowPageScroll,m!=g&&y.trigger("slimscroll",0==~~g?"top":"bottom")):v=!1,m=g,h>=y.outerHeight()?v=!0:(w.stop(!0,!0).fadeIn("fast"),r.railVisible&&C.stop(!0,!0).fadeIn("fast"))}function l(){r.alwaysVisible||(f=setTimeout(function(){r.disableFadeOut&&u||c||d||(w.fadeOut("slow"),C.fadeOut("slow"))},1e3))}var u,c,d,f,p,h,g,m,v=!1,y=e(this);if(y.parent().hasClass(r.wrapperClass)){var b=y.scrollTop(),w=y.siblings("."+r.barClass),C=y.siblings("."+r.railClass);if(a(),e.isPlainObject(n)){if("height"in n&&"auto"==n.height){y.parent().css("height","auto"),y.css("height","auto");var S=y.parent().parent().height();y.parent().css("height",S),y.css("height",S)}else"height"in n&&(S=n.height,y.parent().css("height",S),y.css("height",S));if("scrollTo"in n)b=parseInt(r.scrollTo);else if("scrollBy"in n)b+=parseInt(r.scrollBy);else if("destroy"in n)return w.remove(),C.remove(),void y.unwrap();i(b,!1,!0)}}else if(!(e.isPlainObject(n)&&"destroy"in n)){r.height="auto"==r.height?y.parent().height():r.height,b=e("<div></div>").addClass(r.wrapperClass).css({position:"relative",overflow:"hidden",width:r.width,height:r.height}),y.css({overflow:"hidden",width:r.width,height:r.height});var C=e("<div></div>").addClass(r.railClass).css({width:r.size,height:"100%",position:"absolute",top:0,display:r.alwaysVisible&&r.railVisible?"block":"none","border-radius":r.railBorderRadius,background:r.railColor,opacity:r.railOpacity,zIndex:90}),w=e("<div></div>").addClass(r.barClass).css({background:r.color,width:r.size,position:"absolute",top:0,opacity:r.opacity,display:r.alwaysVisible?"block":"none","border-radius":r.borderRadius,BorderRadius:r.borderRadius,MozBorderRadius:r.borderRadius,WebkitBorderRadius:r.borderRadius,zIndex:99}),S="right"==r.position?{right:r.distance}:{left:r.distance};C.css(S),w.css(S),y.wrap(b),y.parent().append(w),y.parent().append(C),r.railDraggable&&w.bind("mousedown",function(n){var r=e(document);return d=!0,t=parseFloat(w.css("top")),pageY=n.pageY,r.bind("mousemove.slimscroll",function(e){currTop=t+e.pageY-pageY,w.css("top",currTop),i(0,w.position().top,!1)}),r.bind("mouseup.slimscroll",function(e){d=!1,l(),r.unbind(".slimscroll")}),!1}).bind("selectstart.slimscroll",function(e){return e.stopPropagation(),e.preventDefault(),!1}),C.hover(function(){s()},function(){l()}),w.hover(function(){c=!0},function(){c=!1}),y.hover(function(){u=!0,s(),l()},function(){u=!1,l()}),y.bind("touchstart",function(e,t){e.originalEvent.touches.length&&(p=e.originalEvent.touches[0].pageY)}),y.bind("touchmove",function(e){v||e.originalEvent.preventDefault(),e.originalEvent.touches.length&&(i((p-e.originalEvent.touches[0].pageY)/r.touchScrollStep,!0),p=e.originalEvent.touches[0].pageY)}),a(),"bottom"===r.start?(w.css({top:y.outerHeight()-w.outerHeight()}),i(0,!0)):"top"!==r.start&&(i(e(r.start).position().top,null,!0),r.alwaysVisible||w.hide()),window.addEventListener?(this.addEventListener("DOMMouseScroll",o,!1),this.addEventListener("mousewheel",o,!1)):document.attachEvent("onmousewheel",o)}}),this}}),e.fn.extend({slimscroll:e.fn.slimScroll})}(jQuery),function(e,t,n){"use strict";!function e(t,n,r){function o(a,s){if(!n[a]){if(!t[a]){var l="function"==typeof require&&require;if(!s&&l)return l(a,!0);if(i)return i(a,!0);var u=new Error("Cannot find module '"+a+"'");throw u.code="MODULE_NOT_FOUND",u}var c=n[a]={exports:{}};t[a][0].call(c.exports,function(e){var n=t[a][1][e];return o(n||e)},c,c.exports,e,t,n,r)}return n[a].exports}for(var i="function"==typeof require&&require,a=0;a<r.length;a++)o(r[a]);return o}({1:[function(r,o,i){var a=function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(i,"__esModule",{value:!0});var s,l,u,c,d=r("./modules/handle-dom"),f=r("./modules/utils"),p=r("./modules/handle-swal-dom"),h=r("./modules/handle-click"),g=r("./modules/handle-key"),m=a(g),v=r("./modules/default-params"),y=a(v),b=r("./modules/set-params"),w=a(b);i.default=u=c=function(){function r(e){var t=o;return t[e]===n?y.default[e]:t[e]}var o=arguments[0];if(d.addClass(t.body,"stop-scrolling"),p.resetInput(),o===n)return f.logStr("SweetAlert expects at least 1 attribute!"),!1;var i=f.extend({},y.default);switch(typeof o){case"string":i.title=o,i.text=arguments[1]||"",i.type=arguments[2]||"";break;case"object":if(o.title===n)return f.logStr('Missing "title" argument!'),!1;i.title=o.title;for(var a in y.default)i[a]=r(a);i.confirmButtonText=i.showCancelButton?"Confirm":y.default.confirmButtonText,i.confirmButtonText=r("confirmButtonText"),i.doneFunction=arguments[1]||null;break;default:return f.logStr('Unexpected type of argument! Expected "string" or "object", got '+typeof o),!1}w.default(i),p.fixVerticalPosition(),p.openModal(arguments[1]);for(var u=p.getModal(),g=u.querySelectorAll("button"),v=["onclick","onmouseover","onmouseout","onmousedown","onmouseup","onfocus"],b=function(e){return h.handleButton(e,i,u)},C=0;C<g.length;C++)for(var S=0;S<v.length;S++){var x=v[S];g[C][x]=b}p.getOverlay().onclick=b,s=e.onkeydown;var T=function(e){return m.default(e,i,u)};e.onkeydown=T,e.onfocus=function(){setTimeout(function(){l!==n&&(l.focus(),l=n)},0)},c.enableButtons()},u.setDefaults=c.setDefaults=function(e){if(!e)throw new Error("userParams is required");if("object"!=typeof e)throw new Error("userParams has to be a object");f.extend(y.default,e)},u.close=c.close=function(){var r=p.getModal();d.fadeOut(p.getOverlay(),5),d.fadeOut(r,5),d.removeClass(r,"showSweetAlert"),d.addClass(r,"hideSweetAlert"),d.removeClass(r,"visible");var o=r.querySelector(".sa-icon.sa-success");d.removeClass(o,"animate"),d.removeClass(o.querySelector(".sa-tip"),"animateSuccessTip"),d.removeClass(o.querySelector(".sa-long"),"animateSuccessLong");var i=r.querySelector(".sa-icon.sa-error");d.removeClass(i,"animateErrorIcon"),d.removeClass(i.querySelector(".sa-x-mark"),"animateXMark");var a=r.querySelector(".sa-icon.sa-warning");return d.removeClass(a,"pulseWarning"),d.removeClass(a.querySelector(".sa-body"),"pulseWarningIns"),d.removeClass(a.querySelector(".sa-dot"),"pulseWarningIns"),setTimeout(function(){var e=r.getAttribute("data-custom-class");d.removeClass(r,e)},300),d.removeClass(t.body,"stop-scrolling"),e.onkeydown=s,e.previousActiveElement&&e.previousActiveElement.focus(),l=n,clearTimeout(r.timeout),!0},u.showInputError=c.showInputError=function(e){var t=p.getModal(),n=t.querySelector(".sa-input-error");d.addClass(n,"show");var r=t.querySelector(".sa-error-container");d.addClass(r,"show"),r.querySelector("p").innerHTML=e,setTimeout(function(){u.enableButtons()},1),t.querySelector("input").focus()},u.resetInputError=c.resetInputError=function(e){if(e&&13===e.keyCode)return!1;var t=p.getModal(),n=t.querySelector(".sa-input-error");d.removeClass(n,"show");var r=t.querySelector(".sa-error-container");d.removeClass(r,"show")},u.disableButtons=c.disableButtons=function(){var e=p.getModal(),t=e.querySelector("button.confirm"),n=e.querySelector("button.cancel");t.disabled=!0,n.disabled=!0},u.enableButtons=c.enableButtons=function(){var e=p.getModal(),t=e.querySelector("button.confirm"),n=e.querySelector("button.cancel");t.disabled=!1,n.disabled=!1},void 0!==e?e.sweetAlert=e.swal=u:f.logStr("SweetAlert is a frontend module!"),o.exports=i.default},{"./modules/default-params":2,"./modules/handle-click":3,"./modules/handle-dom":4,"./modules/handle-key":5,"./modules/handle-swal-dom":6,"./modules/set-params":8,"./modules/utils":9}],2:[function(e,t,n){Object.defineProperty(n,"__esModule",{value:!0});var r={title:"",text:"",type:null,allowOutsideClick:!1,showConfirmButton:!0,showCancelButton:!1,closeOnConfirm:!0,closeOnCancel:!0,confirmButtonText:"OK",confirmButtonColor:"#8CD4F5",cancelButtonText:"Cancel",imageUrl:null,imageSize:null,timer:null,customClass:"",html:!1,animation:!0,allowEscapeKey:!0,inputType:"text",inputPlaceholder:"",inputValue:"",showLoaderOnConfirm:!1};n.default=r,t.exports=n.default},{}],3:[function(t,n,r){Object.defineProperty(r,"__esModule",{value:!0});var o=t("./utils"),i=(t("./handle-swal-dom"),t("./handle-dom")),a=function(t,n,r){function a(e){h&&n.confirmButtonColor&&(p.style.backgroundColor=e)}var u,c,d,f=t||e.event,p=f.target||f.srcElement,h=-1!==p.className.indexOf("confirm"),g=-1!==p.className.indexOf("sweet-overlay"),m=i.hasClass(r,"visible"),v=n.doneFunction&&"true"===r.getAttribute("data-has-done-function");switch(h&&n.confirmButtonColor&&(u=n.confirmButtonColor,c=o.colorLuminance(u,-.04),d=o.colorLuminance(u,-.14)),f.type){case"mouseover":a(c);break;case"mouseout":a(u);break;case"mousedown":a(d);break;case"mouseup":a(c);break;case"focus":var y=r.querySelector("button.confirm"),b=r.querySelector("button.cancel");h?b.style.boxShadow="none":y.style.boxShadow="none";break;case"click":var w=r===p,C=i.isDescendant(r,p);if(!w&&!C&&m&&!n.allowOutsideClick)break;h&&v&&m?s(r,n):v&&m||g?l(r,n):i.isDescendant(r,p)&&"BUTTON"===p.tagName&&sweetAlert.close()}},s=function(e,t){var n=!0;i.hasClass(e,"show-input")&&((n=e.querySelector("input").value)||(n="")),t.doneFunction(n),t.closeOnConfirm&&sweetAlert.close(),t.showLoaderOnConfirm&&sweetAlert.disableButtons()},l=function(e,t){var n=String(t.doneFunction).replace(/\s/g,"");"function("===n.substring(0,9)&&")"!==n.substring(9,10)&&t.doneFunction(!1),t.closeOnCancel&&sweetAlert.close()};r.default={handleButton:a,handleConfirm:s,handleCancel:l},n.exports=r.default},{"./handle-dom":4,"./handle-swal-dom":6,"./utils":9}],4:[function(n,r,o){Object.defineProperty(o,"__esModule",{value:!0});var i=function(e,t){return new RegExp(" "+t+" ").test(" "+e.className+" ")},a=function(e,t){i(e,t)||(e.className+=" "+t)},s=function(e,t){var n=" "+e.className.replace(/[\t\r\n]/g," ")+" ";if(i(e,t)){for(;n.indexOf(" "+t+" ")>=0;)n=n.replace(" "+t+" "," ");e.className=n.replace(/^\s+|\s+$/g,"")}},l=function(e){var n=t.createElement("div");return n.appendChild(t.createTextNode(e)),n.innerHTML},u=function(e){e.style.opacity="",e.style.display="block"},c=function(e){if(e&&!e.length)return u(e);for(var t=0;t<e.length;++t)u(e[t])},d=function(e){e.style.opacity="",e.style.display="none"},f=function(e){if(e&&!e.length)return d(e);for(var t=0;t<e.length;++t)d(e[t])},p=function(e,t){for(var n=t.parentNode;null!==n;){if(n===e)return!0;n=n.parentNode}return!1},h=function(e){e.style.left="-9999px",e.style.display="block";var t,n=e.clientHeight;return t="undefined"!=typeof getComputedStyle?parseInt(getComputedStyle(e).getPropertyValue("padding-top"),10):parseInt(e.currentStyle.padding),e.style.left="",e.style.display="none","-"+parseInt((n+t)/2)+"px"},g=function(e,t){if(+e.style.opacity<1){t=t||16,e.style.opacity=0,e.style.display="block";var n=+new Date,r=function(e){function t(){return e.apply(this,arguments)}return t.toString=function(){return e.toString()},t}(function(){e.style.opacity=+e.style.opacity+(new Date-n)/100,n=+new Date,+e.style.opacity<1&&setTimeout(r,t)});r()}e.style.display="block"},m=function(e,t){t=t||16,e.style.opacity=1;var n=+new Date,r=function(e){function t(){return e.apply(this,arguments)}return t.toString=function(){return e.toString()},t}(function(){e.style.opacity=+e.style.opacity-(new Date-n)/100,n=+new Date,+e.style.opacity>0?setTimeout(r,t):e.style.display="none"});r()},v=function(n){if("function"==typeof MouseEvent){var r=new MouseEvent("click",{view:e,bubbles:!1,cancelable:!0});n.dispatchEvent(r)}else if(t.createEvent){var o=t.createEvent("MouseEvents");o.initEvent("click",!1,!1),n.dispatchEvent(o)}else t.createEventObject?n.fireEvent("onclick"):"function"==typeof n.onclick&&n.onclick()},y=function(t){"function"==typeof t.stopPropagation?(t.stopPropagation(),t.preventDefault()):e.event&&e.event.hasOwnProperty("cancelBubble")&&(e.event.cancelBubble=!0)};o.hasClass=i,o.addClass=a,o.removeClass=s,o.escapeHtml=l,o._show=u,o.show=c,o._hide=d,o.hide=f,o.isDescendant=p,o.getTopMargin=h,o.fadeIn=g,o.fadeOut=m,o.fireClick=v,o.stopEventPropagation=y},{}],5:[function(t,r,o){Object.defineProperty(o,"__esModule",{value:!0});var i=t("./handle-dom"),a=t("./handle-swal-dom"),s=function(t,r,o){var s=t||e.event,l=s.keyCode||s.which,u=o.querySelector("button.confirm"),c=o.querySelector("button.cancel"),d=o.querySelectorAll("button[tabindex]");if(-1!==[9,13,32,27].indexOf(l)){for(var f=s.target||s.srcElement,p=-1,h=0;h<d.length;h++)if(f===d[h]){p=h;break}9===l?(f=-1===p?u:p===d.length-1?d[0]:d[p+1],i.stopEventPropagation(s),f.focus(),r.confirmButtonColor&&a.setFocusStyle(f,r.confirmButtonColor)):13===l?("INPUT"===f.tagName&&(f=u,u.focus()),f=-1===p?u:n):27===l&&!0===r.allowEscapeKey?(f=c,i.fireClick(f,s)):f=n}};o.default=s,r.exports=o.default},{"./handle-dom":4,"./handle-swal-dom":6}],6:[function(n,r,o){var i=function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(o,"__esModule",{value:!0});var a=n("./utils"),s=n("./handle-dom"),l=n("./default-params"),u=i(l),c=n("./injected-html"),d=i(c),f=function(){var e=t.createElement("div");for(e.innerHTML=d.default;e.firstChild;)t.body.appendChild(e.firstChild)},p=function(e){function t(){return e.apply(this,arguments)}return t.toString=function(){return e.toString()},t}(function(){var e=t.querySelector(".sweet-alert");return e||(f(),e=p()),e}),h=function(){var e=p();return e?e.querySelector("input"):void 0},g=function(){return t.querySelector(".sweet-overlay")},m=function(e,t){var n=a.hexToRgb(t);e.style.boxShadow="0 0 2px rgba("+n+", 0.8), inset 0 0 0 1px rgba(0, 0, 0, 0.05)"},v=function(n){var r=p();s.fadeIn(g(),10),s.show(r),s.addClass(r,"showSweetAlert"),s.removeClass(r,"hideSweetAlert"),e.previousActiveElement=t.activeElement,r.querySelector("button.confirm").focus(),setTimeout(function(){s.addClass(r,"visible")},500);var o=r.getAttribute("data-timer");if("null"!==o&&""!==o){var i=n;r.timeout=setTimeout(function(){(i||null)&&"true"===r.getAttribute("data-has-done-function")?i(null):sweetAlert.close()},o)}},y=function(){var e=p(),t=h();s.removeClass(e,"show-input"),t.value=u.default.inputValue,t.setAttribute("type",u.default.inputType),t.setAttribute("placeholder",u.default.inputPlaceholder),b()},b=function(e){if(e&&13===e.keyCode)return!1;var t=p(),n=t.querySelector(".sa-input-error");s.removeClass(n,"show");var r=t.querySelector(".sa-error-container");s.removeClass(r,"show")},w=function(){p().style.marginTop=s.getTopMargin(p())};o.sweetAlertInitialize=f,o.getModal=p,o.getOverlay=g,o.getInput=h,o.setFocusStyle=m,o.openModal=v,o.resetInput=y,o.resetInputError=b,o.fixVerticalPosition=w},{"./default-params":2,"./handle-dom":4,"./injected-html":7,"./utils":9}],7:[function(e,t,n){Object.defineProperty(n,"__esModule",{value:!0});n.default='<div class="sweet-overlay" tabIndex="-1"></div><div class="sweet-alert"><div class="sa-icon sa-error">\n      <span class="sa-x-mark">\n        <span class="sa-line sa-left"></span>\n        <span class="sa-line sa-right"></span>\n      </span>\n    </div><div class="sa-icon sa-warning">\n      <span class="sa-body"></span>\n      <span class="sa-dot"></span>\n    </div><div class="sa-icon sa-info"></div><div class="sa-icon sa-success">\n      <span class="sa-line sa-tip"></span>\n      <span class="sa-line sa-long"></span>\n\n      <div class="sa-placeholder"></div>\n      <div class="sa-fix"></div>\n    </div><div class="sa-icon sa-custom"></div><h2>Title</h2>\n    <p>Text</p>\n    <fieldset>\n      <input type="text" tabIndex="3" />\n      <div class="sa-input-error"></div>\n    </fieldset><div class="sa-error-container">\n      <div class="icon">!</div>\n      <p>Not valid!</p>\n    </div><div class="sa-button-container">\n      <button class="cancel" tabIndex="2">Cancel</button>\n      <div class="sa-confirm-button-container">\n        <button class="confirm" tabIndex="1">OK</button><div class="la-ball-fall">\n          <div></div>\n          <div></div>\n          <div></div>\n        </div>\n      </div>\n    </div></div>',t.exports=n.default},{}],8:[function(e,t,r){Object.defineProperty(r,"__esModule",{value:!0});var o=e("./utils"),i=e("./handle-swal-dom"),a=e("./handle-dom"),s=["error","warning","info","success","input","prompt"],l=function(e){var t=i.getModal(),r=t.querySelector("h2"),l=t.querySelector("p"),u=t.querySelector("button.cancel"),c=t.querySelector("button.confirm");if(r.innerHTML=e.html?e.title:a.escapeHtml(e.title).split("\n").join("<br>"),l.innerHTML=e.html?e.text:a.escapeHtml(e.text||"").split("\n").join("<br>"),e.text&&a.show(l),e.customClass)a.addClass(t,e.customClass),t.setAttribute("data-custom-class",e.customClass);else{var d=t.getAttribute("data-custom-class");a.removeClass(t,d),t.setAttribute("data-custom-class","")}if(a.hide(t.querySelectorAll(".sa-icon")),e.type&&!o.isIE8()){var f=function(){for(var r=!1,o=0;o<s.length;o++)if(e.type===s[o]){r=!0;break}if(!r)return logStr("Unknown alert type: "+e.type),{v:!1};var l=["success","error","warning","info"],u=n;-1!==l.indexOf(e.type)&&(u=t.querySelector(".sa-icon.sa-"+e.type),a.show(u));var c=i.getInput();switch(e.type){case"success":a.addClass(u,"animate"),a.addClass(u.querySelector(".sa-tip"),"animateSuccessTip"),a.addClass(u.querySelector(".sa-long"),"animateSuccessLong");break;case"error":a.addClass(u,"animateErrorIcon"),a.addClass(u.querySelector(".sa-x-mark"),"animateXMark");break;case"warning":a.addClass(u,"pulseWarning"),a.addClass(u.querySelector(".sa-body"),"pulseWarningIns"),a.addClass(u.querySelector(".sa-dot"),"pulseWarningIns");break;case"input":case"prompt":c.setAttribute("type",e.inputType),c.value=e.inputValue,c.setAttribute("placeholder",e.inputPlaceholder),a.addClass(t,"show-input"),setTimeout(function(){c.focus(),c.addEventListener("keyup",swal.resetInputError)},400)}}();if("object"==typeof f)return f.v}if(e.imageUrl){var p=t.querySelector(".sa-icon.sa-custom");p.style.backgroundImage="url("+e.imageUrl+")",a.show(p);var h=80,g=80;if(e.imageSize){var m=e.imageSize.toString().split("x"),v=m[0],y=m[1];v&&y?(h=v,g=y):logStr("Parameter imageSize expects value with format WIDTHxHEIGHT, got "+e.imageSize)}p.setAttribute("style",p.getAttribute("style")+"width:"+h+"px; height:"+g+"px")}t.setAttribute("data-has-cancel-button",e.showCancelButton),e.showCancelButton?u.style.display="inline-block":a.hide(u),t.setAttribute("data-has-confirm-button",e.showConfirmButton),e.showConfirmButton?c.style.display="inline-block":a.hide(c),e.cancelButtonText&&(u.innerHTML=a.escapeHtml(e.cancelButtonText)),e.confirmButtonText&&(c.innerHTML=a.escapeHtml(e.confirmButtonText)),e.confirmButtonColor&&(c.style.backgroundColor=e.confirmButtonColor,c.style.borderLeftColor=e.confirmLoadingButtonColor,c.style.borderRightColor=e.confirmLoadingButtonColor,i.setFocusStyle(c,e.confirmButtonColor)),t.setAttribute("data-allow-outside-click",e.allowOutsideClick);var b=!!e.doneFunction;t.setAttribute("data-has-done-function",b),e.animation?"string"==typeof e.animation?t.setAttribute("data-animation",e.animation):t.setAttribute("data-animation","pop"):t.setAttribute("data-animation","none"),t.setAttribute("data-timer",e.timer)};r.default=l,t.exports=r.default},{"./handle-dom":4,"./handle-swal-dom":6,"./utils":9}],9:[function(t,n,r){Object.defineProperty(r,"__esModule",{value:!0});var o=function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);return e},i=function(e){var t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);return t?parseInt(t[1],16)+", "+parseInt(t[2],16)+", "+parseInt(t[3],16):null},a=function(){return e.attachEvent&&!e.addEventListener},s=function(t){e.console&&e.console.log("SweetAlert: "+t)},l=function(e,t){e=String(e).replace(/[^0-9a-f]/gi,""),e.length<6&&(e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]),t=t||0;var n,r,o="#";for(r=0;3>r;r++)n=parseInt(e.substr(2*r,2),16),n=Math.round(Math.min(Math.max(0,n+n*t),255)).toString(16),o+=("00"+n).substr(n.length);return o};r.extend=o,r.hexToRgb=i,r.isIE8=a,r.logStr=s,r.colorLuminance=l},{}]},{},[1]),"function"==typeof define&&define.amd?define(function(){return sweetAlert}):"undefined"!=typeof module&&module.exports&&(module.exports=sweetAlert)}(window,document),function(e){"use strict";var t='[data-toggle="context"]',n=function(t,n){this.$element=e(t),this.before=n.before||this.before,this.onItem=n.onItem||this.onItem,this.scopes=n.scopes||null,n.target&&this.$element.data("target",n.target),this.listen()};n.prototype={constructor:n,show:function(t){var n,r,o,i={relatedTarget:this,target:t.currentTarget};if(!this.isDisabled()&&(this.closemenu(),!1!==this.before.call(this,t,e(t.currentTarget))))return n=this.getMenu(),n.trigger(e.Event("show.bs.context",i)),r=this.getPosition(t,n),o="li:not(.divider)",n.attr("style","").css(r).addClass("open").on("click.context.data-api",o,e.proxy(this.onItem,this,e(t.currentTarget))).trigger("shown.bs.context",i),e("html").on("click.context.data-api",n.selector,e.proxy(this.closemenu,this)),!1},closemenu:function(t){var n,r,o;n=this.getMenu(),n.hasClass("open")&&(o={relatedTarget:this},n.trigger(e.Event("hide.bs.context",o)),r="li:not(.divider)",n.removeClass("open").off("click.context.data-api",r).trigger("hidden.bs.context",o),e("html").off("click.context.data-api",n.selector),t&&t.stopPropagation())},keydown:function(e){27==e.which&&this.closemenu(e)},before:function(e){return!0},onItem:function(e){return!0},listen:function(){this.$element.on("contextmenu.context.data-api",this.scopes,e.proxy(this.show,this)),e("html").on("click.context.data-api",e.proxy(this.closemenu,this)),e("html").on("keydown.context.data-api",e.proxy(this.keydown,this))},destroy:function(){this.$element.off(".context.data-api").removeData("context"),e("html").off(".context.data-api")},isDisabled:function(){return this.$element.hasClass("disabled")||this.$element.attr("disabled")},getMenu:function(){var t,n=this.$element.data("target");return n||(n=this.$element.attr("href"),n=n&&n.replace(/.*(?=#[^\s]*$)/,"")),t=e(n),t&&t.length?t:this.$element.find(n)},getPosition:function(t,n){var r,o,i,a=t.clientX,s=t.clientY,l=e(window).width(),u=e(window).height(),c=n.find(".dropdown-menu").outerWidth(),d=n.find(".dropdown-menu").outerHeight(),f={position:"absolute","z-index":9999};return r=s+d>u?{top:s-d+e(window).scrollTop()}:{top:s+e(window).scrollTop()},o=a+c>l&&a-c>0?{left:a-c+e(window).scrollLeft()}:{left:a+e(window).scrollLeft()},i=n.offsetParent().offset(),o.left=o.left-i.left,r.top=r.top-i.top,e.extend(f,r,o)}},e.fn.contextmenu=function(t,r){return this.each(function(){var o=e(this),i=o.data("context"),a="object"==typeof t&&t;i||o.data("context",i=new n(o,a)),"string"==typeof t&&i[t].call(i,r)})},e.fn.contextmenu.Constructor=n,e(document).on("contextmenu.context.data-api",function(){e(t).each(function(){var t=e(this).data("context");t&&t.closemenu()})}).on("contextmenu.context.data-api",t,function(t){e(this).contextmenu("show",t),t.preventDefault(),t.stopPropagation()})}(jQuery);/*
 * @version    1.4.0
 * @date       2015-10-26
 * @stability  3 - Stable
 * <AUTHOR> Rooden (https://github.com/litejs/natural-compare-lite)
 * @license    MIT License
 */
var naturalCompare=function(e,t){function n(e,t,o){if(o){for(r=t;(o=n(e,r))<76&&o>65;)++r;return+e.slice(t-1,r)}return o=l&&l.indexOf(e.charAt(t)),o>-1?o+76:(o=e.charCodeAt(t)||0)<45||o>127?o:o<46?65:o<48?o-1:o<58?o+18:o<65?o-11:o<91?o+11:o<97?o-37:o<123?o+5:o-63}var r,o,i=1,a=0,s=0,l=String.alphabet;if((e+="")!=(t+=""))for(;i;)if(o=n(e,a++),i=n(t,s++),o<76&&i<76&&o>66&&i>66&&(o=n(e,a,a),i=n(t,s,a=r),s=r),o!=i)return o<i?-1:1;return 0};try{module.exports=naturalCompare}catch(e){String.naturalCompare=naturalCompare}/*!
 * angular-translate - v2.18.1 - 2018-05-19
 * 
 * Copyright (c) 2018 The angular-translate team, Pascal Precht; Licensed MIT
 */
!function(e,t){"function"==typeof define&&define.amd?define([],function(){return t()}):"object"==typeof module&&module.exports?module.exports=t():t()}(0,function(){function e(e){"use strict";var t=e.storageKey(),n=e.storage(),r=function(){var r=e.preferredLanguage();angular.isString(r)?e.use(r):n.put(t,e.use())};r.displayName="fallbackFromIncorrectStorageValue",n?n.get(t)?e.use(n.get(t)).catch(r):r():angular.isString(e.preferredLanguage())&&e.use(e.preferredLanguage())}function t(e,t,n,r){"use strict";var o,i,a,s,l,u,c,d,f,p,h,g,m,v,y,b,w={},C=[],S=e,x=[],T="translate-cloak",k=!1,$=!1,E=".",A=!1,O=!1,I=0,B=!0,M="default",L={default:function(e){return(e||"").split("-").join("_")},java:function(e){var t=(e||"").split("-").join("_"),n=t.split("_");return 1<n.length?n[0].toLowerCase()+"_"+n[1].toUpperCase():t},bcp47:function(e){var t=(e||"").split("_").join("-"),n=t.split("-");switch(n.length){case 1:n[0]=n[0].toLowerCase();break;case 2:n[0]=n[0].toLowerCase(),4===n[1].length?n[1]=n[1].charAt(0).toUpperCase()+n[1].slice(1).toLowerCase():n[1]=n[1].toUpperCase();break;case 3:n[0]=n[0].toLowerCase(),n[1]=n[1].charAt(0).toUpperCase()+n[1].slice(1).toLowerCase(),n[2]=n[2].toUpperCase();break;default:return t}return n.join("-")},"iso639-1":function(e){return(e||"").split("_").join("-").split("-")[0].toLowerCase()}},P=function(){if(angular.isFunction(r.getLocale))return r.getLocale();var e,n,o=t.$get().navigator,i=["language","browserLanguage","systemLanguage","userLanguage"];if(angular.isArray(o.languages))for(e=0;e<o.languages.length;e++)if((n=o.languages[e])&&n.length)return n;for(e=0;e<i.length;e++)if((n=o[i[e]])&&n.length)return n;return null};P.displayName="angular-translate/service: getFirstBrowserLanguage";var N=function(){var e=P()||"";return L[M]&&(e=L[M](e)),e};N.displayName="angular-translate/service: getLocale";var D=function(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1},_=function(){return this.toString().replace(/^\s+|\s+$/g,"")},j=function(e){return angular.isString(e)?e.toLowerCase():e},R=function(e){if(e){for(var t,n=[],r=j(e),o=0,a=C.length;o<a;o++)n.push(j(C[o]));if(-1<(o=D(n,r)))return C[o];if(i)for(var s in i)if(i.hasOwnProperty(s)){var l=!1,u=Object.prototype.hasOwnProperty.call(i,s)&&j(s)===j(e);if("*"===s.slice(-1)&&(l=j(s.slice(0,-1))===j(e.slice(0,s.length-1))),(u||l)&&(t=i[s],-1<D(n,j(t))))return t}var c=e.split("_");return 1<c.length&&-1<D(n,j(c[0]))?c[0]:void 0}},F=function(e,t){if(!e&&!t)return w;if(e&&!t){if(angular.isString(e))return w[e]}else angular.isObject(w[e])||(w[e]={}),angular.extend(w[e],z(t));return this};this.translations=F,this.cloakClassName=function(e){return e?(T=e,this):T},this.nestedObjectDelimeter=function(e){return e?(E=e,this):E};var z=function(e,t,n,r){var o,i,a;for(o in t||(t=[]),n||(n={}),e)Object.prototype.hasOwnProperty.call(e,o)&&(a=e[o],angular.isObject(a)?z(a,t.concat(o),n,o):(i=t.length?""+t.join(E)+E+o:o,t.length&&o===r&&(n[""+t.join(E)]="@:"+i),n[i]=a));return n};z.displayName="flatObject",this.addInterpolation=function(e){return x.push(e),this},this.useMessageFormatInterpolation=function(){return this.useInterpolation("$translateMessageFormatInterpolation")},this.useInterpolation=function(e){return p=e,this},this.useSanitizeValueStrategy=function(e){return n.useStrategy(e),this},this.preferredLanguage=function(e){return e?(U(e),this):o};var U=function(e){return e&&(o=e),o};this.translationNotFoundIndicator=function(e){return this.translationNotFoundIndicatorLeft(e),this.translationNotFoundIndicatorRight(e),this},this.translationNotFoundIndicatorLeft=function(e){return e?(m=e,this):m},this.translationNotFoundIndicatorRight=function(e){return e?(v=e,this):v},this.fallbackLanguage=function(e){return q(e),this};var q=function(e){return e?(angular.isString(e)?(s=!0,a=[e]):angular.isArray(e)&&(s=!1,a=e),angular.isString(o)&&D(a,o)<0&&a.push(o),this):s?a[0]:a};this.use=function(e){if(e){if(!w[e]&&!h)throw new Error("$translateProvider couldn't find translationTable for langKey: '"+e+"'");return l=e,this}return l},this.resolveClientLocale=function(){return N()};var V=function(e){return e?(S=e,this):d?d+S:S};this.storageKey=V,this.useUrlLoader=function(e,t){return this.useLoader("$translateUrlLoader",angular.extend({url:e},t))},this.useStaticFilesLoader=function(e){return this.useLoader("$translateStaticFilesLoader",e)},this.useLoader=function(e,t){return h=e,g=t||{},this},this.useLocalStorage=function(){return this.useStorage("$translateLocalStorage")},this.useCookieStorage=function(){return this.useStorage("$translateCookieStorage")},this.useStorage=function(e){return c=e,this},this.storagePrefix=function(e){return e?(d=e,this):e},this.useMissingTranslationHandlerLog=function(){return this.useMissingTranslationHandler("$translateMissingTranslationHandlerLog")},this.useMissingTranslationHandler=function(e){return f=e,this},this.usePostCompiling=function(e){return k=!!e,this},this.forceAsyncReload=function(e){return $=!!e,this},this.uniformLanguageTag=function(e){return e?angular.isString(e)&&(e={standard:e}):e={},M=e.standard,this},this.determinePreferredLanguage=function(e){var t=e&&angular.isFunction(e)?e():N();return o=C.length&&R(t)||t,this},this.registerAvailableLanguageKeys=function(e,t){return e?(C=e,t&&(i=t),this):C},this.useLoaderCache=function(e){return!1===e?y=void 0:!0===e?y=!0:void 0===e?y="$translationCache":e&&(y=e),this},this.directivePriority=function(e){return void 0===e?I:(I=e,this)},this.statefulFilter=function(e){return void 0===e?B:(B=e,this)},this.postProcess=function(e){return b=e||void 0,this},this.keepContent=function(e){return O=!!e,this},this.$get=["$log","$injector","$rootScope","$q",function(e,t,n,r){var i,d,M,L=t.get(p||"$translateDefaultInterpolation"),P=!1,j={},H={},W=function(e,t,n,s,u,f){!l&&o&&(l=o);var p=u&&u!==l?R(u)||u:l;if(u&&ie(u),angular.isArray(e))return function(e){for(var o={},i=[],a=0,l=e.length;a<l;a++)i.push(function(e){var i=r.defer(),a=function(t){o[e]=t,i.resolve([e,t])};return W(e,t,n,s,u,f).then(a,a),i.promise}(e[a]));return r.all(i).then(function(){return o})}(e);var h=r.defer();e&&(e=_.apply(e));var g=function(){var e=H[p]||H[o];if(d=0,c&&!e){var t=i.get(S);if(e=H[t],a&&a.length){var n=D(a,t);d=0===n?1:0,D(a,o)<0&&a.push(o)}}return e}();if(g){var m=function(){u||(p=l),ne(e,t,n,s,p,f).then(h.resolve,h.reject)};m.displayName="promiseResolved",g.finally(m).catch(angular.noop)}else ne(e,t,n,s,p,f).then(h.resolve,h.reject);return h.promise},X=function(e){return m&&(e=[m,e].join(" ")),v&&(e=[e,v].join(" ")),e},G=function(e){l=e,c&&i.put(W.storageKey(),l),n.$emit("$translateChangeSuccess",{language:e}),L.setLocale(l);var t=function(e,t){j[t].setLocale(l)};t.displayName="eachInterpolatorLocaleSetter",angular.forEach(j,t),n.$emit("$translateChangeEnd",{language:e})},K=function(e){if(!e)throw"No language key specified for loading.";var o=r.defer();n.$emit("$translateLoadingStart",{language:e}),P=!0;var i=y;"string"==typeof i&&(i=t.get(i));var a=angular.extend({},g,{key:e,$http:angular.extend({},{cache:i},g.$http)}),s=function(t){var r={};n.$emit("$translateLoadingSuccess",{language:e}),angular.isArray(t)?angular.forEach(t,function(e){angular.extend(r,z(e))}):angular.extend(r,z(t)),P=!1,o.resolve({key:e,table:r}),n.$emit("$translateLoadingEnd",{language:e})};s.displayName="onLoaderSuccess";var l=function(e){n.$emit("$translateLoadingError",{language:e}),o.reject(e),n.$emit("$translateLoadingEnd",{language:e})};return l.displayName="onLoaderError",t.get(h)(a).then(s,l),o.promise};if(c&&(!(i=t.get(c)).get||!i.put))throw new Error("Couldn't use storage '"+c+"', missing get() or put() method!");if(x.length){var Y=function(e){var n=t.get(e);n.setLocale(o||l),j[n.getInterpolationIdentifier()]=n};Y.displayName="interpolationFactoryAdder",angular.forEach(x,Y)}var Q=function(e,t,n,o,i){var a=r.defer(),s=function(r){if(Object.prototype.hasOwnProperty.call(r,t)&&null!==r[t]){o.setLocale(e);var s=r[t];if("@:"===s.substr(0,2))Q(e,s.substr(2),n,o,i).then(a.resolve,a.reject);else{var u=o.interpolate(r[t],n,"service",i,t);u=oe(t,r[t],u,n,e),a.resolve(u)}o.setLocale(l)}else a.reject()};return s.displayName="fallbackTranslationResolver",function(e){var t=r.defer();if(Object.prototype.hasOwnProperty.call(w,e))t.resolve(w[e]);else if(H[e]){var n=function(e){F(e.key,e.table),t.resolve(e.table)};n.displayName="translationTableResolver",H[e].then(n,t.reject)}else t.reject();return t.promise}(e).then(s,a.reject),a.promise},Z=function(e,t,n,r,o){var i,a=w[e];if(a&&Object.prototype.hasOwnProperty.call(a,t)&&null!==a[t]){if(r.setLocale(e),i=r.interpolate(a[t],n,"filter",o,t),i=oe(t,a[t],i,n,e,o),!angular.isString(i)&&angular.isFunction(i.$$unwrapTrustedValue)){var s=i.$$unwrapTrustedValue();if("@:"===s.substr(0,2))return Z(e,s.substr(2),n,r,o)}else if("@:"===i.substr(0,2))return Z(e,i.substr(2),n,r,o);r.setLocale(l)}return i},J=function(e,n,r,o){return f?t.get(f)(e,l,n,r,o):e},ee=function(e,t,n,o,i,s){var l=r.defer();if(e<a.length){var u=a[e];Q(u,t,n,o,s).then(function(e){l.resolve(e)},function(){return ee(e+1,t,n,o,i,s).then(l.resolve,l.reject)})}else if(i)l.resolve(i);else{var c=J(t,n,i);f&&c?l.resolve(c):l.reject(X(t))}return l.promise},te=function(e,t,n,r,o){var i;if(e<a.length){var s=a[e];(i=Z(s,t,n,r,o))||""===i||(i=te(e+1,t,n,r))}return i},ne=function(e,t,n,o,i,s){var l,u,c,p,h,g=r.defer(),m=i?w[i]:w,v=n?j[n]:L;if(m&&Object.prototype.hasOwnProperty.call(m,e)&&null!==m[e]){var y=m[e];if("@:"===y.substr(0,2))W(y.substr(2),t,n,o,i,s).then(g.resolve,g.reject);else{var b=v.interpolate(y,t,"service",s,e);b=oe(e,y,b,t,i),g.resolve(b)}}else{var C;f&&!P&&(C=J(e,t,o)),i&&a&&a.length?(l=e,u=t,c=v,p=o,h=s,ee(0<M?M:d,l,u,c,p,h)).then(function(e){g.resolve(e)},function(e){g.reject(X(e))}):f&&!P&&C?o?g.resolve(o):g.resolve(C):o?g.resolve(o):g.reject(X(e))}return g.promise},re=function(e,t,n,r,o){var i,s=r?w[r]:w,l=L;if(j&&Object.prototype.hasOwnProperty.call(j,n)&&(l=j[n]),s&&Object.prototype.hasOwnProperty.call(s,e)&&null!==s[e]){var u=s[e];"@:"===u.substr(0,2)?i=re(u.substr(2),t,n,r,o):(i=l.interpolate(u,t,"filter",o,e),i=oe(e,u,i,t,r,o))}else{var c;f&&!P&&(c=J(e,t,o)),i=r&&a&&a.length?te((d=0)<M?M:d,e,t,l,o):f&&!P&&c?c:X(e)}return i},oe=function(e,n,r,o,i,a){var s=b;return s&&("string"==typeof s&&(s=t.get(s)),s)?s(e,n,r,o,i,a):r},ie=function(e){w[e]||!h||H[e]||(H[e]=K(e).then(function(e){return F(e.key,e.table),e}))};W.preferredLanguage=function(e){return e&&U(e),o},W.cloakClassName=function(){return T},W.nestedObjectDelimeter=function(){return E},W.fallbackLanguage=function(e){if(null!=e){if(q(e),h&&a&&a.length)for(var t=0,n=a.length;t<n;t++)H[a[t]]||(H[a[t]]=K(a[t]));W.use(W.use())}return s?a[0]:a},W.useFallbackLanguage=function(e){if(null!=e)if(e){var t=D(a,e);-1<t&&(M=t)}else M=0},W.proposedLanguage=function(){return u},W.storage=function(){return i},W.negotiateLocale=R,W.use=function(e){if(!e)return l;var t=r.defer();t.promise.then(null,angular.noop),n.$emit("$translateChangeStart",{language:e});var o=R(e);return 0<C.length&&!o?r.reject(e):(o&&(e=o),u=e,!$&&w[e]||!h||H[e]?H[e]?H[e].then(function(e){return u===e.key&&G(e.key),t.resolve(e.key),e},function(e){return!l&&a&&0<a.length&&a[0]!==e?W.use(a[0]).then(t.resolve,t.reject):t.reject(e)}):(t.resolve(e),G(e)):(H[e]=K(e).then(function(n){return F(n.key,n.table),t.resolve(n.key),u===e&&G(n.key),n},function(e){return n.$emit("$translateChangeError",{language:e}),t.reject(e),n.$emit("$translateChangeEnd",{language:e}),r.reject(e)}),H[e].finally(function(){var t;u===(t=e)&&(u=void 0),H[t]=void 0}).catch(angular.noop)),t.promise)},W.resolveClientLocale=function(){return N()},W.storageKey=function(){return V()},W.isPostCompilingEnabled=function(){return k},W.isForceAsyncReloadEnabled=function(){return $},W.isKeepContent=function(){return O},W.refresh=function(e){function t(e){var t=K(e);return(H[e]=t).then(function(t){w[e]={},F(e,t.table),i[e]=!0},angular.noop),t}if(!h)throw new Error("Couldn't refresh translation table, no loader registered!");n.$emit("$translateRefreshStart",{language:e});var o=r.defer(),i={};if(o.promise.then(function(){for(var e in w)w.hasOwnProperty(e)&&(e in i||delete w[e]);l&&G(l)},angular.noop).finally(function(){n.$emit("$translateRefreshEnd",{language:e})}),e)w[e]?t(e).then(o.resolve,o.reject):o.reject();else{var s=a&&a.slice()||[];l&&-1===s.indexOf(l)&&s.push(l),r.all(s.map(t)).then(o.resolve,o.reject)}return o.promise},W.instant=function(e,t,n,r,i){var s=r&&r!==l?R(r)||r:l;if(null===e||angular.isUndefined(e))return e;if(r&&ie(r),angular.isArray(e)){for(var u={},c=0,d=e.length;c<d;c++)u[e[c]]=W.instant(e[c],t,n,r,i);return u}if(angular.isString(e)&&e.length<1)return e;e&&(e=_.apply(e));var p,h,g=[];o&&g.push(o),s&&g.push(s),a&&a.length&&(g=g.concat(a));for(var y=0,b=g.length;y<b;y++){var C=g[y];if(w[C]&&void 0!==w[C][e]&&(p=re(e,t,n,s,i)),void 0!==p)break}return p||""===p||(m||v?p=X(e):(p=L.interpolate(e,t,"filter",i),f&&!P&&(h=J(e,t,i)),f&&!P&&h&&(p=h))),p},W.versionInfo=function(){return"2.18.1"},W.loaderCache=function(){return y},W.directivePriority=function(){return I},W.statefulFilter=function(){return B},W.isReady=function(){return A};var ae=r.defer();ae.promise.then(function(){A=!0}),W.onReady=function(e){var t=r.defer();return angular.isFunction(e)&&t.promise.then(e),A?t.resolve():ae.promise.then(t.resolve),t.promise},W.getAvailableLanguageKeys=function(){return 0<C.length?C:null},W.getTranslationTable=function(e){return(e=e||W.use())&&w[e]?angular.copy(w[e]):null};var se=n.$on("$translateReady",function(){ae.resolve(),se(),se=null}),le=n.$on("$translateChangeEnd",function(){ae.resolve(),le(),le=null});if(h){if(angular.equals(w,{})&&W.use()&&W.use(W.use()),a&&a.length)for(var ue=function(e){return F(e.key,e.table),n.$emit("$translateChangeEnd",{language:e.key}),e},ce=0,de=a.length;ce<de;ce++){var fe=a[ce];!$&&w[fe]||(H[fe]=K(fe).then(ue))}}else n.$emit("$translateReady",{language:W.use()});return W}]}function n(e,t){"use strict";var n={};return n.setLocale=function(e){},n.getInterpolationIdentifier=function(){return"default"},n.useSanitizeValueStrategy=function(e){return t.useStrategy(e),this},n.interpolate=function(n,r,o,i,a){var s;return r=r||{},r=t.sanitize(r,"params",i,o),angular.isNumber(n)?s=""+n:angular.isString(n)?(s=e(n)(r),s=t.sanitize(s,"text",i,o)):s="",s},n}function r(e,t,n,r,o){"use strict";var i=function(e){return angular.isString(e)?e.toLowerCase():e};return{restrict:"AE",scope:!0,priority:e.directivePriority(),compile:function(a,s){var l=s.translateValues?s.translateValues:void 0,u=s.translateInterpolation?s.translateInterpolation:void 0,c=s.translateSanitizeStrategy?s.translateSanitizeStrategy:void 0,d=a[0].outerHTML.match(/translate-value-+/i),f="^(.*)("+t.startSymbol()+".*"+t.endSymbol()+")(.*)",p="^(.*)"+t.startSymbol()+"(.*)"+t.endSymbol()+"(.*)";return function(a,h,g){a.interpolateParams={},a.preText="",a.postText="",a.translateNamespace=function e(t){return t.translateNamespace?t.translateNamespace:t.$parent?e(t.$parent):void 0}(a);var m={},v=function(e){if(angular.isFunction(v._unwatchOld)&&(v._unwatchOld(),v._unwatchOld=void 0),angular.equals(e,"")||!angular.isDefined(e)){var n=function(){return this.toString().replace(/^\s+|\s+$/g,"")}.apply(h.text()),r=n.match(f);if(angular.isArray(r)){a.preText=r[1],a.postText=r[3],m.translate=t(r[2])(a.$parent);var o=n.match(p);angular.isArray(o)&&o[2]&&o[2].length&&(v._unwatchOld=a.$watch(o[2],function(e){m.translate=e,C()}))}else m.translate=n||void 0}else m.translate=e;C()};!function(e,t,n){if(t.translateValues&&angular.extend(e,r(t.translateValues)(a.$parent)),d)for(var o in n)Object.prototype.hasOwnProperty.call(t,o)&&"translateValue"===o.substr(0,14)&&"translateValues"!==o&&(e[i(o.substr(14,1))+o.substr(15)]=n[o])}(a.interpolateParams,g,s);var y=!0;for(var b in g.$observe("translate",function(e){void 0===e?v(""):""===e&&y||(m.translate=e,C()),y=!1}),g)g.hasOwnProperty(b)&&"translateAttr"===b.substr(0,13)&&13<b.length&&function(e){g.$observe(e,function(t){m[e]=t,C()})}(b);if(g.$observe("translateDefault",function(e){a.defaultText=e,C()}),c&&g.$observe("translateSanitizeStrategy",function(e){a.sanitizeStrategy=r(e)(a.$parent),C()}),l&&g.$observe("translateValues",function(e){e&&a.$parent.$watch(function(){angular.extend(a.interpolateParams,r(e)(a.$parent))})}),d){for(var w in g)Object.prototype.hasOwnProperty.call(g,w)&&"translateValue"===w.substr(0,14)&&"translateValues"!==w&&function(e){g.$observe(e,function(t){var n=i(e.substr(14,1))+e.substr(15);a.interpolateParams[n]=t})}(w)}var C=function(){for(var e in m)m.hasOwnProperty(e)&&void 0!==m[e]&&S(e,m[e],a,a.interpolateParams,a.defaultText,a.translateNamespace)},S=function(t,n,r,o,i,a){n?(a&&"."===n.charAt(0)&&(n=a+n),e(n,o,u,i,r.translateLanguage,r.sanitizeStrategy).then(function(e){x(e,r,!0,t)},function(e){x(e,r,!1,t)})):x(n,r,!1,t)},x=function(t,r,o,i){if(o||void 0!==r.defaultText&&(t=r.defaultText),"translate"===i){(o||!o&&!e.isKeepContent()&&void 0===g.translateKeepContent)&&h.empty().append(r.preText+t+r.postText);var a=e.isPostCompilingEnabled(),l=void 0!==s.translateCompile,u=l&&"false"!==s.translateCompile;(a&&!l||u)&&n(h.contents())(r)}else{var c=g.$attr[i];"data-"===c.substr(0,5)&&(c=c.substr(5)),c=c.substr(15),h.attr(c,t)}};(l||d||g.translateDefault)&&a.$watch("interpolateParams",C,!0),a.$on("translateLanguageChanged",C);var T=o.$on("$translateChangeSuccess",C);h.text().length?v(g.translate?g.translate:""):g.translate&&v(g.translate),C(),a.$on("$destroy",T)}}}}function o(e,t){"use strict";return{restrict:"A",priority:e.directivePriority(),link:function(n,r,o){var a,s,l,u={},c=function(){angular.forEach(a,function(t,i){t&&(u[i]=!0,n.translateNamespace&&"."===t.charAt(0)&&(t=n.translateNamespace+t),e(t,s,o.translateInterpolation,void 0,n.translateLanguage,l).then(function(e){r.attr(i,e)},function(e){r.attr(i,e)}))}),angular.forEach(u,function(e,t){a[t]||(r.removeAttr(t),delete u[t])})};i(n,o.translateAttr,function(e){a=e},c),i(n,o.translateValues,function(e){s=e},c),i(n,o.translateSanitizeStrategy,function(e){l=e},c),o.translateValues&&n.$watch(o.translateValues,c,!0),n.$on("translateLanguageChanged",c);var d=t.$on("$translateChangeSuccess",c);c(),n.$on("$destroy",d)}}}function i(e,t,n,r){"use strict";t&&("::"===t.substr(0,2)?t=t.substr(2):e.$watch(t,function(e){n(e),r()},!0),n(e.$eval(t)))}function a(e,t){"use strict";return{compile:function(n){var r=function(t){t.addClass(e.cloakClassName())};return r(n),function(n,o,i){var a=function(t){t.removeClass(e.cloakClassName())}.bind(this,o),s=r.bind(this,o);i.translateCloak&&i.translateCloak.length?(i.$observe("translateCloak",function(t){e(t).then(a,s)}),t.$on("$translateChangeSuccess",function(){e(i.translateCloak).then(a,s)})):e.onReady(a)}}}}function s(){"use strict";return{restrict:"A",scope:!0,compile:function(){return{pre:function(e,t,n){e.translateNamespace=function e(t){return t.translateNamespace?t.translateNamespace:t.$parent?e(t.$parent):void 0}(e),e.translateNamespace&&"."===n.translateNamespace.charAt(0)?e.translateNamespace+=n.translateNamespace:e.translateNamespace=n.translateNamespace}}}}}function l(){"use strict";return{restrict:"A",scope:!0,compile:function(){return function(e,t,n){n.$observe("translateLanguage",function(t){e.translateLanguage=t}),e.$watch("translateLanguage",function(){e.$broadcast("translateLanguageChanged")})}}}}function u(e,t){"use strict";var n=function(n,r,o,i){if(!angular.isObject(r)){var a=this||{__SCOPE_IS_NOT_AVAILABLE:"More info at https://github.com/angular/angular.js/commit/8863b9d04c722b278fa93c5d66ad1e578ad6eb1f"};r=e(r)(a)}return t.instant(n,r,o,i)};return t.statefulFilter()&&(n.$stateful=!0),n}function c(e){"use strict";return e("translations")}return e.$inject=["$translate"],t.$inject=["$STORAGE_KEY","$windowProvider","$translateSanitizationProvider","pascalprechtTranslateOverrider"],n.$inject=["$interpolate","$translateSanitization"],r.$inject=["$translate","$interpolate","$compile","$parse","$rootScope"],o.$inject=["$translate","$rootScope"],a.$inject=["$translate","$rootScope"],u.$inject=["$parse","$translate"],c.$inject=["$cacheFactory"],angular.module("pascalprecht.translate",["ng"]).run(e),e.displayName="runTranslate",angular.module("pascalprecht.translate").provider("$translateSanitization",function(){"use strict";var e,t,n,r=null,o=!1,i=!1;(n={sanitize:function(e,t){return"text"===t&&(e=s(e)),e},escape:function(e,t){return"text"===t&&(e=a(e)),e},sanitizeParameters:function(e,t){return"params"===t&&(e=u(e,s)),e},escapeParameters:function(e,t){return"params"===t&&(e=u(e,a)),e},sce:function(e,t,n){return"text"===t?e=l(e):"params"===t&&"filter"!==n&&(e=u(e,a)),e},sceParameters:function(e,t){return"params"===t&&(e=u(e,l)),e}}).escaped=n.escapeParameters,this.addStrategy=function(e,t){return n[e]=t,this},this.removeStrategy=function(e){return delete n[e],this},this.useStrategy=function(e){return o=!0,r=e,this},this.$get=["$injector","$log",function(a,s){var l,u={};return a.has("$sanitize")&&(e=a.get("$sanitize")),a.has("$sce")&&(t=a.get("$sce")),{useStrategy:(l=this,function(e){l.useStrategy(e)}),sanitize:function(e,t,l,c){if(r||o||i||(s.warn("pascalprecht.translate.$translateSanitization: No sanitization strategy has been configured. This can have serious security implications. See http://angular-translate.github.io/docs/#/guide/19_security for details."),i=!0),l||null===l||(l=r),!l)return e;c||(c="service");var d,f,p,h,g=angular.isArray(l)?l:[l];return d=e,f=t,p=c,h=g,angular.forEach(h,function(e){if(angular.isFunction(e))d=e(d,f,p);else if(angular.isFunction(n[e]))d=n[e](d,f,p);else{if(!angular.isString(n[e]))throw new Error("pascalprecht.translate.$translateSanitization: Unknown sanitization strategy: '"+e+"'");if(!u[n[e]])try{u[n[e]]=a.get(n[e])}catch(t){throw u[n[e]]=function(){},new Error("pascalprecht.translate.$translateSanitization: Unknown sanitization strategy: '"+e+"'")}d=u[n[e]](d,f,p)}}),d}}}];var a=function(e){var t=angular.element("<div></div>");return t.text(e),t.html()},s=function(t){if(!e)throw new Error("pascalprecht.translate.$translateSanitization: Error cannot find $sanitize service. Either include the ngSanitize module (https://docs.angularjs.org/api/ngSanitize) or use a sanitization strategy which does not depend on $sanitize, such as 'escape'.");return e(t)},l=function(e){if(!t)throw new Error("pascalprecht.translate.$translateSanitization: Error cannot find $sce service.");return t.trustAsHtml(e)},u=function(e,t,n){if(angular.isDate(e))return e;if(angular.isObject(e)){var r=angular.isArray(e)?[]:{};if(n){if(-1<n.indexOf(e))throw new Error("pascalprecht.translate.$translateSanitization: Error cannot interpolate parameter due recursive object")}else n=[];return n.push(e),angular.forEach(e,function(e,o){angular.isFunction(e)||(r[o]=u(e,t,n))}),n.splice(-1,1),r}return angular.isNumber(e)?e:!0===e||!1===e?e:angular.isUndefined(e)||null===e?e:t(e)}}),angular.module("pascalprecht.translate").constant("pascalprechtTranslateOverrider",{}).provider("$translate",t),t.displayName="displayName",angular.module("pascalprecht.translate").factory("$translateDefaultInterpolation",n),n.displayName="$translateDefaultInterpolation",angular.module("pascalprecht.translate").constant("$STORAGE_KEY","NG_TRANSLATE_LANG_KEY"),angular.module("pascalprecht.translate").directive("translate",r),r.displayName="translateDirective",angular.module("pascalprecht.translate").directive("translateAttr",o),o.displayName="translateAttrDirective",angular.module("pascalprecht.translate").directive("translateCloak",a),a.displayName="translateCloakDirective",angular.module("pascalprecht.translate").directive("translateNamespace",s),s.displayName="translateNamespaceDirective",angular.module("pascalprecht.translate").directive("translateLanguage",l),l.displayName="translateLanguageDirective",angular.module("pascalprecht.translate").filter("translate",u),u.displayName="translateFilterFactory",angular.module("pascalprecht.translate").factory("$translationCache",c),c.displayName="$translationCache","pascalprecht.translate"}),function(){"use strict";function e(e){return angular.isUndefined(e)||null===e}function t(){try{return require("moment")}catch(e){throw new Error("Please install moment via npm. Please reference to: https://github.com/urish/angular-moment")}}function n(n,r){if(void 0===r){if("function"!=typeof require)throw new Error("Moment cannot be found by angular-moment! Please reference to: https://github.com/urish/angular-moment");r=t()}return n.module("angularMoment",[]).constant("angularMomentConfig",{preprocess:null,timezone:null,format:null,statefulFilters:!0}).constant("moment",r).constant("amTimeAgoConfig",{withoutSuffix:!1,serverTime:null,titleFormat:null,fullDateThreshold:null,fullDateFormat:null,fullDateThresholdUnit:"day"}).directive("amTimeAgo",["$window","moment","amMoment","amTimeAgoConfig",function(t,r,o,i){return function(a,s,l){function u(){var e;if(g)e=g;else if(i.serverTime){var t=(new Date).getTime(),n=t-S+i.serverTime;e=r(n)}else e=r();return e}function c(){m&&(t.clearTimeout(m),m=null)}function d(e){var n=u().diff(e,C),r=b&&n>=b;if(r?s.text(e.format(w)):s.text(e.from(u(),v)),y&&k&&s.attr("title",e.format(y)),!r){var o=Math.abs(u().diff(e,"minute")),i=3600;o<1?i=1:o<60?i=30:o<180&&(i=300),m=t.setTimeout(function(){d(e)},1e3*i)}}function f(e){T&&s.attr("datetime",e)}function p(){if(c(),h){var e=o.preprocessDate(h);d(e),f(e.toISOString())}}var h,g,m=null,v=i.withoutSuffix,y=i.titleFormat,b=i.fullDateThreshold,w=i.fullDateFormat,C=i.fullDateThresholdUnit,S=(new Date).getTime(),x=l.amTimeAgo,T="TIME"===s[0].nodeName.toUpperCase(),k=!s.attr("title");a.$watch(x,function(t){return e(t)||""===t?(c(),void(h&&(s.text(""),f(""),h=null))):(h=t,void p())}),n.isDefined(l.amFrom)&&a.$watch(l.amFrom,function(t){g=e(t)||""===t?null:r(t),p()}),n.isDefined(l.amWithoutSuffix)&&a.$watch(l.amWithoutSuffix,function(e){"boolean"==typeof e?(v=e,p()):v=i.withoutSuffix}),l.$observe("amFullDateThreshold",function(e){b=e,p()}),l.$observe("amFullDateFormat",function(e){w=e,p()}),l.$observe("amFullDateThresholdUnit",function(e){C=e,p()}),a.$on("$destroy",function(){c()}),a.$on("amMoment:localeChanged",function(){p()})}}]).service("amMoment",["moment","$rootScope","$log","angularMomentConfig",function(e,t,r,o){var i=null;this.changeLocale=function(r,o){var i=e.locale(r,o);return n.isDefined(r)&&t.$broadcast("amMoment:localeChanged"),i},this.changeTimezone=function(n){e.tz&&e.tz.setDefault?(e.tz.setDefault(n),t.$broadcast("amMoment:timezoneChanged")):r.warn("angular-moment: changeTimezone() works only with moment-timezone.js v0.3.0 or greater."),o.timezone=n,i=n},this.preprocessDate=function(t){return i!==o.timezone&&this.changeTimezone(o.timezone),o.preprocess?o.preprocess(t):e(!isNaN(parseFloat(t))&&isFinite(t)?parseInt(t,10):t)}}]).filter("amParse",["moment",function(e){return function(t,n){return e(t,n)}}]).filter("amFromUnix",["moment",function(e){return function(t){return e.unix(t)}}]).filter("amUtc",["moment",function(e){return function(t){return e.utc(t)}}]).filter("amUtcOffset",["amMoment",function(e){function t(t,n){return e.preprocessDate(t).utcOffset(n)}return t}]).filter("amLocal",["moment",function(e){return function(t){return e.isMoment(t)?t.local():null}}]).filter("amTimezone",["amMoment","angularMomentConfig","$log",function(e,t,n){function r(t,r){var o=e.preprocessDate(t);return r?o.tz?o.tz(r):(n.warn("angular-moment: named timezone specified but moment.tz() is undefined. Did you forget to include moment-timezone.js ?"),o):o}return r}]).filter("amCalendar",["moment","amMoment","angularMomentConfig",function(t,n,r){function o(t,r,o){if(e(t))return"";var i=n.preprocessDate(t);return i.isValid()?i.calendar(r,o):""}return o.$stateful=r.statefulFilters,o}]).filter("amDifference",["moment","amMoment","angularMomentConfig",function(t,n,r){function o(r,o,i,a){if(e(r))return"";var s=n.preprocessDate(r),l=e(o)?t():n.preprocessDate(o);return s.isValid()&&l.isValid()?s.diff(l,i,a):""}return o.$stateful=r.statefulFilters,o}]).filter("amDateFormat",["moment","amMoment","angularMomentConfig",function(t,n,r){function o(t,r){if(e(t))return"";var o=n.preprocessDate(t);return o.isValid()?o.format(r):""}return o.$stateful=r.statefulFilters,o}]).filter("amDurationFormat",["moment","angularMomentConfig",function(t,n){function r(n,r,o){return e(n)?"":t.duration(n,r).humanize(o)}return r.$stateful=n.statefulFilters,r}]).filter("amTimeAgo",["moment","amMoment","angularMomentConfig",function(t,n,r){function o(r,o,i){var a,s;return e(r)?"":(r=n.preprocessDate(r),a=t(r),a.isValid()?(s=t(i),!e(i)&&s.isValid()?a.from(s,o):a.fromNow(o)):"")}return o.$stateful=r.statefulFilters,o}]).filter("amSubtract",["moment","angularMomentConfig",function(t,n){function r(n,r,o){return e(n)?"":t(n).subtract(parseInt(r,10),o)}return r.$stateful=n.statefulFilters,r}]).filter("amAdd",["moment","angularMomentConfig",function(t,n){function r(n,r,o){return e(n)?"":t(n).add(parseInt(r,10),o)}return r.$stateful=n.statefulFilters,r}]).filter("amStartOf",["moment","angularMomentConfig",function(t,n){function r(n,r){return e(n)?"":t(n).startOf(r)}return r.$stateful=n.statefulFilters,r}]).filter("amEndOf",["moment","angularMomentConfig",function(t,n){function r(n,r){return e(n)?"":t(n).endOf(r)}return r.$stateful=n.statefulFilters,r}]),"angularMoment"}var r=window&&window.process&&window.process.type;"function"==typeof define&&define.amd?define(["angular","moment"],n):"undefined"!=typeof module&&module&&module.exports&&"function"==typeof require&&!r?module.exports=n(require("angular"),require("moment")):n(angular,("undefined"!=typeof global?global:window).moment)}(),function(e,t){if("function"==typeof define&&define.amd)define(["module","exports","angular","ws"],t);else if("undefined"!=typeof exports)t(module,exports,require("angular"),require("ws"));else{var n={exports:{}};t(n,n.exports,e.angular,e.ws),e.angularWebsocket=n.exports}}(this,function(e,t,n,r){"use strict";function o(e,t,n,r){function o(t,n,r){r||!g(n)||m(n)||(r=n,n=void 0),this.protocols=n,this.url=t||"Missing URL",this.ssl=/(wss)/i.test(this.url),this.scope=r&&r.scope||e,this.rootScopeFailover=r&&r.rootScopeFailover&&!0,this.useApplyAsync=r&&r.useApplyAsync||!1,this.initialTimeout=r&&r.initialTimeout||500,this.maxTimeout=r&&r.maxTimeout||3e5,this.reconnectIfNotNormalClose=r&&r.reconnectIfNotNormalClose||!1,this.binaryType=r&&r.binaryType||"blob",this._reconnectAttempts=0,this.sendQueue=[],this.onOpenCallbacks=[],this.onMessageCallbacks=[],this.onErrorCallbacks=[],this.onCloseCallbacks=[],c(this._readyStateConstants),t?this._connect():this._setInternalState(0)}return o.prototype._readyStateConstants={CONNECTING:0,OPEN:1,CLOSING:2,CLOSED:3,RECONNECT_ABORTED:4},o.prototype._normalCloseCode=1e3,o.prototype._reconnectableStatusCodes=[4e3],o.prototype.safeDigest=function(e){e&&!this.scope.$$phase&&this.scope.$digest()},o.prototype.bindToScope=function(t){var n=this;return t&&(this.scope=t,this.rootScopeFailover&&this.scope.$on("$destroy",function(){n.scope=e})),n},o.prototype._connect=function(e){!e&&this.socket&&this.socket.readyState===this._readyStateConstants.OPEN||(this.socket=r.create(this.url,this.protocols),this.socket.onmessage=s.default.bind(this,this._onMessageHandler),this.socket.onopen=s.default.bind(this,this._onOpenHandler),this.socket.onerror=s.default.bind(this,this._onErrorHandler),this.socket.onclose=s.default.bind(this,this._onCloseHandler),this.socket.binaryType=this.binaryType)},o.prototype.fireQueue=function(){for(;this.sendQueue.length&&this.socket.readyState===this._readyStateConstants.OPEN;){var e=this.sendQueue.shift();this.socket.send(f(e.message)||"blob"!=this.binaryType?e.message:JSON.stringify(e.message)),e.deferred.resolve()}},o.prototype.notifyOpenCallbacks=function(e){for(var t=0;t<this.onOpenCallbacks.length;t++)this.onOpenCallbacks[t].call(this,e)},o.prototype.notifyCloseCallbacks=function(e){
for(var t=0;t<this.onCloseCallbacks.length;t++)this.onCloseCallbacks[t].call(this,e)},o.prototype.notifyErrorCallbacks=function(e){for(var t=0;t<this.onErrorCallbacks.length;t++)this.onErrorCallbacks[t].call(this,e)},o.prototype.onOpen=function(e){return this.onOpenCallbacks.push(e),this},o.prototype.onClose=function(e){return this.onCloseCallbacks.push(e),this},o.prototype.onError=function(e){return this.onErrorCallbacks.push(e),this},o.prototype.onMessage=function(e,t){if(!p(e))throw new Error("Callback must be a function");if(t&&h(t.filter)&&!f(t.filter)&&!(t.filter instanceof RegExp))throw new Error("Pattern must be a string or regular expression");return this.onMessageCallbacks.push({fn:e,pattern:t?t.filter:void 0,autoApply:!t||t.autoApply}),this},o.prototype._onOpenHandler=function(e){this._reconnectAttempts=0,this.notifyOpenCallbacks(e),this.fireQueue()},o.prototype._onCloseHandler=function(e){var t=this;t.useApplyAsync?t.scope.$applyAsync(function(){t.notifyCloseCallbacks(e)}):(t.notifyCloseCallbacks(e),t.safeDigest(!0)),(this.reconnectIfNotNormalClose&&e.code!==this._normalCloseCode||this._reconnectableStatusCodes.indexOf(e.code)>-1)&&this.reconnect()},o.prototype._onErrorHandler=function(e){var t=this;t.useApplyAsync?t.scope.$applyAsync(function(){t.notifyErrorCallbacks(e)}):(t.notifyErrorCallbacks(e),t.safeDigest(!0))},o.prototype._onMessageHandler=function(e){function t(e,t,n){n=y.call(arguments,2),o.useApplyAsync?o.scope.$applyAsync(function(){e.apply(o,n)}):(e.apply(o,n),o.safeDigest(t))}for(var n,r,o=this,i=0;i<o.onMessageCallbacks.length;i++)r=o.onMessageCallbacks[i],n=r.pattern,n?f(n)&&e.data===n?t(r.fn,r.autoApply,e):n instanceof RegExp&&n.exec(e.data)&&t(r.fn,r.autoApply,e):t(r.fn,r.autoApply,e)},o.prototype.close=function(e){return!e&&this.socket.bufferedAmount||this.socket.close(),this},o.prototype.send=function(e){function n(e){e.cancel=o;var t=e.then;return e.then=function(){return n(t.apply(this,arguments))},e}function o(t){return a.sendQueue.splice(a.sendQueue.indexOf(e),1),i.reject(t),a}var i=t.defer(),a=this,s=n(i.promise);return a.readyState===a._readyStateConstants.RECONNECT_ABORTED?i.reject("Socket connection has been closed"):(a.sendQueue.push({message:e,deferred:i}),a.fireQueue()),r.isMocked&&r.isMocked()&&r.isConnected(this.url)&&this._onMessageHandler(r.mockSend()),s},o.prototype.reconnect=function(){this.close();var e=this._getBackoffDelay(++this._reconnectAttempts),t=e/1e3;return console.log("Reconnecting in "+t+" seconds"),n(s.default.bind(this,this._connect),e),this},o.prototype._getBackoffDelay=function(e){var t=Math.random()+1,n=this.initialTimeout,r=e,o=this.maxTimeout;return Math.floor(Math.min(t*n*Math.pow(2,r),o))},o.prototype._setInternalState=function(e){if(Math.floor(e)!==e||0>e||e>4)throw new Error("state must be an integer between 0 and 4, got: "+e);d||(this.readyState=e||this.socket.readyState),this._internalConnectionState=e,v(this.sendQueue,function(e){e.deferred.reject("Message cancelled due to closed socket connection")})},d&&d(o.prototype,"readyState",{get:function(){return this._internalConnectionState||this.socket.readyState},set:function(){throw new Error("The readyState property is read-only")}}),function(e,t,n){return new o(e,t,n)}}function i(e){this.create=function(e,t){if(!/wss?:\/\//.exec(e))throw new Error("Invalid url provided");return t?new a(e,t):new a(e)},this.createWebSocketBackend=function(t,n){return e.warn("Deprecated: Please use .create(url, protocols)"),this.create(t,n)}}Object.defineProperty(t,"__esModule",{value:!0});var a,s=function(e){return e&&e.__esModule?e:{default:e}}(n),l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e};if("object"===(void 0===t?"undefined":l(t))&&"function"==typeof require)try{a=r.Client||r.client||r}catch(e){}a=a||window.WebSocket||window.MozWebSocket;var u=s.default.noop,c=Object.freeze?Object.freeze:u,d=Object.defineProperty,f=s.default.isString,p=s.default.isFunction,h=s.default.isDefined,g=s.default.isObject,m=s.default.isArray,v=s.default.forEach,y=Array.prototype.slice;Array.prototype.indexOf||(Array.prototype.indexOf=function(e){var t=this.length>>>0,n=Number(arguments[1])||0;for(0>(n=0>n?Math.ceil(n):Math.floor(n))&&(n+=t);t>n;n++)if(n in this&&this[n]===e)return n;return-1}),s.default.module("ngWebSocket",[]).factory("$websocket",["$rootScope","$q","$timeout","$websocketBackend",o]).factory("WebSocket",["$rootScope","$q","$timeout","WebsocketBackend",o]).service("$websocketBackend",["$log",i]).service("WebSocketBackend",["$log",i]),s.default.module("angular-websocket",["ngWebSocket"]),t.default=s.default.module("ngWebSocket"),e.exports=t.default}),"undefined"!=typeof module&&module.exports?module.exports=new Base64:Base64(),function(e,t){var n=t.isDefined,r=t.isUndefined,o=t.isNumber,i=t.isObject,a=t.isArray,s=t.isString,l=t.extend,u=t.toJson;t.module("LocalStorageModule",[]).provider("localStorageService",function(){this.prefix="ls",this.storageType="localStorage",this.cookie={expiry:30,path:"/",secure:!1},this.defaultToCookie=!0,this.notify={setItem:!0,removeItem:!1},this.setPrefix=function(e){return this.prefix=e,this},this.setStorageType=function(e){return this.storageType=e,this},this.setDefaultToCookie=function(e){return this.defaultToCookie=!!e,this},this.setStorageCookie=function(e,t,n){return this.cookie.expiry=e,this.cookie.path=t,this.cookie.secure=n,this},this.setStorageCookieDomain=function(e){return this.cookie.domain=e,this},this.setNotify=function(e,t){return this.notify={setItem:e,removeItem:t},this},this.$get=["$rootScope","$window","$document","$parse","$timeout",function(e,t,c,d,f){function p(n){if(n||(n=t.event),y.setItem&&s(n.key)&&S(n.key)){var r=C(n.key);f(function(){e.$broadcast("LocalStorageModule.notification.changed",{key:r,newvalue:n.newValue,storageType:g.storageType})})}}var h,g=this,m=g.prefix,v=g.cookie,y=g.notify,b=g.storageType;c?c[0]&&(c=c[0]):c=document,"."!==m.substr(-1)&&(m=m?m+".":"");var w=function(e){return m+e},C=function(e){return e.replace(new RegExp("^"+m,"g"),"")},S=function(e){return 0===e.indexOf(m)},x=function(){try{var n=b in t&&null!==t[b],r=w("__"+Math.round(1e7*Math.random()));return n&&(h=t[b],h.setItem(r,""),h.removeItem(r)),n}catch(t){return g.defaultToCookie&&(b="cookie"),e.$broadcast("LocalStorageModule.notification.error",t.message),!1}},T=x(),k=function(t,n,o){var i=N();try{if(D(o),n=r(n)?null:u(n),!T&&g.defaultToCookie||"cookie"===g.storageType)return T||e.$broadcast("LocalStorageModule.notification.warning","LOCAL_STORAGE_NOT_SUPPORTED"),y.setItem&&e.$broadcast("LocalStorageModule.notification.setitem",{key:t,newvalue:n,storageType:"cookie"}),B(t,n);try{h&&h.setItem(w(t),n),y.setItem&&e.$broadcast("LocalStorageModule.notification.setitem",{key:t,newvalue:n,storageType:g.storageType})}catch(r){return e.$broadcast("LocalStorageModule.notification.error",r.message),B(t,n)}return!0}finally{D(i)}},$=function(t,n){var r=N();try{if(D(n),!T&&g.defaultToCookie||"cookie"===g.storageType)return T||e.$broadcast("LocalStorageModule.notification.warning","LOCAL_STORAGE_NOT_SUPPORTED"),M(t);var o=h?h.getItem(w(t)):null;if(!o||"null"===o)return null;try{return JSON.parse(o)}catch(e){return o}}finally{D(r)}},E=function(){var t=N();try{var n=0;arguments.length>=1&&("localStorage"===arguments[arguments.length-1]||"sessionStorage"===arguments[arguments.length-1])&&(n=1,D(arguments[arguments.length-1]));var r,o;for(r=0;r<arguments.length-n;r++)if(o=arguments[r],!T&&g.defaultToCookie||"cookie"===g.storageType)T||e.$broadcast("LocalStorageModule.notification.warning","LOCAL_STORAGE_NOT_SUPPORTED"),y.removeItem&&e.$broadcast("LocalStorageModule.notification.removeitem",{key:o,storageType:"cookie"}),L(o);else try{h.removeItem(w(o)),y.removeItem&&e.$broadcast("LocalStorageModule.notification.removeitem",{key:o,storageType:g.storageType})}catch(t){e.$broadcast("LocalStorageModule.notification.error",t.message),L(o)}}finally{D(t)}},A=function(t){var n=N();try{if(D(t),!T)return e.$broadcast("LocalStorageModule.notification.warning","LOCAL_STORAGE_NOT_SUPPORTED"),[];var r=m.length,o=[];for(var i in h)if(i.substr(0,r)===m)try{o.push(i.substr(r))}catch(t){return e.$broadcast("LocalStorageModule.notification.error",t.Description),[]}return o}finally{D(n)}},O=function(t,n){var r=N();try{D(n);var o=m?new RegExp("^"+m):new RegExp,i=t?new RegExp(t):new RegExp;if(!T&&g.defaultToCookie||"cookie"===g.storageType)return T||e.$broadcast("LocalStorageModule.notification.warning","LOCAL_STORAGE_NOT_SUPPORTED"),P();if(!T&&!g.defaultToCookie)return!1;var a=m.length;for(var s in h)if(o.test(s)&&i.test(s.substr(a)))try{E(s.substr(a))}catch(t){return e.$broadcast("LocalStorageModule.notification.error",t.message),P()}return!0}finally{D(r)}},I=function(){try{return t.navigator.cookieEnabled||"cookie"in c&&(c.cookie.length>0||(c.cookie="test").indexOf.call(c.cookie,"test")>-1)}catch(t){return e.$broadcast("LocalStorageModule.notification.error",t.message),!1}}(),B=function(t,n,s,l){if(r(n))return!1;if((a(n)||i(n))&&(n=u(n)),!I)return e.$broadcast("LocalStorageModule.notification.error","COOKIES_NOT_SUPPORTED"),!1;try{var d="",f=new Date,p="";if(null===n?(f.setTime(f.getTime()+-864e5),d="; expires="+f.toGMTString(),n=""):o(s)&&0!==s?(f.setTime(f.getTime()+24*s*60*60*1e3),d="; expires="+f.toGMTString()):0!==v.expiry&&(f.setTime(f.getTime()+24*v.expiry*60*60*1e3),d="; expires="+f.toGMTString()),t){var h="; path="+v.path;v.domain&&(p="; domain="+v.domain),"boolean"==typeof l?!0===l&&(p+="; secure"):!0===v.secure&&(p+="; secure"),c.cookie=w(t)+"="+encodeURIComponent(n)+d+h+p}}catch(t){return e.$broadcast("LocalStorageModule.notification.error",t.message),!1}return!0},M=function(t){if(!I)return e.$broadcast("LocalStorageModule.notification.error","COOKIES_NOT_SUPPORTED"),!1;for(var n=c.cookie&&c.cookie.split(";")||[],r=0;r<n.length;r++){for(var o=n[r];" "===o.charAt(0);)o=o.substring(1,o.length);if(0===o.indexOf(w(t)+"=")){var i=decodeURIComponent(o.substring(m.length+t.length+1,o.length));try{var a=JSON.parse(i);return"number"==typeof a?i:a}catch(e){return i}}}return null},L=function(e){B(e,null)},P=function(){for(var e=null,t=m.length,n=c.cookie.split(";"),r=0;r<n.length;r++){for(e=n[r];" "===e.charAt(0);)e=e.substring(1,e.length);var o=e.substring(t,e.indexOf("="));L(o)}},N=function(){return b},D=function(e){return e&&b!==e&&(b=e,T=x()),T},_=function(e,t,r,o,a){o=o||t;var s=$(o,a);return null===s&&n(r)?s=r:i(s)&&i(r)&&(s=l(s,r)),d(t).assign(e,s),e.$watch(t,function(e){k(o,e,a)},i(e[t]))};T&&(t.addEventListener?(t.addEventListener("storage",p,!1),e.$on("$destroy",function(){t.removeEventListener("storage",p)})):t.attachEvent&&(t.attachEvent("onstorage",p),e.$on("$destroy",function(){t.detachEvent("onstorage",p)})));var j=function(e){var n=N();try{D(e);for(var r=0,o=t[b],i=0;i<o.length;i++)0===o.key(i).indexOf(m)&&r++;return r}finally{D(n)}};return{isSupported:T,getStorageType:N,setStorageType:D,setPrefix:function(e){m=e},set:k,add:k,get:$,keys:A,remove:E,clearAll:O,bind:_,deriveKey:w,underiveKey:C,length:j,defaultToCookie:this.defaultToCookie,cookie:{isSupported:I,set:B,add:B,get:M,remove:L,clearAll:P}}}]})}(window,window.angular),/**
 * angular-ui-notification - Angular.js service providing simple notifications using Bootstrap 3 styles with css transitions for animating
 * <AUTHOR>
 * @version v0.3.6
 * @link https://github.com/alexcrack/angular-ui-notification
 * @license MIT
 */
angular.module("ui-notification",[]),angular.module("ui-notification").provider("Notification",function(){this.options={delay:5e3,startTop:10,startRight:10,verticalSpacing:10,horizontalSpacing:10,positionX:"right",positionY:"top",replaceMessage:!1,templateUrl:"angular-ui-notification.html",onClose:void 0,closeOnClick:!0,maxCount:0,container:"body",priority:10},this.setOptions=function(e){if(!angular.isObject(e))throw new Error("Options should be an object!");this.options=angular.extend({},this.options,e)},this.$get=["$timeout","$http","$compile","$templateCache","$rootScope","$injector","$sce","$q","$window",function(e,t,n,r,o,i,a,s,l){var u=this.options,c=u.startTop,d=u.startRight,f=u.verticalSpacing,p=u.horizontalSpacing,h=u.delay,g=[],m=!1,v=function(i,v){function y(t){function r(e){["-webkit-transition","-o-transition","transition"].forEach(function(t){y.css(t,e)})}var o=i.scope.$new();o.message=a.trustAsHtml(i.message),o.title=a.trustAsHtml(i.title),o.t=i.type.substr(0,1),o.delay=i.delay,o.onClose=i.onClose;var s=function(e,t){return e._priority-t._priority},h=function(e,t){return t._priority-e._priority},v=function(){var e=0,t=0,n=c,r=d,o=[];"top"===i.positionY?g.sort(s):"bottom"===i.positionY&&g.sort(h);for(var a=g.length-1;a>=0;a--){var l=g[a];if(i.replaceMessage&&a<g.length-1)l.addClass("killed");else{var m=parseInt(l[0].offsetHeight),v=parseInt(l[0].offsetWidth),y=o[l._positionY+l._positionX];b+m>window.innerHeight&&(y=c,t++,e=0);var b=n=y?0===e?y:y+f:c,w=r+t*(p+v);l.css(l._positionY,b+"px"),"center"==l._positionX?l.css("left",parseInt(window.innerWidth/2-v/2)+"px"):l.css(l._positionX,w+"px"),o[l._positionY+l._positionX]=b+m,u.maxCount>0&&g.length>u.maxCount&&0===a&&l.scope().kill(!0),e++}}},y=n(t)(o);y._positionY=i.positionY,y._positionX=i.positionX,y._priority=i.priority,y.addClass(i.type);var w=function(e){e=e.originalEvent||e,("click"===e.type||"opacity"===e.propertyName&&e.elapsedTime>=1)&&(o.onClose&&o.$apply(o.onClose(y)),y.remove(),g.splice(g.indexOf(y),1),o.$destroy(),v())};i.closeOnClick&&(y.addClass("clickable"),y.bind("click",w)),y.bind("webkitTransitionEnd oTransitionEnd otransitionend transitionend msTransitionEnd",w),angular.isNumber(i.delay)&&e(function(){y.addClass("killed")},i.delay),r("none"),angular.element(document.querySelector(i.container)).append(y);var C=-(parseInt(y[0].offsetHeight)+50);if(y.css(y._positionY,C+"px"),g.push(y),"center"==i.positionX){var S=parseInt(y[0].offsetWidth);y.css("left",parseInt(window.innerWidth/2-S/2)+"px")}e(function(){r("")}),o._templateElement=y,o.kill=function(t){t?(o.onClose&&o.$apply(o.onClose(o._templateElement)),g.splice(g.indexOf(o._templateElement),1),o._templateElement.remove(),o.$destroy(),e(v)):o._templateElement.addClass("killed")},e(v),m||(angular.element(l).bind("resize",function(t){e(v)}),m=!0),b.resolve(o)}var b=s.defer();"object"==typeof i&&null!==i||(i={message:i}),i.scope=i.scope?i.scope:o,i.template=i.templateUrl?i.templateUrl:u.templateUrl,i.delay=angular.isUndefined(i.delay)?h:i.delay,i.type=v||i.type||u.type||"",i.positionY=i.positionY?i.positionY:u.positionY,i.positionX=i.positionX?i.positionX:u.positionX,i.replaceMessage=i.replaceMessage?i.replaceMessage:u.replaceMessage,i.onClose=i.onClose?i.onClose:u.onClose,i.closeOnClick=null!==i.closeOnClick&&void 0!==i.closeOnClick?i.closeOnClick:u.closeOnClick,i.container=i.container?i.container:u.container,i.priority=i.priority?i.priority:u.priority;var w=r.get(i.template);return w?y(w):t.get(i.template,{cache:!0}).then(function(e){y(e.data)}).catch(function(e){throw new Error("Template ("+i.template+") could not be loaded. "+e)}),b.promise};return v.primary=function(e){return this(e,"primary")},v.error=function(e){return this(e,"error")},v.success=function(e){return this(e,"success")},v.info=function(e){return this(e,"info")},v.warning=function(e){return this(e,"warning")},v.clearAll=function(){angular.forEach(g,function(e){e.addClass("killed")})},v}]}),angular.module("ui-notification").run(["$templateCache",function(e){e.put("angular-ui-notification.html",'<div class="ui-notification"><h3 ng-show="title" ng-bind-html="title"></h3><div class="message" ng-bind-html="message"></div></div>')}]),angular.module("angularBittorrentPeerid",[]),angular.module("angularBittorrentPeerid").run(function(){"function"!=typeof String.prototype.endsWith&&(String.prototype.endsWith=function(e){return this.slice(-e.length)===e}),"function"!=typeof String.prototype.startsWith&&(String.prototype.startsWith=function(e,t){return t=t||0,this.slice(t,t+e.length)===e})}),angular.module("angularBittorrentPeerid").factory("peeridUtils",function(){function e(e){var t=e.charCodeAt(0);return t>="0".charCodeAt(0)&&t<="9".charCodeAt(0)}function t(e){var t=e.toLowerCase().charCodeAt(0);return t>="a".charCodeAt(0)&&t<="z".charCodeAt(0)}function n(n){return e(n)||t(n)||"."===n}function r(e,t){t=t||0;for(var n=""+(255&e);n.length<t;)n="0"+n;return n}return{getUtf8Data:function(e){var t,n,r=[];for(t=0;t<e.length;t++)n=e.charCodeAt(t),128>n?r.push(n):2048>n?r.push(192|n>>6,128|63&n):r.push(224|n>>12,128|63&n>>6,128|63&n);return r},isAzStyle:function(e){return"-"===e.charAt(0)&&("-"===e.charAt(7)||("FG"===e.substring(1,3)||("LH"===e.substring(1,3)||("NE"===e.substring(1,3)||("KT"===e.substring(1,3)||"SP"===e.substring(1,3))))))},isShadowStyle:function(r){var o,i,a;if("-"!==r.charAt(5))return!1;if(!t(r.charAt(0)))return!1;if(!e(r.charAt(1))&&"-"!==r.charAt(1))return!1;for(o=4;o>0&&"-"===r.charAt(o);o--);for(i=1;o>=i;i++){if("-"===(a=r.charAt(i)))return!1;if(null===n(a))return!1}return!0},isMainlineStyle:function(e){return"-"===e.charAt(2)&&"-"===e.charAt(7)&&("-"===e.charAt(4)||"-"===e.charAt(5))},isPossibleSpoofClient:function(e){return e.endsWith("UDP0")||e.endsWith("HTTPBT")},decodeNumericValueOfByte:r,getAzStyleVersionNumber:function(e,t){return"function"==typeof t?t(e):null},getShadowStyleVersionNumber:function(){return null},decodeBitSpiritClient:function(e,t){if("BS"!==e.substring(2,4))return null;var n=""+t[1];return"0"===n&&(n=1),{client:"BitSpirit",version:n}},decodeBitCometClient:function(e,t){var n,o,i,a,s="";if(e.startsWith("exbc"))s="";else if(e.startsWith("FUTB"))s="(Solidox Mod)";else{if(!e.startsWith("xUTB"))return null;s="(Mod 2)"}return n="LORD"===e.substring(6,10),o=n?"BitLord":"BitComet",i=r(t[4]),a=n&&"0"!==i?1:2,{client:o+(s?" "+s:""),version:i+"."+r(t[5],a)}},identifyAwkwardClient:function(e,t){var n,r,o=20;for(n=0;20>n;++n)if(t[n]>0){o=n;break}if(0===o){for(r=!0,n=0;16>n;++n)if(0===t[n]){r=!1;break}if(r){for(n=16;20>n;++n)if(t[n]!==(t[n%16]^t[15-n%16])){r=!1;break}if(r)return{client:"Shareaza"}}}return 9===o&&3===t[9]&&3===t[10]&&3===t[11]?{client:"I2PSnark"}:12===o&&97===t[12]&&97===t[13]?{client:"Experimental",version:"3.2.1b2"}:12===o&&0===t[12]&&0===t[13]?{client:"Experimental",version:"3.1"}:12===o?{client:"Mainline"}:null}}}),angular.module("angularBittorrentPeerid").provider("bittorrentPeeridService",function(){var e={},t={},n={},r={},o={},i=[],a=function(e){return e[0]+"."+e[1]+"."+e[2]},s=function(e){return isNaN(e[2])?e[0]+"."+e[1]+".1"+"ABCDE".indexOf(e[2]):e[0]+"."+e[1]+"."+e[2]},l=function(e){var t=e[3];return t="B"===t?"Beta":"A"===t?"Alpha":"",e[0]+"."+e[1]+"."+e[2]+" "+t},u=function(e){return e[0]+"."+e[1]+"."+e[2]+"."+e[3]},c=function(e){return e[0]+e[1]+"."+e[2]+e[3]},d=function(e){return e[1]+"."+e[2]+e[3]},f=function(e){return"0"===e[0]&&"0"===e[1]&&"0"===e[2]?"0."+e[3]:"0"===e[0]&&"0"===e[1]?"0."+e[2]+e[3]:e[0]+"."+e[1]+e[2]+("Z"===e[3]||"X"===e[3]?"+":"")},p=function(e){var t="";return t+="0"===e[0]?e[1]+".":""+e[0]+e[1]+".",t+="0"===e[2]?e[3]:""+e[2]+e[3]},h="2.33.4",g="NO_VERSION",m=this.addAzStyle=function(n,r,o){o=o||u,e[n]=r,t[r]=o},v=this.addShadowStyle=function(e,t,o){o=o||a,n[e]=t,r[t]=o},y=this.addMainlineStyle=function(e,t){o[e]=t},b=this.addSimpleClient=function(e,t,n,r){("number"==typeof n||void 0===n)&&(r=n,n=t,t=void 0),i.push({id:n,client:e,version:t,position:r||0})};!function(){m("A~","Ares",a),m("AG","Ares",a),m("AN","Ares",u),m("AR","Ares"),m("AV","Avicora"),m("AX","BitPump",c),m("AT","Artemis"),m("AZ","Vuze",u),m("BB","BitBuddy","1.234"),m("BC","BitComet",d),m("BE","BitTorrent SDK"),m("BF","BitFlu",g),m("BG","BTG",u),m("bk","BitKitten (libtorrent)"),m("BR","BitRocket","1.2(34)"),m("BS","BTSlave"),m("BT","BitTorrent",l),m("BW","BitWombat"),m("BX","BittorrentX"),m("CB","Shareaza Plus"),m("CD","Enhanced CTorrent",c),m("CT","CTorrent","1.2.34"),m("DP","Propogate Data Client"),m("DE","Deluge",s),m("EB","EBit"),m("ES","Electric Sheep",a),m("FC","FileCroc"),m("FG","FlashGet",d),m("FT","FoxTorrent/RedSwoosh"),m("GR","GetRight","1.2"),m("GS","GSTorrent"),m("HL","Halite",a),m("HN","Hydranode"),m("KG","KGet"),m("KT","KTorrent","1.2.3=[RD].4"),m("LC","LeechCraft"),m("LH","LH-ABC"),m("LK","linkage",a),m("LP","Lphant",c),m("LT","libtorrent (Rasterbar)",h),m("lt","libTorrent (Rakshasa)",h),m("LW","LimeWire",g),m("MO","MonoTorrent"),m("MP","MooPolice",a),m("MR","Miro"),m("MT","MoonlightTorrent"),m("NE","BT Next Evolution",a),m("NX","Net Transport"),m("OS","OneSwarm",u),m("OT","OmegaTorrent"),m("PC","CacheLogic","12.3-4"),m("PT","Popcorn Time"),m("PD","Pando"),m("PE","PeerProject"),m("pX","pHoeniX"),m("qB","qBittorrent",s),m("QD","qqdownload"),m("RT","Retriever"),m("RZ","RezTorrent"),m("S~","Shareaza alpha/beta"),m("SB","SwiftBit"),m("SD","迅雷在线 (Xunlei)"),m("SG","GS Torrent",u),m("SN","ShareNET"),m("SP","BitSpirit",a),m("SS","SwarmScope"),m("ST","SymTorrent","2.34"),m("st","SharkTorrent"),m("SZ","Shareaza"),m("TN","Torrent.NET"),m("TR","Transmission",f),m("TS","TorrentStorm"),m("TT","TuoTu",a),m("UL","uLeecher!"),m("UE","µTorrent Embedded",l),m("UT","µTorrent",l),m("UM","µTorrent Mac",l),m("WD","WebTorrent Desktop",p),m("WT","Bitlet"),m("WW","WebTorrent",p),m("WY","FireTorrent"),m("VG","哇嘎 (Vagaa)",u),m("XL","迅雷在线 (Xunlei)"),m("XT","XanTorrent"),m("XF","Xfplay",f),m("XX","XTorrent","1.2.34"),m("XC","XTorrent","1.2.34"),m("ZT","ZipTorrent"),m("7T","aTorrent"),m("ZO","Zona",u),m("#@","Invalid PeerID"),v("A","ABC"),v("O","Osprey Permaseed"),v("Q","BTQueue"),v("R","Tribler"),v("S","Shad0w"),v("T","BitTornado"),v("U","UPnP NAT"),y("M","Mainline"),y("Q","Queen Bee"),b("µTorrent","1.7.0 RC","-UT170-"),b("Azureus","1","Azureus"),b("Azureus","*******","Azureus",5),b("Aria","2","-aria2-"),b("BitTorrent Plus!","II","PRC.P---"),b("BitTorrent Plus!","P87.P---"),b("BitTorrent Plus!","S587Plus"),b("BitTyrant (Azureus Mod)","AZ2500BT"),b("Blizzard Downloader","BLZ"),b("BTGetit","BG",10),b("BTugaXP","btuga"),b("BTugaXP","BTuga",5),b("BTugaXP","oernu"),b("Deadman Walking","BTDWV-"),b("Deadman","Deadman Walking-"),b("External Webseed","Ext"),b("G3 Torrent","-G3"),b("GreedBT","2.7.1","271-"),b("Hurricane Electric","arclight"),b("HTTP Seed","-WS"),b("JVtorrent","10-------"),b("Limewire","LIME"),b("Martini Man","martini"),b("Pando","Pando"),b("PeerApp","PEERAPP"),b("SimpleBT","btfans",4),b("Swarmy","a00---0"),b("Swarmy","a02---0"),b("Teeweety","T00---0"),b("TorrentTopia","346-"),b("XanTorrent","DansClient"),b("MediaGet","-MG1"),b("MediaGet","2.1","-MG21"),b("Amazon AWS S3","S3-"),b("BitTorrent DNA","DNA"),b("Opera","OP"),b("Opera","O"),b("Burst!","Mbrst"),b("TurboBT","turbobt"),b("BT Protocol Daemon","btpd"),b("Plus!","Plus"),b("XBT","XBT"),b("BitsOnWheels","-BOW"),b("eXeem","eX"),b("MLdonkey","-ML"),b("Bitlet","BitLet"),b("AllPeers","AP"),b("BTuga Revolution","BTM"),b("Rufus","RS",2),b("BitMagnet","BM",2),b("QVOD","QVOD"),b("Top-BT","TB"),b("Tixati","TIX"),b("folx","-FL"),b("µTorrent Mac","-UM"),b("µTorrent","-UT")}(),this.$get=["peeridUtils",function(r){var a=function(t){return e[t.substring(1,3)]},s=function(e){return n[e.substring(0,1)]},l=function(e){return o[e.substring(0,1)]},u=function(e){var t,n;for(t=0;t<i.length;++t)if(n=i[t],e.startsWith(n.id,n.position))return n;return null},c=function(e,n){var o=t[e];return o?r.getAzStyleVersionNumber(n.substring(3,7),o):null};return{parseClient:function(e){var t,n,o=r.getUtf8Data(e),i=null;return r.isPossibleSpoofClient(e)?(i=r.decodeBitSpiritClient(e,o))?i:(i=r.decodeBitCometClient(e,o))?i:{client:"BitSpirit?"}:r.isAzStyle(e)&&(i=a(e))?(t=c(i,e),i.startsWith("ZipTorrent")&&e.startsWith("bLAde",8)?{client:"Unknown [Fake: ZipTorrent]",version:t}:"µTorrent"===i&&"6.0 Beta"===t?{client:"Mainline",version:"6.0 Beta"}:i.startsWith("libTorrent (Rakshasa)")?{client:i+" / rTorrent*",version:t}:{client:i,version:t}):r.isShadowStyle(e)&&(i=s(e))?{client:i}:r.isMainlineStyle(e)&&(i=l(e))?{client:i}:(i=r.decodeBitSpiritClient(e,o))?i:(i=r.decodeBitCometClient(e,o))?i:(n=u(e),n?(i=n.client,{client:i,version:n.version}):(i=r.identifyAwkwardClient(e,o))?i:{client:"unknown"})}}}]}),angular.module("cgBusy",[]),angular.module("cgBusy").factory("_cgBusyTrackerFactory",["$timeout","$q",function(e,t){return function(){var n={};n.promises=[],n.delayPromise=null,n.durationPromise=null,n.delayJustFinished=!1,n.reset=function(t){n.minDuration=t.minDuration,n.promises=[],angular.forEach(t.promises,function(e){e&&!e.$cgBusyFulfilled&&r(e)}),0!==n.promises.length&&(n.delayJustFinished=!1,t.delay&&(n.delayPromise=e(function(){n.delayPromise=null,n.delayJustFinished=!0},parseInt(t.delay,10))),t.minDuration&&(n.durationPromise=e(function(){n.durationPromise=null},parseInt(t.minDuration,10)+(t.delay?parseInt(t.delay,10):0))))},n.isPromise=function(e){return void 0!==(e&&(e.then||e.$then||e.$promise&&e.$promise.then))},n.callThen=function(e,n,r){var o;e.then||e.$then?o=e:e.$promise?o=e.$promise:e.denodeify&&(o=t.when(e)),(o.then||o.$then).call(o,n,r)};var r=function(e){if(!n.isPromise(e))throw new Error("cgBusy expects a promise (or something that has a .promise or .$promise");-1===n.promises.indexOf(e)&&(n.promises.push(e),n.callThen(e,function(){e.$cgBusyFulfilled=!0,-1!==n.promises.indexOf(e)&&n.promises.splice(n.promises.indexOf(e),1)},function(){e.$cgBusyFulfilled=!0,-1!==n.promises.indexOf(e)&&n.promises.splice(n.promises.indexOf(e),1)}))};return n.active=function(){return!n.delayPromise&&(n.delayJustFinished?(n.delayJustFinished=!1,0===n.promises.length&&(n.durationPromise=null),n.promises.length>0):!!n.durationPromise||n.promises.length>0)},n}}]),angular.module("cgBusy").value("cgBusyDefaults",{}),angular.module("cgBusy").directive("cgBusy",["$compile","$templateCache","cgBusyDefaults","$http","_cgBusyTrackerFactory",function(e,t,n,r,o){return{restrict:"A",link:function(i,a,s){var l=a.css("position");("static"===l||""===l||void 0===l)&&a.css("position","relative");var u,c,d,f,p,h=o(),g={templateUrl:"angular-busy.html",delay:0,minDuration:0,backdrop:!0,message:"Please Wait...",wrapperClass:"cg-busy cg-busy-animation"};angular.extend(g,n),i.$watchCollection(s.cgBusy,function(n){if(n||(n={promise:null}),angular.isString(n))throw new Error("Invalid value for cg-busy. cgBusy no longer accepts string ids to represent promises/trackers.");(angular.isArray(n)||h.isPromise(n))&&(n={promise:n}),n=angular.extend(angular.copy(g),n),n.templateUrl||(n.templateUrl=g.templateUrl),angular.isArray(n.promise)||(n.promise=[n.promise]),f||(f=i.$new()),f.$message=n.message,angular.equals(h.promises,n.promise)||h.reset({promises:n.promise,delay:n.delay,minDuration:n.minDuration}),f.$cgBusyIsActive=function(){return h.active()},u&&d===n.templateUrl&&p===n.backdrop||(u&&u.remove(),c&&c.remove(),d=n.templateUrl,p=n.backdrop,r.get(d,{cache:t}).then(function(t){if(n.backdrop=void 0===n.backdrop||n.backdrop,n.backdrop){c=e('<div class="cg-busy cg-busy-backdrop cg-busy-backdrop-animation ng-hide" ng-show="$cgBusyIsActive()"></div>')(f),a.append(c)}var r='<div class="'+n.wrapperClass+' ng-hide" ng-show="$cgBusyIsActive()">'+t.data+"</div>";u=e(r)(f),angular.element(u.children()[0]).css("position","absolute").css("top",0).css("left",0).css("right",0).css("bottom",0),a.append(u)},function(e){throw new Error("Template specified for cgBusy ("+n.templateUrl+") could not be loaded. "+e)}))},!0)}}}]),angular.module("cgBusy").run(["$templateCache",function(e){"use strict";e.put("angular-busy.html",'<div class="cg-busy-default-wrapper">\n\n   <div class="cg-busy-default-sign">\n\n      <div class="cg-busy-default-spinner">\n         <div class="bar1"></div>\n         <div class="bar2"></div>\n         <div class="bar3"></div>\n         <div class="bar4"></div>\n         <div class="bar5"></div>\n         <div class="bar6"></div>\n         <div class="bar7"></div>\n         <div class="bar8"></div>\n         <div class="bar9"></div>\n         <div class="bar10"></div>\n         <div class="bar11"></div>\n         <div class="bar12"></div>\n      </div>\n\n      <div class="cg-busy-default-text">{{$message}}</div>\n\n   </div>\n\n</div>')}]),angular.module("angularPromiseButtons",[]),angular.module("angularPromiseButtons").directive("promiseBtn",["angularPromiseButtons","$parse","$timeout","$compile",function(e,t,n,r){"use strict";return{restrict:"EA",priority:e.config.priority,scope:{promiseBtn:"=",promiseBtnOptions:"=?",ngDisabled:"=?"},link:function(o,i,a){function s(e){b.btnLoadingClass&&!b.addClassToCurrentBtnOnly&&e.addClass(b.btnLoadingClass),b.disableBtn&&!b.disableCurrentBtnOnly&&e.attr("disabled","disabled")}function l(e){b.minDuration&&!v||!y||(b.btnLoadingClass&&e.removeClass(b.btnLoadingClass),b.disableBtn&&!o.ngDisabled&&e.removeAttr("disabled"))}function u(e,t){o.$watch(e,function(e){v=!1,y=!1,b.minDuration&&(m=n(function(){v=!0,l(t)},b.minDuration)),e&&e.then?(s(t),e.finally?e.finally(function(){y=!0,l(t)}):e.then(function(){y=!0,l(t)}).catch(function(){y=!0,l(t)})):e&&e.$promise&&(s(t),e.$promise.finally(function(){y=!0,l(t)}))})}function c(e){return e.split(";").map(function(e){return t(e)})}function d(e){e.append(r(b.spinnerTpl)(o))}function f(e){b.addClassToCurrentBtnOnly&&e.on(b.CLICK_EVENT,function(){e.addClass(b.btnLoadingClass)}),b.disableCurrentBtnOnly&&e.on(b.CLICK_EVENT,function(){e.attr("disabled","disabled")})}function p(e,t,n){var r=c(a[t]);i.off(e),i.on(e,function(e){o.$apply(function(){r.forEach(function(t){var r=t(o.$parent,{$event:e});g||(g=u(function(){return r},n))})})})}function h(e,t,n){b.priority>0?p(e,t,n):o.$evalAsync(function(){p(e,t,n)})}var g,m,v,y,b=e.config;if(a.promiseBtn)d(i),f(i),u(function(){return o.promiseBtn},i);else if(a.hasOwnProperty(b.CLICK_ATTR))d(i),f(i),h(b.CLICK_EVENT,b.CLICK_ATTR,i);else if(a.hasOwnProperty(b.SUBMIT_ATTR)){var w=function(t){for(var n=[],r=t.find(e.config.BTN_SELECTOR),o=0;o<r.length;o++){var i=r[o];"submit"===angular.element(i).attr("type")&&n.push(i)}return angular.element(n)}(i);d(w),f(w),h(b.SUBMIT_EVENT,b.SUBMIT_ATTR,w)}o.$watch("promiseBtnOptions",function(e){angular.isObject(e)&&(b=angular.extend({},b,e))},!0),o.$on("$destroy",function(){n.cancel(m)})}}}]),angular.module("angularPromiseButtons").provider("angularPromiseButtons",function(){"use strict";var e={spinnerTpl:'<span class="btn-spinner"></span>',priority:10,disableBtn:!0,btnLoadingClass:"is-loading",addClassToCurrentBtnOnly:!1,disableCurrentBtnOnly:!1,minDuration:!1,CLICK_EVENT:"click",CLICK_ATTR:"ngClick",SUBMIT_EVENT:"submit",SUBMIT_ATTR:"ngSubmit",BTN_SELECTOR:"button"};return{extendConfig:function(t){e=angular.extend(e,t)},$get:function(){return{config:e}}}}),function(e,t){"function"==typeof define&&define.amd?define(["angular"],t):"object"==typeof module&&module.exports?module.exports=t(require("angular")):e.angularClipboard=t(e.angular)}(this,function(e){return e.module("angular-clipboard",[]).factory("clipboard",["$document","$window",function(e,t){function n(n,r){var o=e[0].createElement("textarea");return o.style.position="absolute",o.style.fontSize="12pt",o.style.border="0",o.style.padding="0",o.style.margin="0",o.style.left="-10000px",o.style.top=(t.pageYOffset||e[0].documentElement.scrollTop)+"px",o.textContent=n,o}function r(t){try{e[0].body.style.webkitUserSelect="initial";var n=e[0].getSelection();n.removeAllRanges();var r=document.createRange();r.selectNodeContents(t),n.addRange(r),t.select(),t.setSelectionRange(0,999999);try{if(!e[0].execCommand("copy"))throw"failure copy"}finally{n.removeAllRanges()}}finally{e[0].body.style.webkitUserSelect=""}}function o(o,i){var a=t.pageXOffset||e[0].documentElement.scrollLeft,s=t.pageYOffset||e[0].documentElement.scrollTop,l=n(o,i);e[0].body.appendChild(l),r(l),t.scrollTo(a,s),e[0].body.removeChild(l)}return{copyText:o,supported:"queryCommandSupported"in e[0]&&e[0].queryCommandSupported("copy")}}]).directive("clipboard",["clipboard",function(t){return{restrict:"A",scope:{onCopied:"&",onError:"&",text:"=",supported:"=?"},link:function(n,r){n.supported=t.supported,r.on("click",function(o){try{t.copyText(n.text,r[0]),e.isFunction(n.onCopied)&&n.$evalAsync(n.onCopied())}catch(t){e.isFunction(n.onError)&&n.$evalAsync(n.onError({err:t}))}})}}}])}),angular.module("inputDropdown",[]).directive("inputDropdown",[function(){return{restrict:"E",scope:{defaultDropdownItems:"=",selectedItem:"=",allowCustomInput:"=",inputRequired:"=",disabled:"=",inputName:"@",inputClassName:"@",inputPlaceholder:"@",onlyShowNonEmptyDropdown:"@",filterListMethod:"&",valueChangedMethod:"&",itemSelectedMethod:"&"},template:'<div class="input-dropdown"><input type="text"name="{{inputName}}"placeholder="{{inputPlaceholder}}"autocomplete="off"ng-model="inputValue"class="{{inputClassName}}"ng-required="inputRequired"ng-change="inputChange()"ng-focus="inputFocus()"ng-blur="inputBlur($event)"ng-disabled="disabled"input-dropdown-validator><ul ng-show="dropdownVisible && dropdownItems && dropdownItems.length"><li ng-repeat="item in dropdownItems"ng-click="selectItem(item)"ng-mouseenter="setActive($index)"ng-mousedown="dropdownPressed()"ng-class="{\'active\': activeItemIndex === $index}"><span ng-if="item.readableName">{{item.readableName}}</span><span ng-if="!item.readableName">{{item}}</span></li></ul></div>',controller:["$scope",function(e){this.getSelectedItem=function(){return e.selectedItem},this.isRequired=function(){return e.inputRequired},this.customInputAllowed=function(){return e.allowCustomInput},this.getInput=function(){return e.inputValue}}],link:function(e,t){var n=!1,r=t.find("input").isolateScope();e.activeItemIndex=0,e.inputValue="",e.dropdownVisible=!1,e.dropdownItems=e.defaultDropdownItems||[],e.$watch("dropdownItems",function(t,n){angular.equals(t,n)||(e.allowCustomInput?e.setInputActive():e.setActive(0))}),e.$watch("selectedItem",function(t,n){r.updateInputValidity(),angular.equals(t,n)||t&&(e.inputValue="string"==typeof t?t:t.readableName)}),e.setInputActive=function(){e.setActive(-1)},e.setActive=function(t){e.activeItemIndex=t},e.inputChange=function(){if(e.selectedItem=null,o(),u(e.inputValue,"input"),!e.inputValue)return void(e.dropdownItems=e.defaultDropdownItems||[]);if(e.allowCustomInput&&r.updateInputValidity(),e.filterListMethod){var t=e.filterListMethod({userInput:e.inputValue});t&&t.then(function(t){e.dropdownItems=t})}},e.inputFocus=function(){e.allowCustomInput?e.setInputActive():e.setActive(0),o()},e.inputBlur=function(e){if(n)return void(n=!1);i()},e.dropdownPressed=function(){n=!0},e.selectItem=function(t){e.selectedItem=t,i(),e.dropdownItems=[t],u(t,"select"),e.itemSelectedMethod&&e.itemSelectedMethod({item:t})};var o=function(){(!e.onlyShowNonEmptyDropdown||e.dropdownItems&&e.dropdownItems.length)&&(e.dropdownVisible=!0)},i=function(){e.dropdownVisible=!1},a=function(){var t=e.activeItemIndex-1;t>=0?e.setActive(t):e.allowCustomInput&&e.setInputActive()},s=function(){var t=e.activeItemIndex+1;t<e.dropdownItems.length&&e.setActive(t)},l=function(){e.activeItemIndex>=0&&e.activeItemIndex<e.dropdownItems.length?e.selectItem(e.dropdownItems[e.activeItemIndex]):e.allowCustomInput&&e.activeItemIndex},u=function(t,n){e.valueChangedMethod&&e.valueChangedMethod({value:t,from:n})};t.bind("keydown keypress",function(t){switch(t.which){case 38:e.$apply(a);break;case 40:e.$apply(s);break;case 13:e.dropdownVisible&&e.dropdownItems&&e.dropdownItems.length>0&&-1!==e.activeItemIndex&&(t.preventDefault(),e.$apply(l));break;case 9:e.dropdownVisible&&e.dropdownItems&&e.dropdownItems.length>0&&-1!==e.activeItemIndex&&e.$apply(l)}})}}}]),angular.module("inputDropdown").directive("inputDropdownValidator",function(){return{require:["^inputDropdown","ngModel"],restrict:"A",scope:{},link:function(e,t,n,r){var o=r[0],i=r[1];e.updateInputValidity=function(){var e=o.getSelectedItem(),t=!1;o.isRequired()?o.customInputAllowed()&&o.getInput()?t=!0:e&&(t=!0):t=!0,i.$setValidity("itemSelectedValid",t)}}}}),function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var t;t="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,t.angularDragula=e()}}(function(){return function e(t,n,r){function o(a,s){if(!n[a]){if(!t[a]){var l="function"==typeof require&&require;if(!s&&l)return l(a,!0);if(i)return i(a,!0);var u=new Error("Cannot find module '"+a+"'");throw u.code="MODULE_NOT_FOUND",u}var c=n[a]={exports:{}};t[a][0].call(c.exports,function(e){var n=t[a][1][e];return o(n||e)},c,c.exports,e,t,n,r)}return n[a].exports}for(var i="function"==typeof require&&require,a=0;a<r.length;a++)o(r[a]);return o}({1:[function(e,t,n){"use strict";function r(e){var t=e.module("dragula",["ng"]);return t.factory("dragulaService",o(e)),t.directive("dragula",i(e)),"dragula"}var o=e("./service"),i=e("./directive");t.exports=r},{"./directive":2,"./service":13}],2:[function(e,t,n){"use strict";function r(e){return["dragulaService",function(e){function t(t,n,r){var i,a=t.dragulaScope||t.$parent,s=n[0],l=t.$eval(r.dragula),u=e.find(a,l);u?(i=u.drake,i.containers.push(s)):(i=o({containers:[s]}),e.add(a,l,i)),t.$watch("dragulaModel",function(t,n){if(t){if(i.models){var r=n?i.models.indexOf(n):-1;r>=0?i.models.splice(r,1,t):i.models.push(t)}else i.models=[t];e.handleModels(a,i)}})}return{restrict:"A",scope:{dragulaScope:"=",dragulaModel:"="},link:t}}]}var o=e("dragula");t.exports=r},{dragula:10}],3:[function(e,t,n){t.exports=function(e,t){return Array.prototype.slice.call(e,t)}},{}],4:[function(e,t,n){"use strict";var r=e("ticky");t.exports=function(e,t,n){e&&r(function(){e.apply(n||null,t||[])})}},{ticky:11}],5:[function(e,t,n){"use strict";var r=e("atoa"),o=e("./debounce");t.exports=function(e,t){var n=t||{},i={};return void 0===e&&(e={}),e.on=function(t,n){return i[t]?i[t].push(n):i[t]=[n],e},e.once=function(t,n){return n._once=!0,e.on(t,n),e},e.off=function(t,n){var r=arguments.length;if(1===r)delete i[t];else if(0===r)i={};else{var o=i[t];if(!o)return e;o.splice(o.indexOf(n),1)}return e},e.emit=function(){var t=r(arguments);return e.emitterSnapshot(t.shift()).apply(this,t)},e.emitterSnapshot=function(t){var a=(i[t]||[]).slice(0);return function(){var i=r(arguments),s=this||e;if("error"===t&&!1!==n.throws&&!a.length)throw 1===i.length?i[0]:i;return a.forEach(function(r){n.async?o(r,i,s):r.apply(s,i),r._once&&e.off(t,r)}),e}},e}},{"./debounce":4,atoa:3}],6:[function(e,t,n){(function(n){"use strict";function r(e,t,n,r){return e.addEventListener(t,n,r)}function o(e,t,n){return e.attachEvent("on"+t,u(e,t,n))}function i(e,t,n,r){return e.removeEventListener(t,n,r)}function a(e,t,n){var r=c(e,t,n);return r?e.detachEvent("on"+t,r):void 0}function s(e,t,n){var r=-1===p.indexOf(t)?function(){return new f(t,{detail:n})}():function(){var e;return h.createEvent?(e=h.createEvent("Event"),e.initEvent(t,!0,!0)):h.createEventObject&&(e=h.createEventObject()),e}();e.dispatchEvent?e.dispatchEvent(r):e.fireEvent("on"+t,r)}function l(e,t,r){return function(t){var o=t||n.event;o.target=o.target||o.srcElement,o.preventDefault=o.preventDefault||function(){o.returnValue=!1},o.stopPropagation=o.stopPropagation||function(){o.cancelBubble=!0},o.which=o.which||o.keyCode,r.call(e,o)}}function u(e,t,n){var r=c(e,t,n)||l(e,t,n);return v.push({wrapper:r,element:e,type:t,fn:n}),r}function c(e,t,n){var r=d(e,t,n);if(r){var o=v[r].wrapper;return v.splice(r,1),o}}function d(e,t,n){var r,o;for(r=0;r<v.length;r++)if(o=v[r],o.element===e&&o.type===t&&o.fn===n)return r}var f=e("custom-event"),p=e("./eventmap"),h=n.document,g=r,m=i,v=[];n.addEventListener||(g=o,m=a),t.exports={add:g,remove:m,fabricate:s}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./eventmap":7,"custom-event":8}],7:[function(e,t,n){(function(e){"use strict";var n=[],r="",o=/^on/;for(r in e)o.test(r)&&n.push(r.slice(2));t.exports=n}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],8:[function(e,t,n){(function(e){var n=e.CustomEvent;t.exports=function(){try{var e=new n("cat",{detail:{foo:"bar"}});return"cat"===e.type&&"bar"===e.detail.foo}catch(e){}return!1}()?n:"function"==typeof document.createEvent?function(e,t){var n=document.createEvent("CustomEvent");return t?n.initCustomEvent(e,t.bubbles,t.cancelable,t.detail):n.initCustomEvent(e,!1,!1,void 0),n}:function(e,t){var n=document.createEventObject();return n.type=e,t?(n.bubbles=Boolean(t.bubbles),n.cancelable=Boolean(t.cancelable),n.detail=t.detail):(n.bubbles=!1,n.cancelable=!1,n.detail=void 0),n}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],9:[function(e,t,n){"use strict";function r(e){var t=a[e];return t?t.lastIndex=0:a[e]=t=new RegExp(s+e+l,"g"),t}function o(e,t){var n=e.className;n.length?r(t).test(n)||(e.className+=" "+t):e.className=t}function i(e,t){e.className=e.className.replace(r(t)," ").trim()}var a={},s="(?:^|\\s)",l="(?:\\s|$)";t.exports={add:o,rm:i}},{}],10:[function(e,t,n){(function(n){"use strict";function r(e,t){function n(e){return-1!==ue.containers.indexOf(e)||le.isContainer(e)}function r(e){var t=e?"remove":"add";o(x,t,"mousedown",k),o(x,t,"mouseup",P)}function s(e){o(x,e?"remove":"add","mousemove",$)}function g(e){var t=e?"remove":"add";w[t](x,"selectstart",T),w[t](x,"click",T)}function v(){r(!0),P({})}function T(e){ae&&e.preventDefault()}function k(e){if(ee=e.clientX,te=e.clientY,!(1!==i(e)||e.metaKey||e.ctrlKey)){var t=e.target,n=E(t);n&&(ae=n,s(),"mousedown"===e.type&&(h(t)?t.focus():e.preventDefault()))}}function $(e){if(ae){if(0===i(e))return void P({});if(void 0===e.clientX||e.clientX!==ee||void 0===e.clientY||e.clientY!==te){if(le.ignoreInputTextSelection){var t=y("clientX",e),n=y("clientY",e);if(h(S.elementFromPoint(t,n)))return}var r=ae;s(!0),g(),M(),I(r);var o=a(Q);Z=y("pageX",e)-o.left,J=y("pageY",e)-o.top,C.add(oe||Q,"gu-transit"),V(),z(e)}}}function E(e){if(!(ue.dragging&&K||n(e))){for(var t=e;p(e)&&!1===n(p(e));){if(le.invalid(e,t))return;if(!(e=p(e)))return}var r=p(e);if(r&&!le.invalid(e,t)){if(le.moves(e,r,t,m(e)))return{item:e,source:r}}}}function A(e){return!!E(e)}function O(e){var t=E(e);t&&I(t)}function I(e){G(e.item,e.source)&&(oe=e.item.cloneNode(!0),ue.emit("cloned",oe,e.item,"copy")),Y=e.source,Q=e.item,ne=re=m(e.item),ue.dragging=!0,ue.emit("drag",Q,Y)}function B(){return!1}function M(){if(ue.dragging){var e=oe||Q;N(e,p(e))}}function L(){ae=!1,s(!0),g(!0)}function P(e){if(L(),ue.dragging){var t=oe||Q,n=y("clientX",e),r=y("clientY",e),o=l(K,n,r),i=F(o,n,r);i&&(oe&&le.copySortSource||!oe||i!==Y)?N(t,i):le.removeOnSpill?D():_()}}function N(e,t){var n=p(e);oe&&le.copySortSource&&t===Y&&n.removeChild(Q),R(t)?ue.emit("cancel",e,Y,Y):ue.emit("drop",e,t,Y,re),j()}function D(){if(ue.dragging){var e=oe||Q,t=p(e);t&&t.removeChild(e),ue.emit(oe?"cancel":"remove",e,t,Y),j()}}function _(e){if(ue.dragging){var t=arguments.length>0?e:le.revertOnSpill,n=oe||Q,r=p(n),o=R(r);!1===o&&t&&(oe?r&&r.removeChild(oe):Y.insertBefore(n,ne)),o||t?ue.emit("cancel",n,Y,Y):ue.emit("drop",n,r,Y,re),j()}}function j(){var e=oe||Q;L(),H(),e&&C.rm(e,"gu-transit"),ie&&clearTimeout(ie),ue.dragging=!1,se&&ue.emit("out",e,se,Y),ue.emit("dragend",e),Y=Q=oe=ne=re=ie=se=null}function R(e,t){var n;return n=void 0!==t?t:K?re:m(oe||Q),e===Y&&n===ne}function F(e,t,r){for(var o=e;o&&!function(){if(!1===n(o))return!1
;var i=W(o,e),a=X(o,i,t,r);return!!R(o,a)||le.accepts(Q,o,Y,a)}();)o=p(o);return o}function z(e){function t(e){ue.emit(e,a,se,Y)}if(K){e.preventDefault();var n=y("clientX",e),r=y("clientY",e),o=n-Z,i=r-J;K.style.left=o+"px",K.style.top=i+"px";var a=oe||Q,s=l(K,n,r),u=F(s,n,r),c=null!==u&&u!==se;(c||null===u)&&(function(){se&&t("out")}(),se=u,function(){c&&t("over")}());var d=p(a);if(u===Y&&oe&&!le.copySortSource)return void(d&&d.removeChild(a));var f,h=W(u,s);if(null!==h)f=X(u,h,n,r);else{if(!0!==le.revertOnSpill||oe)return void(oe&&d&&d.removeChild(a));f=ne,u=Y}(null===f&&c||f!==a&&f!==m(a))&&(re=f,u.insertBefore(a,f),ue.emit("shadow",a,u,Y))}}function U(e){C.rm(e,"gu-hide")}function q(e){ue.dragging&&C.add(e,"gu-hide")}function V(){if(!K){var e=Q.getBoundingClientRect();K=Q.cloneNode(!0),K.style.width=d(e)+"px",K.style.height=f(e)+"px",C.rm(K,"gu-transit"),C.add(K,"gu-mirror"),le.mirrorContainer.appendChild(K),o(x,"add","mousemove",z),C.add(le.mirrorContainer,"gu-unselectable"),ue.emit("cloned",K,Q,"mirror")}}function H(){K&&(C.rm(le.mirrorContainer,"gu-unselectable"),o(x,"remove","mousemove",z),p(K).removeChild(K),K=null)}function W(e,t){for(var n=t;n!==e&&p(n)!==e;)n=p(n);return n===x?null:n}function X(e,t,n,r){function o(e){return e?m(t):t}var i="horizontal"===le.direction;return t!==e?function(){var e=t.getBoundingClientRect();return o(i?n>e.left+d(e)/2:r>e.top+f(e)/2)}():function(){var t,o,a,s=e.children.length;for(t=0;s>t;t++){if(o=e.children[t],a=o.getBoundingClientRect(),i&&a.left+a.width/2>n)return o;if(!i&&a.top+a.height/2>r)return o}return null}()}function G(e,t){return"boolean"==typeof le.copy?le.copy:le.copy(e,t)}1===arguments.length&&!1===Array.isArray(e)&&(t=e,e=[]);var K,Y,Q,Z,J,ee,te,ne,re,oe,ie,ae,se=null,le=t||{};void 0===le.moves&&(le.moves=c),void 0===le.accepts&&(le.accepts=c),void 0===le.invalid&&(le.invalid=B),void 0===le.containers&&(le.containers=e||[]),void 0===le.isContainer&&(le.isContainer=u),void 0===le.copy&&(le.copy=!1),void 0===le.copySortSource&&(le.copySortSource=!1),void 0===le.revertOnSpill&&(le.revertOnSpill=!1),void 0===le.removeOnSpill&&(le.removeOnSpill=!1),void 0===le.direction&&(le.direction="vertical"),void 0===le.ignoreInputTextSelection&&(le.ignoreInputTextSelection=!0),void 0===le.mirrorContainer&&(le.mirrorContainer=S.body);var ue=b({containers:le.containers,start:O,end:M,cancel:_,remove:D,destroy:v,canMove:A,dragging:!1});return!0===le.removeOnSpill&&ue.on("over",U).on("out",q),r(),ue}function o(e,t,r,o){var i={mouseup:"touchend",mousedown:"touchstart",mousemove:"touchmove"},a={mouseup:"pointerup",mousedown:"pointerdown",mousemove:"pointermove"},s={mouseup:"MSPointerUp",mousedown:"MSPointerDown",mousemove:"MSPointerMove"};n.navigator.pointerEnabled?w[t](e,a[r],o):n.navigator.msPointerEnabled?w[t](e,s[r],o):(w[t](e,i[r],o),w[t](e,r,o))}function i(e){if(void 0!==e.touches)return e.touches.length;if(void 0!==e.which&&0!==e.which)return e.which;if(void 0!==e.buttons)return e.buttons;var t=e.button;return void 0!==t?1&t?1:2&t?3:4&t?2:0:void 0}function a(e){var t=e.getBoundingClientRect();return{left:t.left+s("scrollLeft","pageXOffset"),top:t.top+s("scrollTop","pageYOffset")}}function s(e,t){return void 0!==n[t]?n[t]:x.clientHeight?x[e]:S.body[e]}function l(e,t,n){var r,o=e||{},i=o.className;return o.className+=" gu-hide",r=S.elementFromPoint(t,n),o.className=i,r}function u(){return!1}function c(){return!0}function d(e){return e.width||e.right-e.left}function f(e){return e.height||e.bottom-e.top}function p(e){return e.parentNode===S?null:e.parentNode}function h(e){return"INPUT"===e.tagName||"TEXTAREA"===e.tagName||"SELECT"===e.tagName||g(e)}function g(e){return!!e&&("false"!==e.contentEditable&&("true"===e.contentEditable||g(p(e))))}function m(e){return e.nextElementSibling||function(){var t=e;do{t=t.nextSibling}while(t&&1!==t.nodeType);return t}()}function v(e){return e.targetTouches&&e.targetTouches.length?e.targetTouches[0]:e.changedTouches&&e.changedTouches.length?e.changedTouches[0]:e}function y(e,t){var n=v(t),r={pageX:"clientX",pageY:"clientY"};return e in r&&!(e in n)&&r[e]in n&&(e=r[e]),n[e]}var b=e("contra/emitter"),w=e("crossvent"),C=e("./classes"),S=document,x=S.documentElement;t.exports=r}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./classes":9,"contra/emitter":5,crossvent:6}],11:[function(e,t,n){var r;r="function"==typeof setImmediate?function(e){setImmediate(e)}:function(e){setTimeout(e,0)},t.exports=r},{}],12:[function(e,t,n){"use strict";function r(e,t,n){function r(e){function r(){var r=o(arguments).map(a);r.unshift(t.name+"."+e),n.$emit.apply(n,r)}t.drake.on(e,r)}function a(t){return e.isElement(t)?e.element(t):t}i.forEach(r)}var o=e("atoa"),i=["cancel","cloned","drag","dragend","drop","out","over","remove","shadow","drop-model","remove-model"];t.exports=r},{atoa:3}],13:[function(e,t,n){"use strict";function r(e){return[function(){function t(t,n){if(!n.registered){var o,i,a,s;n.on("remove",function(e,r){n.models&&(s=n.models[n.containers.indexOf(r)],t.$applyAsync(function(){s.splice(i,1),n.emit("remove-model",e,r)}))}),n.on("drag",function(e,t){o=e,i=r(e,t)}),n.on("drop",function(l,u,c){n.models&&(a=r(l,u),t.$applyAsync(function(){if(s=n.models[n.containers.indexOf(c)],u===c)s.splice(a,0,s.splice(i,1)[0]);else{var t=o===l,r=n.models[n.containers.indexOf(u)],d=t?s[i]:e.copy(s[i]);t&&s.splice(i,1),r.splice(a,0,d),u.removeChild(l)}n.emit("drop-model",l,u,c)}))}),n.registered=!0}}function n(e){var t=e[i];return t||(t=e[i]={bags:[]}),t}function r(t,n){return Array.prototype.indexOf.call(e.element(n).children(),t)}function s(r,o,i){var s=l(r,o);if(s)throw new Error('Bag named: "'+o+'" already exists in same angular scope.');var u=n(r);return s={name:o,drake:i},u.bags.push(s),a(e,s,r),i.models&&t(r,i),s}function l(e,t){for(var r=n(e).bags,o=0;o<r.length;o++)if(r[o].name===t)return r[o]}function u(e,t){var r=n(e).bags,o=l(e,t),i=r.indexOf(o);r.splice(i,1),o.drake.destroy()}function c(e,n,r){t(e,s(e,n,o(r)).drake)}return{add:s,find:l,options:c,destroy:u,handleModels:t}}]}var o=e("dragula"),i="$$dragula",a=e("./replicate-events");t.exports=r},{"./replicate-events":12,dragula:10}]},{},[1])(1)}),function(e,t){"use strict";"function"==typeof define&&define.amd?define(["angular","sweetalert"],t):"object"==typeof module&&module.exports?module.exports=t(require("angular"),require("sweetalert")):t(e.angular,e.swal)}(this,function(e,t){"use strict";e.module("oitozero.ngSweetAlert",[]).factory("SweetAlert",["$rootScope",function(e){return{swal:function(n,r,o){e.$evalAsync(function(){"function"==typeof r?t(n,function(t){e.$evalAsync(function(){r(t)})},o):t(n,r,o)})},success:function(n,r){e.$evalAsync(function(){t(n,r,"success")})},error:function(n,r){e.$evalAsync(function(){t(n,r,"error")})},warning:function(n,r){e.$evalAsync(function(){t(n,r,"warning")})},info:function(n,r){e.$evalAsync(function(){t(n,r,"info")})},showInputError:function(n){e.$evalAsync(function(){t.showInputError(n)})},close:function(){e.$evalAsync(function(){t.close()})}}}])});