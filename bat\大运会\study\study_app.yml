io.swagger.enabled: false
server:
  port: 8025
spring:
  servlet:
    multipart:
      max-request-size: 10MB
      max-file-size: 10MB
  main:
    banner-mode: off
  application:
    name: train-app
  datasource:
    url: ******************************************************************************************************************
    username: dingding
    password: 1qaz2wsx
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 5
      maximum-pool-size: 50
      auto-commit: true
      idle-timeout: 30000
      pool-name: DatebookHikariCP
      max-lifetime: 600000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  redis:
    #host: r-ww69f13ae47defb4.redis.rds.cdtocc-inc.gov
    host: *************
    port: 6379
    password: 1qaz2WSX
    database: 1
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      fail-on-empty-beans: false
  groovy:
    template:
      check-template-location: false

mybatis-plus:
  mapper-locations: classpath:/mapper/*Mapper.xml
  global-config:
    banner: false
    db-config:
      id-type: auto
      table-underline: true
      logic-delete-value: 1
      logic-not-delete-value: 0
  configuration:
    map-underscore-to-camel-case: true
    auto-mapping-behavior: full
    cache-enabled: false


logging:
  config: /root/app/study_app/logback-spring.xml
  level:
    org.springframework.context.support.ResourceBundleMessageSource: ERROR


exception.enable-global-exception-handler: false


com:
  yunzb123:
    log:
      log-len: 1024
  cloudpay:
    dingtalk:
      corp-id: ding24267832f03477a3f5bf40eda33b7ba0
      app-key: dingpzntsy2pbp5cqyce
      app-secret: OwfYhK5LAcae75wB7ia6CERsAobf21_oDm-g2i_Pq8M4gv43zCnJ8Me5J1fM5oQX
      agent-id: 1163008726
      js-api-url: 'http://************:6843/'
      token: f4188c5fc8c79c9276ddd96a37ddd888
      aes-key: 7k6N9PDNTVnrSGbGfX9vKVN55kBtSYfLwwXOfN6DV88
      callback-url: http://************:8025/callback
    auth:
      access-keys:
        - 2156cff7e68f30eaaa0d8513e57c2ca9
#      ips-white:
#        - ***********/24
#        - ************
      exclude-paths:
        - /token
        - /access/js-api-sign
        - /access/js-api-sign
        - /callback
        - /media/**
    aliyun-oss:
      #endpoint: oss-cn-chengdu-tocc-d01-a.cdtocc-inc.gov
      endpoint: ***********
      bucket: dd-dyh-learn
      expire: 900
      key: h7EMVGjFp9lNxYAi
      secret: r620uPH02gpCApdQ020LuYAwWwY5if

    upload-path: /srv/data/media/
