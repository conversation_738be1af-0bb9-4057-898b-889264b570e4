/**
 * 基础Web Component类
 * 提供Shadow DOM、样式注入、生命周期管理等基础功能
 */

export interface ComponentOptions {
  /** 组件标签名 */
  tagName: string;
  /** 组件样式 */
  styles?: string;
  /** 是否使用Shadow DOM */
  useShadowDOM?: boolean;
  /** Shadow DOM模式 */
  shadowMode?: 'open' | 'closed';
}

export interface ComponentState {
  [key: string]: any;
}

export interface ComponentEvents {
  [key: string]: (...args: any[]) => void;
}

/**
 * 基础Web Component抽象类
 */
export abstract class BaseComponent<
  TState extends ComponentState = ComponentState,
  TEvents extends ComponentEvents = ComponentEvents
> extends HTMLElement {
  protected shadowRoot: ShadowRoot | null = null;
  protected container: HTMLElement;
  protected state: TState;
  protected events: TEvents;
  protected options: ComponentOptions;
  
  private _isConnected = false;
  private _styleElement: HTMLStyleElement | null = null;

  constructor(state: TState, events: TEvents, options: ComponentOptions) {
    super();
    
    this.state = state;
    this.events = events;
    this.options = {
      useShadowDOM: true,
      shadowMode: 'open',
      ...options
    };

    this.initializeComponent();
  }

  /**
   * 初始化组件
   */
  private initializeComponent(): void {
    // 创建Shadow DOM或使用普通DOM
    if (this.options.useShadowDOM) {
      this.shadowRoot = this.attachShadow({ mode: this.options.shadowMode! });
      this.container = this.shadowRoot;
    } else {
      this.container = this;
    }

    // 注入样式
    if (this.options.styles) {
      this.injectStyles(this.options.styles);
    }

    // 创建模板
    this.render();
    
    // 绑定事件
    this.bindEvents();
  }

  /**
   * 注入样式
   */
  protected injectStyles(styles: string): void {
    this._styleElement = document.createElement('style');
    this._styleElement.textContent = styles;
    this.container.appendChild(this._styleElement);
  }

  /**
   * 更新样式
   */
  protected updateStyles(styles: string): void {
    if (this._styleElement) {
      this._styleElement.textContent = styles;
    } else {
      this.injectStyles(styles);
    }
  }

  /**
   * 渲染组件模板 - 子类必须实现
   */
  protected abstract render(): void;

  /**
   * 绑定事件 - 子类可选实现
   */
  protected bindEvents(): void {
    // 默认空实现，子类可重写
  }

  /**
   * 更新状态
   */
  protected setState(newState: Partial<TState>): void {
    const oldState = { ...this.state };
    Object.assign(this.state, newState);
    this.onStateChange(oldState, this.state);
  }

  /**
   * 状态变化回调 - 子类可重写
   */
  protected onStateChange(oldState: TState, newState: TState): void {
    // 默认重新渲染
    this.rerender();
  }

  /**
   * 重新渲染
   */
  protected rerender(): void {
    // 清空容器内容（保留样式）
    const children = Array.from(this.container.children);
    children.forEach(child => {
      if (child !== this._styleElement) {
        child.remove();
      }
    });

    // 重新渲染
    this.render();
    this.bindEvents();
  }

  /**
   * 查找元素 - 支持Shadow DOM
   */
  protected querySelector<T extends Element = Element>(selector: string): T | null {
    return this.container.querySelector<T>(selector);
  }

  /**
   * 查找所有元素 - 支持Shadow DOM
   */
  protected querySelectorAll<T extends Element = Element>(selector: string): NodeListOf<T> {
    return this.container.querySelectorAll<T>(selector);
  }

  /**
   * 创建元素
   */
  protected createElement<K extends keyof HTMLElementTagNameMap>(
    tagName: K,
    options?: {
      className?: string;
      textContent?: string;
      innerHTML?: string;
      attributes?: Record<string, string>;
    }
  ): HTMLElementTagNameMap[K] {
    const element = document.createElement(tagName);
    
    if (options) {
      if (options.className) element.className = options.className;
      if (options.textContent) element.textContent = options.textContent;
      if (options.innerHTML) element.innerHTML = options.innerHTML;
      if (options.attributes) {
        Object.entries(options.attributes).forEach(([key, value]) => {
          element.setAttribute(key, value);
        });
      }
    }
    
    return element;
  }

  /**
   * 派发自定义事件
   */
  protected dispatchCustomEvent<T = any>(
    eventName: string, 
    detail?: T, 
    options?: EventInit
  ): boolean {
    const event = new CustomEvent(eventName, {
      detail,
      bubbles: true,
      cancelable: true,
      ...options
    });
    return this.dispatchEvent(event);
  }

  /**
   * 生命周期：连接到DOM
   */
  connectedCallback(): void {
    this._isConnected = true;
    this.onConnected();
  }

  /**
   * 生命周期：从DOM断开
   */
  disconnectedCallback(): void {
    this._isConnected = false;
    this.onDisconnected();
  }

  /**
   * 生命周期：属性变化
   */
  attributeChangedCallback(name: string, oldValue: string | null, newValue: string | null): void {
    this.onAttributeChanged(name, oldValue, newValue);
  }

  /**
   * 连接回调 - 子类可重写
   */
  protected onConnected(): void {
    // 默认空实现
  }

  /**
   * 断开回调 - 子类可重写
   */
  protected onDisconnected(): void {
    // 默认空实现
  }

  /**
   * 属性变化回调 - 子类可重写
   */
  protected onAttributeChanged(name: string, oldValue: string | null, newValue: string | null): void {
    // 默认空实现
  }

  /**
   * 获取观察的属性 - 子类可重写
   */
  static get observedAttributes(): string[] {
    return [];
  }

  /**
   * 检查是否已连接
   */
  get isConnected(): boolean {
    return this._isConnected;
  }

  /**
   * 获取当前状态
   */
  getState(): Readonly<TState> {
    return { ...this.state };
  }

  /**
   * 销毁组件
   */
  destroy(): void {
    if (this.parentNode) {
      this.parentNode.removeChild(this);
    }
  }
}

/**
 * 组件注册装饰器
 */
export function Component(options: ComponentOptions) {
  return function <T extends typeof BaseComponent>(constructor: T) {
    // 注册自定义元素
    if (!customElements.get(options.tagName)) {
      customElements.define(options.tagName, constructor);
    }
    return constructor;
  };
}
