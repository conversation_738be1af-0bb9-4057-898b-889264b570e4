import req from './request.js'
import crypto from 'crypto'
function sha256(message, secret = '', encoding) {
    const hmac = crypto.createHmac('sha256', secret)
    return hmac.update(message).digest(encoding)
}
function getHash(message, encoding = 'base64') {
    const hash = crypto.createHash('sha256')
    return hash.update(message).digest(encoding)
}

export default class XunFly {
    constructor() {
        this.option = {
            hostUrl:
                "https://api.xf-yun.com/v1/private/s00b65163",
            host: "webapi.xfyun.cn",
            appId: '2defdef8',
            apiKey: '2586883be1cd40478350d48e8cde1ce1',
            apiSecret: 'YmMzY2FmMDA2NGI0ZDhiMTIyNTBhMjMz',
        }
    }
    async load() {
        let url = `http://xf.aka.today/v3/user_info.php?open_id=1cced4e925ab1f7be4f0436fcbedf132`

        let resp = await req.get(url)
        console.log(resp.data.all_share);
    }

    getApiUrl() {
        const d = this.option;
        let e = new URL(d.hostUrl)
        let t = e.hostname
        let n = e.pathname
        let a = new Date().toUTCString()
        //let a = 'Sun, 11 Dec 2022 13:55:02 GMT'
        let o = `host: ${t}\ndate: ${a}\nPOST ${n} HTTP/1.1`
        //let i = r.HmacSHA256(o, d.apiSecret)

        let i = sha256(o, d.apiSecret)
        let c = Buffer.from(i).toString('base64')
        let l = `api_key="${d.apiKey}", algorithm="hmac-sha256", headers="host date request-line", signature="${c}"`

        return `${d.hostUrl}?host=${t}&date=${encodeURI(a)}&authorization=${Buffer.from(l).toString('base64')}`
    }
    async req({
        img64
    }) {
        let api_url = this.getApiUrl()
        let api_id = this.option.app_id

        let error = {
            success: false,
            message: '请求解析失败',
        }
        try {
            let resp = await req.post(api_url, {
                headers: {
                    'content-type': 'application/json',
                    'api_id': api_id
                },
                json:{"header":{"app_id":"2defdef8","status":3},"parameter":{"s00b65163":{"category":"mix0","result":{"encoding":"utf8","compress":"raw","format":"json"}}},"payload":{"s00b65163_data_1":{"encoding":"png","image":img64,"status":3}}}
            })
            let text = resp?.payload?.result?.text
            if (resp && resp.header['code'] === 0 && text) {
                let result = JSON.parse(Buffer.from(text, 'base64').toString())
                let pages = result?.pages
                if (pages && pages.length > 0) {
                    let result = pages[0]?.lines[0]?.words[0]?.content
                    if (result) {
                        return {
                            success: true,
                            result
                        }
                    }

                }

            }

        } catch (e) {
        }
        return error

    }
}
