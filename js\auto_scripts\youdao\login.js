const MD5 = require("./md5");
const req = require("../../simple_req");

let default_acc = {
  username: "yiye<PERSON><EMAIL>",
  password: "m4Q6$$BDsh"
};
 class NeteaseLogin {
  constructor(conf = {}) {
    this.params = Object.assign({}, default_acc, conf);
    this.md5Password();
  }
  md5Password() {
    var pswd = this.params.password,
      username = this.params.username;
    if (0 !== pswd.length) {
      if (pswd.length > 16) {
        var domain = username.substring(username.lastIndexOf("@") + 1);
        "126.com" != domain &&
          "vip.163.com" != domain &&
          "188.com" != domain &&
          (pswd = pswd.substring(0, 16));
      }

      this.params.password = MD5.MD5(pswd);
    }
  }
  generateUrl() {
    //let default_params = "app=android&product=YNOTE&tp=urstoken&cf=7&show=false&systemName=android&systemVersion=29&resolution=2210*1080&pdtVersion=6.8.0&mac=210856a1c5b5aebb&deviceType=android&os=android&os_ver=29&device_name=Redmi+K20+Pro&device_model=Redmi+K20+Pro&device_id=android-3a809edc-373e-4c4c-81dd-70ae52eb5c10-1591076408479&client_ver=6.8.0&device_type=android"

    let default_params = "app=web&product=YNOTE&tp=urstoken&cf=7&show=false&systemName=Windows&systemVersion=29&resolution=2210*1080&pdtVersion=6.8.0&mac=210856a1c5b5aebb&deviceType=WindowsPC&os=Windows&os_ver=29&device_name=WindowsPC&device_modelWindowsPC&device_id=Windows-3a809edc-373e-4c4c-81dd-70ae52eb5c10-1591076408479&client_ver=6.8.0&device_type=WindowsPC"
    let login_url =
      "https://note.youdao.com/login/acc/login";
    let auth_url = "https://note.youdao.com/login/acc/urs/verify/check";
    this.login_url = login_url + "?" + default_params;
    this.auth_url = auth_url + "?" + default_params;
  }
  async login() {
    this.generateUrl();
    let resp = await req.post(this.auth_url,
      {
        headers: { Cookie: 'YNOTE_URS_VERIFY=VCEYMdSdNVzEk4wL0fPL0zMPLqLnMOY0wFOfe4nHOl0kE6LzMO4PB0YMk4lWnLPLRQLnMJK0fUGRpLnLO50Mzm0z5k4q464kl0;' },
        form: { username: '<EMAIL>', password: '085b3e61914395bd654b4e9bd0f4cc06' }
      })
    console.log(resp)
    let cookies = resp.headers["set-cookie"];
    let _cookies = cookies.join("; ");
    resp = await req.get(this.login_url, { headers: { Cookie: _cookies, 'User-Agent': 'okhttp/3.10.0' } });
    return r.headers["set-cookie"]
  }
};
module.exports =NeteaseLogin