declare -x OVPN_AUTH=
declare -x OVPN_CIPHER=
declare -x OVPN_CLIENT_TO_CLIENT=
declare -x OVPN_CN=VPN.SERVERNAME.COM
declare -x OVPN_COMP_LZO=0
declare -x OVPN_DEFROUTE=0
declare -x OVPN_DEVICE=tun
declare -x OVPN_DEVICEN=0
declare -x OVPN_DISABLE_PUSH_BLOCK_DNS=1
declare -x OVPN_DNS=1
declare -x OVPN_DNSMASQ=0
declare -x OVPN_DNS_SERVERS=([0]="*************")
declare -x OVPN_ENV=/etc/openvpn/ovpn_env.sh
declare -x OVPN_EXTRA_CLIENT_CONFIG=()
declare -x OVPN_EXTRA_SERVER_CONFIG=()
declare -x OVPN_FRAGMENT=
declare -x OVPN_KEEPALIVE='10 60'
declare -x OVPN_MTU=
declare -x OVPN_NAT=1
declare -x OVPN_PORT=1194
declare -x OVPN_PROTO=tcp
declare -x OVPN_PUSH=([0]="route *********** *************")
declare -x OVPN_ROUTES=()
declare -x OVPN_SERVER=*************/24
declare -x OVPN_SERVER_URL=tcp://VPN.SERVERNAME.COM:1194
declare -x OVPN_TLS_CIPHER=
