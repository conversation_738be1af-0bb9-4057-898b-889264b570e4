version: '3.7'
services:
  nginx:
    image: nginx:alpine
    ports:
      - 80:80
    environment:
      - TZ=Asia/Shanghai
    configs:
      - source: nginx_config
        target: /etc/nginx/nginx.conf
    volumes:
      - /root/docker/nginx/conf.d:/etc/nginx/conf.d
      - /root/docker/nginx/html:/usr/share/nginx/html
    logging:
      driver: "json-file"
      options:
        max-size: "1m"

configs:
  nginx_config:
    external: true