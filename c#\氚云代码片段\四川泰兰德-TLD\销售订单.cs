
using System;
using System.Collections.Generic;
using System.Text;
using H3;

public class D149913Sno9vwxo1ym57slwtoxwojn4h5: H3.SmartForm.SmartFormController
{
    public D149913Sno9vwxo1ym57slwtoxwojn4h5(H3.SmartForm.SmartFormRequest request): base(request)
    {
    }

    protected override void OnLoad(H3.SmartForm.LoadSmartFormResponse response)
    {
        base.OnLoad(response);
    }

    protected override void OnSubmit(string actionName, H3.SmartForm.SmartFormPostValue postValue, H3.SmartForm.SubmitSmartFormResponse response)
    {
        base.OnSubmit(actionName, postValue, response);
    }

    protected override void OnWorkflowInstanceStateChanged(H3.Workflow.Instance.WorkflowInstanceState oldState, H3.Workflow.Instance.WorkflowInstanceState newState)
    {

        //流程审批结束,业务规则执行。还有个审批流程结束（finished状态下直接删除），就直接删除也会触发业务规则执行。在submit里面处理
        if(oldState == H3.Workflow.Instance.WorkflowInstanceState.Running && newState == H3.Workflow.Instance.WorkflowInstanceState.Finished)
        {
            //获取销售订单
            string saleOrderId = this.Request.BizObjectId;
            H3.DataModel.BizObject saleOrder = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId,
                this.Engine, "D149913Sno9vwxo1ym57slwtoxwojn4h5", saleOrderId, false);
            string defaultCreater = "2946cda7-f8b8-4a31-b304-c1bc6dd4c89a"; //技术中心总监
            //生成、材料计划
            H3.DataModel.BizObjectSchema productPlanSchema = this.Engine.BizObjectManager.GetPublishedSchema("D149913e2a3fc9936094a4d9fb7c643558f78c3");
            H3.DataModel.BizObject productPlan = new H3.DataModel.BizObject(this.Engine, productPlanSchema, defaultCreater);

            bool isCreateProduct = true;
            //查询生成、材料计划
            H3.Data.Filter.Filter productPlanFilter = new H3.Data.Filter.Filter();
            productPlanFilter.Matcher = new H3.Data.Filter.ItemMatcher("F0000004", H3.Data.ComparisonOperatorType.Equal, this.Request.BizObjectId);
            H3.DataModel.BizObject[] productPlans = H3.DataModel.BizObject.GetList(this.Engine, defaultCreater,
                this.Engine.BizObjectManager.GetPublishedSchema("D149913e2a3fc9936094a4d9fb7c643558f78c3"),
                H3.DataModel.GetListScopeType.GlobalAll, productPlanFilter);
            if(productPlans != null && productPlans.Length > 0)
            {
                productPlan = productPlans[0];
                isCreateProduct = false;
            }

            productPlan["F0000003"] = "销售"; //销售、囤货
            List < H3.DataModel.BizObject > productDetailsOfProductPlan = new List<H3.DataModel.BizObject>();
            List < H3.DataModel.BizObject > materialDetailsOfProductPlan = new List<H3.DataModel.BizObject>();
            H3.DataModel.BizObject[] productDetails = (H3.DataModel.BizObject[]) this.Request.BizObject["D149913Fcvpzd1vuqbtxl9374rmx6vl93"];

            if(productDetails != null && productDetails.Length > 0)
            {
                foreach(H3.DataModel.BizObject productDetail in productDetails)
                {

                    string productId = productDetail["F0000003"] + string.Empty;

                    //查询库存
                    string sqlpo = "select F0000006,F0000014 from i_D149913b779923207a14917a7e6b909ddcf3863 where F0000001 = '" + productId + "'";
                    System.Data.DataTable stockTable = this.Request.Engine.Query.QueryTable(sqlpo, null);
                    int stockNum = 0;
                    int warningNum = 0;
                    if(stockTable != null && stockTable.Rows.Count > 0)
                    {
                        stockNum = Convert.ToInt32(String.IsNullOrEmpty(stockTable.Rows[0]["F0000006"] + "") ? "0" + stockTable.Rows[0]["F0000006"] : stockTable.Rows[0]["F0000006"]);
                        warningNum = Convert.ToInt32(String.IsNullOrEmpty(stockTable.Rows[0]["F0000014"] + "") ? "0" + stockTable.Rows[0]["F0000014"] : stockTable.Rows[0]["F0000014"]);
                    }

                    int useNum = Convert.ToInt32(productDetail["F0000006"]);
                    int avaiableNum = stockNum - warningNum;
                    //库存可用量 与待使用量比较
                    if(useNum > avaiableNum)
                    {
                        int purchaseNum = useNum - avaiableNum;
                        if(avaiableNum < 0)
                        { // 库存小于0 ， 则采购数为当前待使用数
                            purchaseNum = useNum;
                        }
                        string sqlprduct = "select p.F0000005 productType,s.F0000001 className from i_D149913de32da0b7b9342ae9ab01d801d2897f8 p left join " +
                            "i_D1499138437ac8929ce42a48177ef8ab71e1e16 s on p.F0000018 = s.ObjectId where p.ObjectId = '" + productId + "'";
                        System.Data.DataTable productTable = this.Request.Engine.Query.QueryTable(sqlprduct, null);
                        if(productTable != null && productTable.Rows.Count > 0)
                        {
                            string className = productTable.Rows[0]["className"] + string.Empty;
                            if(className == "售后维修") continue;
                            string productType = productTable.Rows[0]["productType"] + string.Empty;
                            if(productType == "流水")
                            {
                                // 查询采购计划是否已存在
                                H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();  //构建过滤器
                                H3.Data.Filter.And andMatcher = new H3.Data.Filter.And();    //构造And匹配器
                                andMatcher.Add(new H3.Data.Filter.ItemMatcher("F0000011", H3.Data.ComparisonOperatorType.Equal, saleOrderId));
                                andMatcher.Add(new H3.Data.Filter.ItemMatcher("F0000018", H3.Data.ComparisonOperatorType.Equal, productId));
                                filter.Matcher = andMatcher;
                                H3.DataModel.BizObjectSchema purchasePlanSchema = this.Engine.BizObjectManager.GetPublishedSchema("D149913Sh59f0tsbpx9bnw37i9p1y5hk3");

                                H3.DataModel.BizObject[] purchasePlans = H3.DataModel.BizObject.GetList(this.Engine, this.Request.UserContext.UserId,
                                    purchasePlanSchema, H3.DataModel.GetListScopeType.GlobalAll, filter);
                                H3.DataModel.BizObject purchasePlan = null;
                                bool isNew = true;
                                if(purchasePlans != null && purchasePlans.Length > 0)
                                {
                                    purchasePlan = purchasePlans[0];
                                    isNew = false;
                                }
                                else
                                {
                                    purchasePlan = new H3.DataModel.BizObject(this.Engine, purchasePlanSchema, this.Request.UserContext.UserId);
                                }

                                purchasePlan["F0000016"] = "销售订单"; //来源
                                purchasePlan["F0000017"] = saleOrder["SeqNo"]; //来源单号
                                purchasePlan["F0000011"] = saleOrderId; //销售订单

                                purchasePlan["F0000018"] = productId; //产品信息
                                purchasePlan["F0000026"] = productDetail["F0000080"];//品牌
                                purchasePlan["F0000021"] = productDetail["F0000005"];//规格型号
                                purchasePlan["F0000022"] = productDetail["F0000036"];//单位
                                purchasePlan["F0000023"] = purchaseNum; //需求数量
                                purchasePlan["F0000025"] = purchaseNum;//剩余采购数量
                                purchasePlan["F0000028"] = productDetail["F0000101"];//产品小类
                                purchasePlan["F0000029"] = saleOrder["F0000018"];// 交货日期
                                purchasePlan.Status = H3.DataModel.BizObjectStatus.Effective; //设置状态生效
                                H3.ErrorCode code = isNew ? purchasePlan.Create() : purchasePlan.Update();
                            }
                            else
                            {
                                //生产、材料计划中产品明细
                                H3.DataModel.BizObject productDetailOfProductPlan = new H3.DataModel.BizObject(this.Engine,
                                    productPlanSchema.GetChildSchema("D149913F218d9ae7744a48d099f49a592d21b3f3"), defaultCreater);//子表对象
                                productDetailOfProductPlan["F0000005"] = productId;//产品信息
                                productDetailOfProductPlan["F0000006"] = purchaseNum; //需求数量
                                productDetailOfProductPlan["F0000007"] = productDetail["F0000036"]; //单位
                                productDetailOfProductPlan["F0000010"] = "采购"; //制造、采购
                                productDetailOfProductPlan["F0000012"] = productDetail["F0000005"]; //规格型号
                                productDetailOfProductPlan["F0000018"] = productDetail["F0000080"];//品牌
                                productDetailOfProductPlan["F0000021"] = purchaseNum; //需采购数量

                                productDetailsOfProductPlan.Add(productDetailOfProductPlan);


                                //查询bom单
                                H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();
                                filter.Matcher = new H3.Data.Filter.ItemMatcher("F0000001", H3.Data.ComparisonOperatorType.Equal, productId);
                                H3.DataModel.BizObject[] bomOrders = H3.DataModel.BizObject.GetList(this.Engine, this.Request.UserContext.UserId,
                                    this.Engine.BizObjectManager.GetPublishedSchema("D14991310b7cff7d2a443b19e8ba19a48994509"),
                                    H3.DataModel.GetListScopeType.GlobalAll, filter);
                                if(bomOrders != null && bomOrders.Length > 0)
                                {
                                    H3.DataModel.BizObject bomOrder = bomOrders[0];
                                    H3.DataModel.BizObject[] demandDetails = (H3.DataModel.BizObject[]) bomOrder["D149913Fa5ace13d63c24085a595142f424a9826"];
                                    if(demandDetails != null && demandDetails.Length > 0)
                                    {
                                        foreach(H3.DataModel.BizObject demandDetail in demandDetails)
                                        {
                                            H3.DataModel.BizObject exist = materialDetailsOfProductPlan.Find(e => Convert.ToString(e["F0000005"]) == Convert.ToString(demandDetail["F0000002"]));
                                            if(exist == null)
                                            {
                                                //生产、材料计划中材料明细
                                                exist = new H3.DataModel.BizObject(this.Engine,
                                                    productPlanSchema.GetChildSchema("D149913aa630cf1629249aa8263f6959cf8336f"),
                                                    defaultCreater);
                                                exist["F0000005"] = demandDetail["F0000002"];//产品信息
                                                exist["F0000006"] = Convert.ToInt32(demandDetail["F0000005"]) * purchaseNum;//需求数量
                                                exist["F0000007"] = demandDetail["F0000013"];//单位
                                                exist["F0000012"] = demandDetail["F0000007"];//规格型号
                                                exist["F0000019"] = demandDetail["F0000012"];//品牌
                                                exist["F0000024"] = demandDetail["F0000010"];//备注
                                                materialDetailsOfProductPlan.Add(exist);
                                            }
                                            else
                                            {
                                                int num = Convert.ToInt32(exist["F0000006"]);
                                                exist["F0000006"] = num + Convert.ToInt32(demandDetail["F0000005"]) * purchaseNum;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                if(productDetailsOfProductPlan.Count > 0)
                {
                    productPlan["CreatedBy"] = defaultCreater;//创建人
                    productPlan["OwnerId"] = defaultCreater;
                    productPlan["F0000017"] = saleOrder["F0000018"];// 交货日期
                    productPlan["F0000004"] = saleOrderId;//销售订单
                    productPlan["F0000026"] = DateTime.Now;//创建日期
                    productPlan["F0000001"] = saleOrder["F0000002"];//客户信息
                    productPlan["D149913F218d9ae7744a48d099f49a592d21b3f3"] = productDetailsOfProductPlan.ToArray();
                    productPlan["D149913aa630cf1629249aa8263f6959cf8336f"] = materialDetailsOfProductPlan.ToArray();
                    //复制技术方案附件到产品计划
                    this.Request.Engine.BizObjectManager.CopyFiles("D149913Sno9vwxo1ym57slwtoxwojn4h5", "", "F0000091", saleOrderId,
                        "D149913e2a3fc9936094a4d9fb7c643558f78c3", "", "F0000064", productPlan.ObjectId, true, true);
                    //复制项目文件附件到产品计划
                    this.Request.Engine.BizObjectManager.CopyFiles("D149913Sno9vwxo1ym57slwtoxwojn4h5", "", "F0000099", saleOrderId,
                        "D149913e2a3fc9936094a4d9fb7c643558f78c3", "", "F0000047", productPlan.ObjectId, true, true);
                    //复制项目预设计附件到产品计划
                    this.Request.Engine.BizObjectManager.CopyFiles("D149913Sno9vwxo1ym57slwtoxwojn4h5", "", "F0000100", saleOrderId,
                        "D149913e2a3fc9936094a4d9fb7c643558f78c3", "", "F0000046", productPlan.ObjectId, true, true);
                    if(isCreateProduct)
                    {
                        string instanceId = System.Guid.NewGuid().ToString(); // 创建流程id
                        productPlan.WorkflowInstanceId = instanceId;
                        productPlan.Create();  //创建表单

                        //发起流程
                        H3.Workflow.Instance.WorkflowInstance wfInstance = this.Request.Engine.WorkflowInstanceManager.GetWorkflowInstance(productPlan.WorkflowInstanceId);
                        string workItemID = string.Empty;
                        string errorMsg = string.Empty;
                        H3.Workflow.Template.WorkflowTemplate wfTemp = this.Request.Engine.WorkflowTemplateManager.GetDefaultWorkflow(productPlan.Schema.SchemaCode);
                        this.Request.Engine.Interactor.OriginateInstance(defaultCreater, productPlan.Schema.SchemaCode,
                            wfTemp.WorkflowVersion, productPlan.ObjectId, productPlan.WorkflowInstanceId, H3.Workflow.WorkItem.AccessMethod.Web,
                            false, // 是否提交流程操作
                            string.Empty, true, out workItemID, out errorMsg);
                    }
                    else
                    {
                        productPlan.Update();  //创建表单
                    }
                }
            }
        }
        base.OnWorkflowInstanceStateChanged(oldState, newState);
    }
}