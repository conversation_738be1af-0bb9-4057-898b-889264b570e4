
using System;
using System.Collections.Generic;
using System.Text;
using H3;

public class D282605slaeafldkkk5vpmkqrfu: H3.SmartForm.SmartFormController
{
    public D282605slaeafldkkk5vpmkqrfu(H3.SmartForm.SmartFormRequest request): base(request)
    {
    }

    protected override void OnLoad(H3.SmartForm.LoadSmartFormResponse response)
    {
        base.OnLoad(response);
    }

    protected override void OnSubmit(string actionName, H3.SmartForm.SmartFormPostValue postValue, H3.SmartForm.SubmitSmartFormResponse response)
    {
        H3.DataModel.BizObject[] details = (H3.DataModel.BizObject[]) this.Request.BizObject["D282605swosjgj8vtucxqgo6vilc"];
        if(details != null && details.Length > 0)
        {
            Dictionary < string, H3.DataModel.BizObject > map = new Dictionary<string, H3.DataModel.BizObject>();


            foreach(H3.DataModel.BizObject detail in details)
            {
                string orderDeliveryDate = detail["F0000038"] + string.Empty;

                string status = detail["F0000064"] + string.Empty;
                if(!string.IsNullOrWhiteSpace(status))
                {
                    continue;
                }

                if(string.IsNullOrWhiteSpace(orderDeliveryDate))
                {
                    detail["F0000064"] = "交期不详";
                } else
                {
                    DateTime odDate = Convert.ToDateTime(orderDeliveryDate);
                    string sprayPackageDate = detail["F0000058"] + string.Empty;
                    if(string.IsNullOrWhiteSpace(sprayPackageDate))
                    {
                        DateTime now = DateTime.Now;
                        if(now > odDate)
                        {

                            detail["F0000064"] = "超期未完";
                        }
                    } else
                    {
                        DateTime spDate = Convert.ToDateTime(sprayPackageDate);

                        if(spDate > odDate)
                        {
                            detail["F0000064"] = "超期完成";
                        } else
                        {
                            detail["F0000064"] = "按期完成";
                        }
                    }
                }
                // 订单编号 + 产品名称 + BomBom 序号
                if(string.IsNullOrWhiteSpace(detail["F0000024"] + "")
                    || string.IsNullOrWhiteSpace(detail["F0000027"] + "")
                    || string.IsNullOrWhiteSpace(detail["F0000045"] + "")) {
                        continue;
                }
                string key = detail["F0000024"] + "_" + detail["F0000027"] + "_" + detail["F0000045"];
                if(map.ContainsKey(key))
                {
                    //数量
                    H3.DataModel.BizObject target = map[key];
                    target["F0000054"] = Convert.ToInt32(string.IsNullOrWhiteSpace(target["F0000054"] + "") ? "0" : target["F0000054"] + "")
                        + Convert.ToInt32(string.IsNullOrWhiteSpace(detail["F0000054"] + "") ? "0" : detail["F0000054"] + "");
                    //总重
                    target["F0000034"] = Convert.ToDouble(string.IsNullOrWhiteSpace(target["F0000034"] + "") ? "0" : target["F0000034"] + "")
                        + Convert.ToDouble(string.IsNullOrWhiteSpace(detail["F0000034"] + "") ? "0" : detail["F0000034"] + "");
                    // 面积
                    target["F0000069"] = Convert.ToDouble(string.IsNullOrWhiteSpace(target["F0000069"] + "") ? "0" : target["F0000069"] + "")
                        + Convert.ToDouble(string.IsNullOrWhiteSpace(detail["F0000069"] + "") ? "0" : detail["F0000069"] + "");

                } else
                {
                    map.Add(key, detail);
                }
            }
            H3.DataModel.BizObject[] objs = new H3.DataModel.BizObject[map.Count];
            map.Values.CopyTo(objs, 0);
            this.Request.BizObject["D282605swosjgj8vtucxqgo6vilc"] = objs;
        }
        base.OnSubmit(actionName, postValue, response);
    }
}