export default class Log{
    static records = []

    static append(...items) {
        if(items && items.length > 0) {
            items = items.map(e => {
                if(typeof e !== 'string') e = JSON.stringify(e)
                return e
            })
            this.records.push(items.join(' '))
        }
    }

    static print() {
        console.log(Log.body())
    }

    static body() {
        return this.records.join('\n')
    }
}