<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
	<title>calc</title>

	<link type="text/css" rel="stylesheet" href="./css/normalize.css"></link>
	<link type="text/css" rel="stylesheet" href="./css/bootstrap.min.css"></link>
	<link type="text/css" rel="stylesheet" href="./css/bootstrap-theme.min.css"></link>

	<style>
		.tab-content .panel{
			padding: 15px;
		}
		[role=presentation] input[type=radio] {
			display: none;
		}
		.control-label {
			text-align: right;
			line-height: 34px;
		}
		.result{
			font-size: 15px;
			font-weight: bold;
			position: relative;
		}
		.result+.result{
			margin-top: 10px;
		}
		.result .desc{
			text-align: right;
		}
		.form-control{
			color: #555;
		}
		.result em {
			line-height: 18px;
			height: 18px;
		}
		em {
			    position: absolute;
    			top: 0;
    			right: 25px;
    			height: 36px;
    			line-height: 36px;
    			font-size: 12px;
    			color: rgba(0,0,0,.25);
		}
		.resultRegin{
			margin-top: 20px;
			background-color: #F1F5FF;
			padding-top: 10px;
			padding-bottom: 10px
		}
	</style>

</head>
<body>
	<div class="container-fluid">
		<ul class="nav nav-tabs">
		  <li role="presentation" class="active"><a href="#profitC" aria-controls="profitC" role="tab" data-toggle="tab">收益计算</a></li>
		  <li role="presentation"><a href="#blast" aria-controls="blast" role="tab" data-toggle="tab">爆仓价格计算</a></li>
		</ul>
	</div>
	<div class="tab-content">
		<div  role="tabpanel" id="profitC" class="panel tab-pane active in fade">
			<form class="form-horizontal">
			  <div class="form-group">
			    <label for="type" class="col-sm-2 col-xs-3 control-label">类型</label>
			    <div class="col-sm-10 col-xs-9">
			      <ul class="nav nav-pills">
				    <li role="presentation" class="active">
				    	<a href="#" data-switch>多仓</a>
						<input type="radio" name="type" value="1" checked></input>
				    </li>
				    <li role="presentation">
				    	<a href="#" data-switch >空仓</a>
				    	<input type="radio" name="type" value="-1"></input>
				    </li>
				  </ul>
			    </div>
			  </div>
			  <div class="form-group">
			    <label for="mulriple" class="col-sm-2 col-xs-3 control-label">杠杆倍数</label>
			    <div class="col-sm-10 col-xs-9">
			      <ul class="nav nav-pills">
				    <li role="presentation" class="active">
				    	<a href="#" data-switch>10X</a>
				    	<input type="radio" name="mulriple" value="10" checked></input>
				    </li>
				    <li role="presentation">
				    	<a href="#" data-switch>20X</a>
				    	<input type="radio" name="mulriple" value="20"></input>
				    </li>
				  </ul>
			    </div>
			  </div>
			  <div class="form-group">
			    <label for="openPrice" class="col-sm-2 col-xs-3 control-label">开仓价格</label>
			    <div class="col-sm-10 col-xs-9">
			      <input type="text" class="form-control" name="open_price" placeholder="开仓价格">
			      <em>USD</em>
			    </div>
			  </div>

			  <div class="form-group">
			    <label for="closePrice" class="col-sm-2 col-xs-3 control-label">平仓价格</label>
			    <div class="col-sm-10 col-xs-9">
			      <input type="text" class="form-control" name="close_price" placeholder="平仓价格">
			      <em>USD</em>
			    </div>
			  </div>

			  <div class="form-group">
			    <label for="openCount" class="col-sm-2 col-xs-3 control-label">开仓数量</label>
			    <div class="col-sm-10 col-xs-9">
			      <input type="text" class="form-control" name="open_count" placeholder="开仓数量">
			      <em>EOS</em>
			    </div>
			  </div>

			  <div class="form-group">
			    <div class="col-sm-offset-2 col-xs-offset-3 col-sm-10">
			      <button type="button" id="profitCalc" class="btn btn-default">开始计算</button>
			    </div>
			  </div>
			</form>
			<div id="profit-result" class="resultRegin">
				<div class="result row">
					<div class="desc col-sm-5 col-xs-5">保证金:      </div><div class="col-sm-7 col-xs-7" id="guaranty"></div>
					<em >EOS</em>
				</div>
				<div class="result row">
					<div class="desc col-sm-5 col-xs-5">收益:        </div><div class="col-sm-7 col-xs-7" id="profit"></div>
					<em >EOS</em>
				</div>
				<div class="result row">
					<div class="desc col-sm-5 col-xs-5">收益率:      </div><div class="col-sm-7 col-xs-7" id="profitRate"></div>
					<em >%</em>
				</div>
				<div class="result row">
					<div class="desc col-sm-5 col-xs-5">Maker手续费: </div><div class="col-sm-7 col-xs-7" id="makerFee"></div>
					<em >EOS</em>
				</div>
				<div class="result row">
					<div class="desc col-sm-5 col-xs-5">Taker手续费: </div><div class="col-sm-7 col-xs-7" id="takerFee"></div>
					<em >EOS</em>
				</div>
			</div>
		</div>
		<div  role="tabpanel" id="blast" class="panel tab-pane fade">
			<form class="form-horizontal">
			<div class="form-group">
			    <label for="type" class="col-sm-2 col-xs-3 control-label">账户模式</label>
			    <div class="col-sm-10 col-xs-9">
			      <ul class="nav nav-pills">
				    <li role="presentation" class="active">
				    	<a href="#" data-switch>全仓</a>
						<input type="radio" name="b_pattern" value="1" ></input>
				    </li>
				    <li role="presentation">
				    	<a href="#" data-switch >逐仓</a>
				    	<input type="radio" name="b_pattern" value="2" checked></input>
				    </li>
				  </ul>
			    </div>
			  </div>
			  <div class="form-group">
			    <label for="type" class="col-sm-2 col-xs-3 control-label">类型</label>
			    <div class="col-sm-10 col-xs-9">
			      <ul class="nav nav-pills">
				    <li role="presentation" class="active">
				    	<a href="#" data-switch>多仓</a>
						<input type="radio" name="b_type" value="1" checked></input>
				    </li>
				    <li role="presentation">
				    	<a href="#" data-switch >空仓</a>
				    	<input type="radio" name="b_type" value="-1"></input>
				    </li>
				  </ul>
			    </div>
			  </div>
			  <div class="form-group">
			    <label for="mulriple" class="col-sm-2 col-xs-3 control-label">杠杆倍数</label>
			    <div class="col-sm-10 col-xs-9">
			      <ul class="nav nav-pills">
				    <li role="presentation" class="active">
				    	<a href="#" data-switch>10X</a>
				    	<input type="radio" name="b_mulriple" value="10" checked></input>
				    </li>
				    <li role="presentation">
				    	<a href="#" data-switch>20X</a>
				    	<input type="radio" name="b_mulriple" value="20"></input>
				    </li>
				  </ul>
			    </div>
			  </div>
			  <div class="form-group">
			    <label for="openPrice" class="col-sm-2 col-xs-3 control-label">开仓价格</label>
			    <div class="col-sm-10 col-xs-9">
			      <input type="text" class="form-control" name="b_open_price" placeholder="开仓价格">
			      <em>USD</em>
			    </div>
			  </div>

			  <div class="form-group">
			    <label for="openCount" class="col-sm-2 col-xs-3 control-label">开仓数量</label>
			    <div class="col-sm-10 col-xs-9">
			      <input type="text" class="form-control" name="b_open_count" placeholder="开仓数量">
			      <em>EOS</em>
			    </div>
			  </div>

			  <div class="form-group">
			    <label for="closePrice" class="col-sm-2 col-xs-3 control-label">合约余额</label>
			    <div class="col-sm-10 col-xs-9">
			      <input type="text" class="form-control" name="b_balance" placeholder="合约余额">
			      <em>EOS</em>
			    </div>
			  </div>

			  <div class="form-group">
			    <div class="col-sm-offset-2 col-xs-offset-3 col-sm-10">
			      <button type="button" id="blastCalc" class="btn btn-default">开始计算</button>
			    </div>
			  </div>
			</form>
			<div id="profit-result" class="resultRegin">
				<div class="result row">
					<div class="desc col-sm-4 col-xs-4">爆仓价:      </div><div class="col-sm-8 col-xs-8" id="blastPrice"></div>
					<em >USD</em>
				</div>
			</div>
		</div>
	</div>

</body>
<script type="text/javascript" src="./js/jquery-3.3.1.min.js"></script>
<script type="text/javascript" src="./js/bootstrap.min.js"></script>
<script type="text/javascript">
function l(e) {
    if ((e = String(e)).includes("e")) return 0;
    var t = e.indexOf(".");
    return -1 < t ? e.length - (t + 1) : 0
}

function i(e) {
    return String(e).replace(".", "")
}

var Calc = {
	calcAdd: function (e, t) {
        var a = Math.max(l(e), l(t)), n = Math.pow(10, a);
        return 16 < a && (a = 16), +((e * n + t * n) / n).toFixed(a)
    },
    calcSub: function (e, t) {
    	return this.calcAdd(e, -t)
	},
	calcMul: function (e, t) {
    	if (String(e).includes("e") || String(t).includes("e")) return e * t;
    	var a = l(e) + l(t);
    	return i(e) * i(t) / Math.pow(10, a)
	},
	calcDiv:  function (e, t) {
    	if (String(e).includes("e") || String(t).includes("e")) return e / t;
    	var a = l(e), n = l(t);
    	return i(e) / i(t) * Math.pow(10, n - a)
	}
}

var Stat = {
	floor: function (e, t) {
        var a = Math.pow(10, t), n = e < 0 ? -1 : 1;
        return Math.floor(Math.abs(e * a)) / a * n
    },
    round: function (e, t) {
        var a = Math.pow(10, t);
        return Math.ceil(e * a) / a
    }
}



function CalcPrice() {
	this.fCurrencyId = 0;
	this.fUnitAmount = 10;
	this.fCurrencyRate = 1;
	this.makerFee = "-0.00030";
	this.takerFee = "-0.00050";
	this.fTradeUnit = 0;
	this.tradeUnits={
		COIN: 0
	};
	this.fRiskRate ={
		10: 0.1,
		20: 0.2
	};
	this.priceTruncate = 3;
}
CalcPrice.prototype.E = function (e, t) {
	    var a = this.fUnitAmount, n = Math.floor(Calc.calcDiv(Calc.calcMul(e, t), a));
	    return n <= 0 ? 1 : n
	}

CalcPrice.prototype.S = function (e, t) {
	    return 0 < this.fCurrencyId ? t : t = 1 === e ? Stat.round(t, this.priceTruncate) : Stat.floor(t, this.priceTruncate)
	}
CalcPrice.prototype.calcProfit = function (e, t, a, n, r) {
        0 < this.fCurrencyId && (a = Calc.calcDiv(a, this.fCurrencyRate)), n = Calc.calcDiv(n, this.fCurrencyRate);
        var o = r;
        this.fTradeUnit === this.tradeUnits.COIN && 0 !== Number(a) && (o = Stat.floor(r / (this.fUnitAmount / a), 0));
        var l = 0;
        0 !== Number(t) && (l = Calc.calcDiv(1, t));
        var i = this.fUnitAmount, s = 0, c = 0, u = 0;
        0 < a && 0 < o && (s =  Calc.calcMul(Calc.calcMul(Calc.calcDiv(i, a), o), l));
        0 < a && 0 < o && 0 < n && (c = Calc.calcMul(Calc.calcMul(Calc.calcDiv(i, a) - Calc.calcDiv(i, n), o), e));
        0 < a && 0 < n && (u = Calc.calcMul(t, Calc.calcMul(e, Calc.calcAdd(1, -Calc.calcDiv(a, n)))));
        var d = this.makerFee, f = this.takerFee, m = Calc.calcDiv(Calc.calcMul(Calc.calcMul(i, o), d), a),
            p = Calc.calcDiv(Calc.calcMul( Calc.calcMul(i, o), f), a);
        this.fTradeUnit === this.tradeUnits.COIN && (m = Calc.calcMul(r, d), p = Calc.calcMul(r, f));
        var h = m < 0 ? -1 : 1, g = p < 0 ? -1 : 1;
        return {
            counterKeep: Stat.round(s, 4),
            counterMakerFee: Stat.round(Math.abs(m), 5) * h,
            counterTakerFee: Stat.round(Math.abs(p), 5) * g,
            counterProfit: Stat.floor(c, 4),
            counterNetProfit: Calc.calcMul(u, 100)
        }
}
CalcPrice.prototype.calcBlastPrice = function (e, t, a, n, r, o) {
        var l = e, i = t, s = a, c = n;
        "" === c && (c = 0), 0 < this.fCurrencyId && (c /= this.fCurrencyRate);
        var u, d, f, m = (u = s, d = n, f = this.fUnitAmount, Calc.calcMul(Calc.calcDiv(f, d), Calc.calcDiv(1, u)));
        1 === l && (m = o);
        var p = this.fRiskRate[Number(s)], h = r;
        "" === h || h <= 0 ? h = 1 : this.fTradeUnit === this.tradeUnits.COIN && (h = this.E(h, c));
        var g = o, y = this.fUnitAmount, _ = 0;
        if (2 === l) {
            var v = Calc.calcDiv(1, c),
                b = Calc.calcDiv(Calc.calcAdd(g, Calc.calcDiv(Calc.calcMul(y, h), Calc.calcMul(s, c))), Calc.calcMul(Calc.calcMul(i, h), y)),
                k = Calc.calcDiv(p, Calc.calcMul(i, Calc.calcMul(c, s)));
            _ = Calc.calcDiv(1, Calc.calcAdd(v, Calc.calcAdd(b, -k)))
        } else {
            var w = Calc.calcMul(Calc.calcMul(Calc.calcAdd(Calc.calcDiv(p, s), i), y), h),
                C = Calc.calcAdd(m, Calc.calcDiv(Calc.calcMul(Calc.calcMul(i, y), h), c));
            _ = Calc.calcDiv(w, C)
        }
        return _ < 0 && (_ = 0), 0 < this.fCurrencyId && (_ *= this.fCurrencyRate), {counterBlastPrice: this.S(i, _)}
}
var calcPrice = new CalcPrice();
</script>
<script type="text/javascript">
$('[data-switch]').click(function() {
	var parent = $(this).parent('[role=presentation]');
	parent.addClass('active');
	parent.siblings().removeClass('active');

	parent.children('input[type=radio]').prop( "checked",true );
})

var startX;
$('.tab-pane').on('touchstart', function(event){
	startX = event.touches[0].clientX;
})
.on('touchend', function(event) {
	var _startX = event.changedTouches[0].clientX;
	var direction = _startX - startX ;
	console.log(direction);
	if (direction < -100) {
		$('[aria-controls=profitC]').trigger('click');
	}
	if (direction > 100) {
		$('[aria-controls=blast]').trigger('click');
	}
})




$('#profitCalc').click(function() {
	/* Act on the event */
	var e = Number($('input[name=type]:checked').val());
	var t = Number($('input[name=mulriple]:checked').val());
	var a = Number($('input[name=open_price]').val());
	var n = Number($('input[name=close_price]').val());
	var r = Number($('input[name=open_count]').val());
	var result = calcPrice.calcProfit(e,t,a,n,r);
	$('#guaranty').text(result.counterKeep);
	$('#profit').text(result.counterProfit).css('color', result.counterProfit>0?'green':'red');
	$('#profitRate').text(result.counterNetProfit).css('color', result.counterNetProfit>0?'green':'red');
	$('#makerFee').text(result.counterMakerFee);
	$('#takerFee').text(result.counterTakerFee);
});

$('#blastCalc').click(function() {
	/* Act on the event */
	var e = Number($('input[name=b_pattern]:checked').val());
	var t = Number($('input[name=b_type]:checked').val());
	var a = Number($('input[name=b_mulriple]:checked').val());
	var n = Number($('input[name=b_open_price]').val());
	var r = Number($('input[name=b_open_count]').val());
	var o = Number($('input[name=b_balance]').val());
	var result = calcPrice.calcBlastPrice(e,t,a,n,r,o);
	$('#blastPrice').text(result.counterBlastPrice).css('color', 'red');
});
</script>

</html>