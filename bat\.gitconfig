[user]
	email = <EMAIL>
	name = ye
[alias]
	co = checkout
	ci = commit
	s = status
	b = branch
	p = push
	a = add
	m = merge
	r = remote
	last = log -1
	lg = log --color --graph --pretty=format:'%Cred%h%Creset -%C(yellow)%d%Creset %s %Cgreen(%cr) %C(bold blue)<%an>%Creset' --abbrev-commit
	cmp = "!f() { git co master; git a -A && git ci -m $1 && git p; }; f"
	cmpa = "!f() { git co master; git a -A && git ci -m $1 && git p; git co uat && git m master && git p;git co production && git m uat;git p;git co master; }; f"
	cmpp = "!f() { git co uat && git m master && git p;git co production && git m uat;git p;git co master; }; f"
	tst = "!f(){ echo $1; }; f"
