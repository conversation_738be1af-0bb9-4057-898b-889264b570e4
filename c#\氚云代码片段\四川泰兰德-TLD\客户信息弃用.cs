
using System;
using System.Collections.Generic;
using System.Text;
using H3;

public class D149913d77c222c479b4bc0994639a67827eeb0_ListViewController : H3.SmartForm.ListViewController
{
    public D149913d77c222c479b4bc0994639a67827eeb0_ListViewController(H3.SmartForm.ListViewRequest request) : base(request)
    {
    }

    protected override void OnLoad(H3.SmartForm.LoadListViewResponse response)
    {
        base.OnLoad(response);
    }

    protected override void OnSubmit(string actionName, H3.SmartForm.ListViewPostValue postValue, H3.SmartForm.SubmitListViewResponse response)
    {
        if (actionName == "discard")
        {
            string userId = this.Request.UserContext.UserId;
            string[] objectIds = this.Deserialize<string[]>(this.Request["ObjectIds"]);
            if (objectIds == null || objectIds.Length == 0)
            {
                response.Errors.Add("请选择待弃用数据");
                return;
            }

            foreach (string objectId in objectIds)
            {
                //修改客户信息拥有者
                string sql = "update I_D149913d77c222c479b4bc0994639a67827eeb0 set ********='公海'" +
                             " where ObjectId='" + objectId + "'";
                //修改公海状态
                sql += ";update I_D149913Sb5cj1kgr8jav74qk65xeu3800 set ********='公海'" +
                        " where ObjectId='" + objectId + "'";
                this.Engine.EngineConfig.CreateCommand().ExecuteScalar(sql);
            }
            response.Message = "操作成功";
            response.Refresh = true;
            return;
        }
        base.OnSubmit(actionName, postValue, response);
    }
}