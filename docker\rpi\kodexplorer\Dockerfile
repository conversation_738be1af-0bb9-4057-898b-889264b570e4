FROM yiyetianxiang/kodexplorer-base

WORKDIR /var/www/html

#COPY sources.list /etc/apt/sources.list

ARG KODEXPLORER_VERSION=4.36
ARG KODEXPLORER_URL="https://github.com/kalcaddle/KodExplorer/archive/${KODEXPLORER_VERSION}.tar.gz"

RUN  apt-get update

RUN wget -q -O /tmp/kodexplorer.tar.gz "$KODEXPLORER_URL" && \
    tar -zxf /tmp/kodexplorer.tar.gz -C /var/www/html --strip-components=1 && \
    rm -rf /tmp/*

COPY start.sh /home/<USER>

RUN chmod a+x /home/<USER>

EXPOSE 6800

CMD ["/home/<USER>","apache2-foreground"]  

