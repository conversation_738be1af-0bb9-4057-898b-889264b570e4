version: '3.7'
services:
    master1:
      image: redis:alpine
      ports:
        - target: 6379
          published: 6379
          protocol: tcp
          mode: host
        - target: 16379
          published: 16379
          protocol: tcp
          mode: host  
      deploy:
        replicas: 2
      command: 
        - "redis-server"
        - "--cluster-enabled yes"
        - "--cluster-config-file nodes.conf"
        - "--cluster-node-timeout 5000"
        - "--cluster-announce-port 6379" 
        - "--cluster-announce-bus-port 16379"
        - "--appendonly yes"
    master2:
      image: redis:alpine
      ports:
        - target: 6379
          published: 6380
          protocol: tcp
          mode: host
        - target: 16379
          published: 16380
          protocol: tcp
          mode: host  
      deploy:
        replicas: 2
      command: 
        - "redis-server"
        - "--cluster-enabled yes"
        - "--cluster-config-file nodes.conf"
        - "--cluster-node-timeout 5000"
        - "--cluster-announce-port 6380" 
        - "--cluster-announce-bus-port 16380"
        - "--appendonly yes"
    master3:
      image: redis:alpine
      ports:
        - target: 6379
          published: 6381
          protocol: tcp
          mode: host
        - target: 16379
          published: 16381
          protocol: tcp
          mode: host  
      deploy:
        replicas: 2
      command: 
        - "redis-server"
        - "--cluster-enabled yes"
        - "--cluster-config-file nodes.conf"
        - "--cluster-node-timeout 5000"
        - "--cluster-announce-port 6381" 
        - "--cluster-announce-bus-port 16381"
        - "--appendonly yes"
