
export default class DateUtil {
    static format(date, pattern, utc) {
        utc = utc ? 'getUTC' : 'get';
        return pattern.replace(/%[YmdHMS]/g, function (m) {
            switch (m) {
                case '%Y': return date[utc + 'FullYear'](); // no leading zeros required
                case '%m': m = 1 + date[utc + 'Month'](); break;
                case '%d': m = date[utc + 'Date'](); break;
                case '%H': m = date[utc + 'Hours'](); break;
                case '%M': m = date[utc + 'Minutes'](); break;
                case '%S': m = date[utc + 'Seconds'](); break;
                default: return m.slice(1); // unknown code, remove %
            }
            // add leading zero if required
            return ('0' + m).slice(-2);
        });
    }
    static parse(str, pattern) {
        let normalized = str.replace(/[^a-zA-Z0-9]/g, '-');
        let normalizedFormat = pattern.replace(/[^a-zA-Z0-9%]/g, '-');
        let formatItems = normalizedFormat.split('-');
        let dateItems = normalized.split('-');

        let monthIndex = formatItems.indexOf("%m");
        let dayIndex = formatItems.indexOf("%d");
        let yearIndex = formatItems.indexOf("%Y");
        let hourIndex = formatItems.indexOf("%H");
        let minutesIndex = formatItems.indexOf("%M");
        let secondsIndex = formatItems.indexOf("%s");

        let year = yearIndex > -1 ? dateItems[yearIndex] : 0;
        let month = monthIndex > -1 ? dateItems[monthIndex] - 1 : 0;
        let day = dayIndex > -1 ? dateItems[dayIndex] : 1;

        let hour = hourIndex > -1 ? dateItems[hourIndex] : 0;
        let minute = minutesIndex > -1 ? dateItems[minutesIndex] : 0;
        let second = secondsIndex > -1 ? dateItems[secondsIndex] : 0;

        return new Date(year, month, day, hour, minute, second);
    }

    static getMonthRange(interval = 0) {
        let now = new Date();
        now.setMonth(now.getMonth() + interval)
        now.setDate(1)
        let start = DateUtil.format(now, '%Y-%m-%d') + ' 00:00:00';
        now.setMonth(now.getMonth() + 1)
        now.setDate(now.getDate() - 1)
        let end = DateUtil.format(now, '%Y-%m-%d') + ' 23:59:59';
        return { start, end }
    }


}
