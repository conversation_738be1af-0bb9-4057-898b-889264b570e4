import req  from './simple_req.js'
import crypto from 'crypto'

export default class Notify {
  constructor(access_token, secret) {
    this.access_token =
      access_token ||
      "4d7fdcf6dd40503211184fc5c97a3c0f5d4af7c9f0e76d7a1d39a98634a91da9";
    this.secret =
      secret ||
      "SEC7ffc1450a973f5a41864659b361c7017174aecd68f6772195cfdc9eb76d62b53";
  }

  sendFeeCards(cards) {
    this.send({
      "msgtype": "feedCard",
      "feedCard": {
        'links': cards
      }
    })
  }

  sendMarkdown(title, text) {
    let time = `\n\n\n ###### ${this.getDate()} 发布`;
    this.send({
      msgtype: "markdown",
      markdown: {
        title: title,
        text: text + time,
      },
    });
  }

  sendText(text) {
    this.send({
      msgtype: "text",
      text: {
        content: text,
      },
    });
  }

  async send(body) {
    console.log("'钉钉机器人'发送消息：" + JSON.stringify(body));
    const hmac = crypto.createHmac("sha256", this.secret);
    const timestamp = Date.now();
    hmac.update(timestamp + "\n" + this.secret);

    const sign = encodeURIComponent(hmac.digest("base64"));

    await req.post( `https://oapi.dingtalk.com/robot/send?access_token=${this.access_token}&timestamp=${timestamp}&sign=${sign}`,
      {
        json: body,
      }
    );
    console.log("发送完成");
  }
  getDate() {
    let now = new Date();
    let month = ("" + (now.getMonth() + 1)).padStart(2, "0");
    let day = ("" + now.getDate()).padStart(2, "0");
    let hour = ("" + now.getHours()).padStart(2, "0");
    let minute = ("" + now.getMinutes()).padStart(2, "0");
    let second = ("" + now.getSeconds()).padStart(2, "0");
    return `${now.getFullYear()}-${month}-${day} ${hour}:${minute}:${second}`;
  }
};
