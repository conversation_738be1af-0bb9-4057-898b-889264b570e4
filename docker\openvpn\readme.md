## openvpn 玩客云
```s
docker run -v /root/docker/openvpn:/etc/openvpn --log-driver=none --rm ixdotai/openvpn ovpn_genconfig -u tcp://VPN.SERVERNAME.COM:1194
docker run -v /root/docker/openvpn:/etc/openvpn --log-driver=none --privileged --rm -it ixdotai/openvpn ovpn_initpki

docker run -v /root/docker/openvpn:/etc/openvpn -d -p 1194:1194/tcp --restart=always --privileged --cap-add=NET_ADMIN ixdotai/openvpn
docker run -v /root/docker/openvpn:/etc/openvpn --log-driver=none --privileged --rm -it ixdotai/openvpn easyrsa build-client-full cloudpay
docker run -v /root/docker/openvpn:/etc/openvpn --log-driver=none  --privileged --rm ixdotai/openvpn ovpn_getclient cloudpay > cloudpay.ovpn
```

### 2023-11-07 15:15:58
```
docker run -v /root/docker/openvpn:/etc/openvpn --rm kylemanna/openvpn ovpn_genconfig -u tcp://frp-can.top:22608
docker run -v /root/docker/openvpn:/etc/openvpn --rm -it kylemanna/openvpn ovpn_initpki

docker run -v /root/docker/openvpn:/etc/openvpn --rm -it kylemanna/openvpn easyrsa build-client-full cloudpay
docker run -v /root/docker/openvpn:/etc/openvpn --rm kylemanna/openvpn ovpn_getclient cloudpay > cloudpay.ovpn


docker run -v /root/docker/openvpn:/etc/openvpn --restart=always -d -p 1194:1194/tcp --cap-add=NET_ADMIN kylemanna/openvpn
```


iptables -t nat -A PREROUTING -i tun0 -d *************** -j DNAT --to-destination ***********