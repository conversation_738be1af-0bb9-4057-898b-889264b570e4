import axios from 'axios'
import Header from '../util/header.js'
;(async () => {

    let headers = Header.parse(`
    cookie: uid=15191;expires=Fri, 18-Nov-2022 09:38:02 GMT;Max-Age=86400;path=/;email=yiyetianxiang%40gmail.com;key=992590e9a758c248ba32c3ce52bc86ad186a3be8f7cab;ip=5465936f47952fcdfb0777f69b2b84be;expire_in=1668764282;ge_ua_key=HKtOgVrqLJKXG2AoZg5xSY7kidE%3D
    origin: https://purefast.net
    pragma: no-cache
    referer: https://purefast.net/user
    user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
`)
console.log(headers);
    let resp = await axios.post('http://purefast.net/user/checkin','', {
        headers: headers,
    })
    console.log(resp.data);
})();