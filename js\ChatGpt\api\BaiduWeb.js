import fetch from "node-fetch";

let _resp = await fetch(
    `https://yiyan.baidu.com/eb/chat/conversation/v2`,
    {
      method: "POST",
      headers: {
        "origin": "https://yiyan.baidu.com",
        "Content-Type": "application/json",
        "host": "yiyan.baidu.com",
        "Referer": "https://yiyan.baidu.com/",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/117.0.2045.60",
        "cookie": cookies,
        "Acs-Token": params.sign
      },
      body: JSON.stringify(params)
    }
  );
  let message = ''
  _resp.body.on("readable", () => {
    let chunk;
    while (null !== (chunk = _resp.body.read())) {
        //console.log('------------response', chunk.toString())
        let result = chunk.toString()
        if(result) {
          if(result.startsWith('event:')) {
            if(message) {
              let index = message.indexOf('data:')
              console.log('------message', message)
              resp.write(message.substring(index))
              if(message.indexOf('"is_end":1') >=0) {
                resp.end()
              }
              message = ''
            }
          }
        }
        message += result
    }
  });