
using System;
using System.Collections.Generic;
using System.Text;
using H3;

public class D149913Sb5cj1kgr8jav74qk65xeu3800_ListViewController : H3.SmartForm.ListViewController
{
    public D149913Sb5cj1kgr8jav74qk65xeu3800_ListViewController(H3.SmartForm.ListViewRequest request) : base(request)
    {
    }

    protected override void OnLoad(H3.SmartForm.LoadListViewResponse response)
    {
        base.OnLoad(response);
    }

    protected override void OnSubmit(string actionName, H3.SmartForm.ListViewPostValue postValue, H3.SmartForm.SubmitListViewResponse response)
    {
        if (actionName == "collect")
        {
            string[] objectIds = this.Deserialize<string[]>(this.Request["ObjectIds"]);
            if (objectIds == null || objectIds.Length == 0)
            {
                response.Errors.Add("请选择待领用数据");
                return;
            }

            string userId = this.Request.UserContext.UserId;
            string sqlpo = "select objectid from i_D1499132aa514ee99054a06b340a5b023be519a where ******** = '" + userId + "'";

            System.Data.DataTable saleTable = this.Request.Engine.Query.QueryTable(sqlpo, null);
            if (saleTable == null || saleTable.Rows.Count == 0)
            {
                response.Errors.Add("当前登录人未在销售信息中维护");
                return;
            }
            string saleId = Convert.ToString(saleTable.Rows[0]["objectid"]);


            foreach (string objectId in objectIds)
            {
                //修改公海信息
                string sql = "update I_D149913Sb5cj1kgr8jav74qk65xeu3800 set F0000034='" +
                 saleId + "',F0000017='跟进中',OwnerId='" + userId + "'" +
                 " where ObjectId='" + objectId + "'";
                //修改客户信息拥有者
                sql += ";update I_D149913d77c222c479b4bc0994639a67827eeb0 set F0000035='" +
                 saleId + "',F0000017='跟进中',OwnerId='" + userId + "'" +
                 " where ObjectId='" + objectId + "'";
                //修改销售订单拥有者
                sql += ";update I_D149913Sno9vwxo1ym57slwtoxwojn4h5 set OwnerId='" + userId + "' where F0000002='" + objectId + "'";
                // 修改跟进记录
                sql += ";update I_D149913c4d3264fd7dd440cb6f8262f16aa5428 set OwnerId='" + userId + "' where ********='" + objectId + "'";
                // 修改项目信息
                sql += ";update I_D149913cb600e63d0f64430b2bf116d08c19046 set OwnerId='" + userId + "' where ********='" + objectId + "'";
                this.Engine.EngineConfig.CreateCommand().ExecuteScalar(sql);
            }


            response.Message = "操作成功";
            response.Refresh = true;
            return;
        }
        base.OnSubmit(actionName, postValue, response);
    }
}