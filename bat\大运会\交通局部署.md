## 开发环境IP，端口，描述
```s
**************  6842    调度系统钉钉H5微应用移动端
**************  6843    学习平台钉钉H5微应用移动端
**************  6844    学习平台钉钉H5微应用PC端

*************** 8024    调度系统后端
*************   8025    学习平台移动端后端
*************   8026    学习平台PC端后端
```

## 钉钉应用关键信息
```yml
企业id： ding24267832f03477a3f5bf40eda33b7ba0

调度系统：
AgentId： **********
AppKey： dinguql7mnupm5e3vnvx
AppSecret： DTzg5odW5CfnPIL0Oo825O_8JzXcWmXC1JIdFTIzPKQyFyi-kgjs_xVFWtDG0APA

学习平台移动端：
AgentId： **********
AppKey： dingpzntsy2pbp5cqyce
AppSecret：  OwfYhK5LAcae75wB7ia6CERsAobf21_oDm-g2i_Pq8M4gv43zCnJ8Me5J1fM5oQX

学习平台后台
AgentId： **********
AppKey： dingsliafbgn2u3lkeqy
AppSecret： OZppDeG71oPAsghNSCuaCOBjvnxNMXjd9O93hAD4PUuo1YxIE4fH4GZlB2SUf1d7
```


## 千方内部接口地址
```s
**************    9101
```

## 公网访问地址
```s
11**********

调度系统后端                    http://11**********:8024    redis: 0
学习平台移动后端                http://11**********:8025    redis: 1
学习平台PC后端                  http://11**********:8026    redis: 2

调度系统钉钉H5微应用移动端      http://11**********:6842
学习平台钉钉H5微应用移动端      http://11**********:6843
学习平台钉钉H5微应用PC端        http://11**********:6844
```

## redi地址
```s
redis-cli -h 192.167.241.8 -a 1qaz2WSX

192.167.241.8
```

## call back 测试
```s
## post 请求
curl -H "Content-Type:application/json" -d "signature=c43f59801c98a608b207a69b5716b4c45c014f61&msg_signature=c43f59801c98a608b207a69b5716b4c45c014f61&timestamp=1618820927518&nonce=NAZhFQPB"  http://11**********:8026/callback


curl 'http://11**********:8026/callback?signature=c43f59801c98a608b207a69b5716b4c45c014f61&msg_signature=c43f59801c98a608b207a69b5716b4c45c014f61&timestamp=1618820927518&nonce=NAZhFQPB' -d '{\"encrypt\":\"TSDI2zc2QdTlxbtPydNfOqP2fHdspZKOdTnnu/AwbIOISUX4PHZT9p/FooAU6unUq+Ow4jyY3Jrl4voxarg4LyXN8w0K5U0BMfhqU/OITWdzNo3l2C+pALzL919Ga9N+\"}' -H 'Content-Type:application/json'


curl 'http://11**********:8025/callback?signature=7684088bb0d2d98899dbe20a1710bb3151e6b566&msg_signature=7684088bb0d2d98899dbe20a1710bb3151e6b566&timestamp=1618827528467&nonce=5ONMZQNN' -d '{"encrypt":"Osk1LkdTPraU6KCPgBuFEl+yYIGczRdq5YXXNDftWSS9ykYjYV3dwKqDS9wKh9oiE3X/tNTdaj/2U33VrQ71uKgHT5rljBcT8/hzPR7BhK6Vi/tktvryC6ElbgLQuCO8"}' -H 'Content-Type:application/json'
```

### swagger 关闭
```yml
io.swagger.enabled: false
```

#### 大运会 vpn
```
链接地址： https://***************:4433/por/service.csp?rnd=fofccdnbipaenoeh#https%3A%2F%2F***************%3A4433%2F

1、阿里
账号：DaYunHui-ALi
密码：&q0nM#7RLkAk

```