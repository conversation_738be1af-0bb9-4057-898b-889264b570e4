const req = require("../../simple_req");
const NeteaseLogin= require('./login')
module.exports = class YouDao {
  constructor() {
    this.headers = {
      Cookie:''
    };

    this.sign_url = "https://note.youdao.com/yws/mapi/user?method=checkin";
  }

  async sign_in() {
    //let neteaseLogin = new NeteaseLogin()
    // let cookies = await neteaseLogin.login()
    this.headers.Cookie = 'YNOTE_LOGIN=true; YNOTE_SESS=v2|4HAcrNq0ZRJ4RfqLn4PF0qB0LO50LpBROMn4YWkMUm0pFOLkW64P4RkA64QS6MqB0Q464Yf0LJS0guhfwBkLwS0gynLlM0MpK0;JSESSIONID=aaaxk9eCOPe4fdKdKe0cy'// cookies.join('; ')

    let resp =await req.post('https://note.youdao.com/yws/api/daupromotion/v1/myTask?_imei=c69b8ea2fb773d24&_system=android&_systemVersion=12&_operator=M&_device=2201122C&_androidId=f0f3f281a85d4ba4&_screenWidth=1080&_screenHeight=2277&_manufacturer=Xiaomi&_appName=ynote&_appuser=7056be6604ab486d9bb66dd7118c3a21&_platform=android&_version=7.3.9&_network=wifi&_deviceId=f0f3f281a85d4ba4&_vendor=base&_longitude=&_latitude=&_cityCode=510100&_cityName=%E6%88%90%E9%83%BD&_launch=3&_firstTime=2022-05-26%2010%3A23%3A50&_cpu=%5Barm64-v8a%2C%20armeabi-v7a%2C%20armeabi%5D&_pkgArch=&sev=j1&sign=ad4a0e690cb7d5bb9afc7c8c44fe3e1d',{
      headers: this.headers,
      form: {
        sign: '006DB021171C7575787682AE7084D12F',
        status: '1',
        imei: 'f0f3f281a85d4ba4',
        mid: 'REL',
        level: 'user',
        location: 'memory',
        net: 'wifi',
        apn: 'wifi',
        username: 'yiyetianxiang',
        login: 'phone',
        phoneVersion: 'android',
        model: '2201122C',
        vendor: 'base',
        first_vendor: 'base',
        keyfrom: 'note.7.3.9.android',
        dpi: '420',
        resolution: '1080*2277',
        bg: '0',
        size: '2.5714285714285716*5.421428571428572',
        os_arch: 'aarch64',
        cpu_abi: 'arm64-v8a',
        os: 'android',
        os_ver: '31',
        device_name: 'zeus',
        device_model: '2201122C',
        device_id: 'c69b8ea2fb773d24',
        client_ver: '7.3.9',
        device_type: 'android'
      }
    })
   console.log(resp, resp.headers)

    //await this.signinapp()
    //await this.logindaily()
    //return this.text()
  }

  async signinapp() {
    let resp = await req.post( this.sign_url,  {
      headers: this.headers,
    })
    if (resp.status == 200) {
      this.sign_in = JSON.parse(resp.body);
    } else {
      return new Error("每日登录: 失败" +
      `response = ${JSON.stringify(resp)}` +
      `data = ${resp.body}`)
    }
  }

  async logindaily() {
    let resp = await req.post( "https://note.youdao.com/yws/api/daupromotion?method=sync",  {
      headers: this.headers,
    })
    if (resp.status == 200) {
      this.sign_in = JSON.parse(resp.body);
    } else {
      return new Error("每日登录: 失败" +
      `response = ${JSON.stringify(resp)}` +
      `data = ${resp.body}`)
    }
  }

  text() {
    if(this.daily && this.daily.error) {
      return this.daily.message;
    }

    const dailyFlag = this.daily.accept === true ? "成功" : "重复";
    const signinFlag =
      this.sign_in.success === 1
        ? "成功"
        : this.sign_in.success === 0
        ? "重复"
        : "错误";
    let text = "### **有道云笔记签到**\n";
    text += `- 每日登录: ${dailyFlag}, 每日签到: ${signinFlag} \n`;
    text += `- 连签: ${this.daily.rewardSpace / 1024 / 1024}天 \n`;
    text += `- 本次获得: ${this.daily.rewardSpace / 1024 / 1024}MB \n`;
    text += `- 总共获得: ${this.daily.totalRewardSpace / 1024 / 1024}MB \n`;
    return text
  }
};
