chcp 65001

@rem 运行openvpn

if "%1" == "h" goto begin
mshta vbscript:createobject("wscript.shell").run("%~nx0 h",0)(window.close)&&exit
:begin

@rem default recover dns server
netsh interface ip set dns "local" static *********** primary

@rem start "" D:\Software\Develop\google\chrome.exe
@rem start "" D:\Software\IDE\idea64.exe.lnk

@rem 删除酷我音乐播放列表
rmdir C:\ProgramData\Kuwodata\kwmusic2013\ModuleData\ModPlayList /s /q && mkdir C:\ProgramData\Kuwodata\kwmusic2013\ModuleData\ModPlayList

start "" mstsc D:\Projects\own\startup\*************.rdp

timeout /t 5
start "" D:\Software\IDE\VSCode\Code.exe

timeout /t 5
start "" D:\Software\Tool\Proxifier\Proxifier.exe

timeout /t 5
start "" msedge

timeout /t 10
netsh interface ip set dns "local" static ************* primary


exit 0

