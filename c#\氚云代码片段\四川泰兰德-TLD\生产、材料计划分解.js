/* 控件接口说明：
 * 1. 读取控件: this.***,*号输入控件编码;
 * 2. 读取控件的值： this.***.GetValue();
 * 3. 设置控件的值： this.***.SetValue(???);
 * 4. 绑定控件值变化事件： this.***.Bind<PERSON>hange(key,function(){})，key是定义唯一的方法名;
 * 5. 解除控件值变化事件： this.***.UnbindChange(key);
 * 6. CheckboxList、DropDownList、RadioButtonList: $.***.AddItem(value,text),$.***.ClearItems();
 */
/* 公共接口：
 * 1. ajax：$.SmartForm.PostForm(actionName,data,callBack,errorBack,async),
 *          actionName:提交的ActionName;data:提交后台的数据;callback:回调函数;errorBack:错误回调函数;async:是否异步;
 * 2. 打开表单：$.IShowForm(schemaCode, objectId, checkIsChange)，
 *          schemaCode:表单编码;objectId;表单数据Id;checkIsChange:关闭时，是否感知变化;
 * 3. 定位接口：$.ILocation();
 */

async function postForm(actionName, data) {
    return new Promise((resolve, reject) => {
        $.SmartForm.PostForm(actionName, data, (resp) => {
            resolve(resp.ReturnData)
        }, (resp) => {
            reject(resp)
        }, true)
    })
}

// 表单插件代码
$.extend($.JForm, {
    // 加载事件
    OnLoad: function () {
        // 产品明细绑定变更事件
        let $this = this
        let products_key = 'D149913c1619bcef24b4e89b11f95ad557911ed'
        let $productDetails = $this[products_key]
        let $materialDetails = $this['D149913Fqbt7kofydy0n11q8b875dckc6']
        $productDetails.BindChange("", async function (changeRow) {
            if (changeRow != null && (
                changeRow[0].DataField == `${products_key}.F0000005` //产品信息
                || changeRow[0].DataField == `${products_key}.F0000006`)) { //数量

                productDetails = $productDetails.GetValue()
                let productIds = productDetails.filter(e => e['F0000005']).map(e => e['F0000005'])
                let resp = await postForm("GetBOM", { productIds: productIds })
                if (!resp || !resp.bomOrders || resp.bomOrders.length == 0) {
                    $materialDetails.ClearRows()
                    return
                }
                let bomOrders = resp.bomOrders
                let materials = {}
                for (let bomOrder of bomOrders) {

                    let productDetail = productDetails.find(e => e['F0000005'] === bomOrder['F0000001'])
                    console.log(productDetail, productDetails)
                    let num = parseInt(productDetail['F0000006'] || 1)
                    let demands = bomOrder['D149913Fa5ace13d63c24085a595142f424a9826']
                    for (let demand of demands) {
                        let productId = demand['F0000002']
                        let demandNum = parseInt(demand['F0000005'] || 0) * num
                        if (materials[productId]) {
                            materials[productId]['D149913Fqbt7kofydy0n11q8b875dckc6.F0000006'] = materials[productId]['D149913Fqbt7kofydy0n11q8b875dckc6.F0000006'] + demandNum
                        } else {
                            materials[productId] = {
                                'D149913Fqbt7kofydy0n11q8b875dckc6.F0000005': productId, //产品信息
                                'D149913Fqbt7kofydy0n11q8b875dckc6.F0000006': demandNum, //需求数量
                                'D149913Fqbt7kofydy0n11q8b875dckc6.F0000007': demand['F0000013'], //单位
                                'D149913Fqbt7kofydy0n11q8b875dckc6.F0000012': demand['F0000007'], //产品型号
                                'D149913Fqbt7kofydy0n11q8b875dckc6.F0000019': demand['F0000012'],//品牌
                                'D149913Fqbt7kofydy0n11q8b875dckc6.F0000024': demand['F0000010'] //备注
                            }
                        }
                    }

                }

                $materialDetails.ClearRows()
                let materialDetails = Object.values(materials)
                for (let materialDetail of materialDetails) {
                    let subObjectId = $.IGuid();  //创建行ID
                    $materialDetails.AddRow(subObjectId, materialDetail)
                }
            }
        });


    },

    // 按钮事件
    OnLoadActions: function (actions) {
    },

    // 提交校验
    OnValidate: function (actionControl) {
        return true;
    },

    // 提交前事件
    BeforeSubmit: function (action, postValue) {
    },

    // 提交后事件
    AfterSubmit: function (action, responseValue) {
    }
});