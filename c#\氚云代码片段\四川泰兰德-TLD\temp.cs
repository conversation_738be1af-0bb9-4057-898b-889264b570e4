using System;
using System.Collections.Generic;
using System.Text;
using H3;

public class D000119salesorder: H3.SmartForm.SmartFormController
{
    public D000119salesorder(H3.SmartForm.SmartFormRequest request): base(request)
    {
        //11
    }

    protected override void OnLoad(H3.SmartForm.LoadSmartFormResponse response)
    {
        base.OnLoad(response);

        //反审核按钮
        {
            //检查条件
            bool isAnti = false;
            H3.DataModel.BizObjectSchema shemaSetting = this.Engine.BizObjectManager.GetPublishedSchema("D000119IntialSetup");
            if(shemaSetting.GetProperty("F0000051") != null)
            {
                string sqlSetting = "select ifnull(F0000051,'关闭') as isAnti from I_D000119IntialSetup order by createdtime desc";
                System.Data.DataTable resultsSetting = this.Request.Engine.Query.QueryTable(sqlSetting, null);
                if(resultsSetting != null && resultsSetting.Rows.Count > 0)
                {
                    if(resultsSetting.Rows[0]["isAnti"] + string.Empty == "开启")
                    {
                        isAnti = true;
                    }
                }
            }
            if(isAnti == true && this.Request.BizObject.Status == H3.DataModel.BizObjectStatus.Effective && this.Request.BizObject["PartialStock"] + string.Empty != "手工关闭")
            {
                H3.Workflow.Instance.WorkflowInstance wfInstance = this.Request.Engine.WorkflowInstanceManager.GetWorkflowInstance(this.Request.BizObject.WorkflowInstanceId);
                if(wfInstance != null)
                {
                    bool flag = false;
                    H3.Workflow.Instance.IToken[] tokens = wfInstance.Tokens;
                    H3.Workflow.Instance.IToken lastToken = tokens[tokens.Length - 2];
                    string sql = "select Finisher,Originator,Participant from H_WorkItem where tokenid='" + lastToken.TokenId + "' and InstanceId='" + this.Request.BizObject.WorkflowInstanceId + "'";
                    System.Data.DataTable result = this.Request.Engine.Query.QueryTable(sql, null);
                    if(result != null && result.Rows.Count > 0)
                    {
                        if(this.Request.UserContext.UserId == result.Rows[0]["Finisher"] + string.Empty)
                        {
                            response.Actions.Add("btnAntiSubmit", new H3.SmartForm.ViewAction("btnAntiSubmit", "反审核", "fa-undo"));
                            flag = true;
                        }
                        else if(this.Request.UserContext.UserId == result.Rows[0]["Participant"] + string.Empty)
                        {
                            response.Actions.Add("btnAntiSubmit", new H3.SmartForm.ViewAction("btnAntiSubmit", "反审核", "fa-undo"));
                            flag = true;
                        }
                    }
                    if(flag == false)
                    {
                        if(this.Request.UserContext.IsAdministrator)
                        {
                            response.Actions.Add("btnAntiSubmit", new H3.SmartForm.ViewAction("btnAntiSubmit", "反审核", "fa-undo"));
                        }
                    }

                }
            }

        }
        response.ReturnData["currency"].Editable = false;
        if(this.Request.BizObject.Status != H3.DataModel.BizObjectStatus.Effective)
        {
            response.ReturnData["PartialStock"].Visible = false;
        }
    }
    protected override void OnSubmit(string actionName, H3.SmartForm.SmartFormPostValue postValue, H3.SmartForm.SubmitSmartFormResponse response)
    {
        //删除日志
        H3.DataModel.BizObjectSchema xschema = this.Engine.BizObjectManager.GetPublishedSchema("D000119sckdetail"); //20201118 代码升级为一张销售出库单可以包含多个CO，即CO入子表
        if(xschema.GetProperty("**********") == null)
        {
            if(actionName == "Remove")
            {
                H3.DataModel.BizObject ob = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId, this.Engine, this.Request.Schema.SchemaCode, this.Request.BizObjectId, false);
                H3.DataModel.BizObject thisObj = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId, this.Engine, ob.Schema.SchemaCode, ob.ObjectId, false);
                var formStatue = this.Request.BizObject.Status; //获取表单状态 cui 2018/06/01
                H3.DataModel.BizObjectHeader header = null;
                mBizObject.RemoveBizObject(this.Engine, this.Request.UserContext.UserId, this.Request.BizObject, out header, true);
                if(header != null)
                {
                    string content = header.SchemaDisplayName + ":" + header.Name;
                    response.Errors.Add("删除失败，数据被" + content + "引用，请先删除此表单！");
                    return;
                }
                if(formStatue == H3.DataModel.BizObjectStatus.Effective) //只有表单在提交生效时，才执行回滚 cui 2018/06/01
                {
                    ReExec(this.Engine, this.Request.UserContext.UserId, ob);
                }
                response.ClosePage = true;
                return;
            }
            if(actionName == "btnAntiSubmit")
            {
                //response.Message="test";
                ReExec(this.Engine, this.Request.UserContext.UserId, this.Request.BizObject);
                AntiSubmitRemoving(this.Engine, this.Request.UserContext.UserId, this.Request.BizObject);
                H3.Workflow.Messages.ActivateInstanceMessage   activiteinstanceMessage = new H3.Workflow.Messages.ActivateInstanceMessage(this.Request.InstanceId); //传递参数为流程实例ID
                this.Request.Engine.WorkflowInstanceManager.SendMessage(activiteinstanceMessage);//只会激活流程，并不会触发运行的节点

                H3.Workflow.Instance.WorkflowInstance wfInstance = this.Request.Engine.WorkflowInstanceManager.GetWorkflowInstance(this.Request.BizObject.WorkflowInstanceId);
                if(wfInstance != null)
                {
                    H3.Workflow.Instance.IToken[] tokens = wfInstance.Tokens;
                    H3.Workflow.Instance.IToken lastToken = tokens[tokens.Length - 2];
                    H3.Workflow.Messages.ActivateActivityMessage activiteMessage = new H3.Workflow.Messages.ActivateActivityMessage(this.Request.InstanceId,
                        lastToken.Activity, H3.Workflow.Instance.Token.UnspecifiedId, new string[] {}, null, false, H3.Workflow.WorkItem.ActionEventType.Adjust);//参数对应描述：流程实例ID，活动节点编码，令牌ID，参与者，前驱令牌，是否检测入口条件，激活类型
                    this.Request.Engine.WorkflowInstanceManager.SendMessage(activiteMessage);//1.不会取消正在运行的节点。2.进行中的流程才能激活调整。

                }

                response.Message = "反审核执行完成";

            }
            if(actionName == "CheckRelation")
            {
                string msg = "";
                //采购订单
                {
                    string sql = "select seqno from i_D000119PO where co='" + this.Request.BizObjectId + "' and status=1";
                    System.Data.DataTable result = this.Request.Engine.Query.QueryTable(sql, null);
                    if(result != null && result.Rows.Count > 0)
                    {
                        int i = 0;
                        foreach(System.Data.DataRow row in result.Rows)
                        {
                            if(i == 0)
                            {
                                msg += "采购订单：" + row["seqno"];
                            }
                            else
                            {
                                msg += "," + row["seqno"];
                            }
                            i++;
                        }
                    }
                }
                //生产计划
                {
                    string sql = "select seqno from i_D000119productionplan where co='" + this.Request.BizObjectId + "' and status=1";
                    System.Data.DataTable result = this.Request.Engine.Query.QueryTable(sql, null);
                    if(result != null && result.Rows.Count > 0)
                    {
                        int i = 0;
                        foreach(System.Data.DataRow row in result.Rows)
                        {
                            if(i == 0)
                            {
                                msg += "生产计划：" + row["seqno"];
                            }
                            else
                            {
                                msg += "," + row["seqno"];
                            }
                            i++;
                        }
                    }
                }
                //销售出库
                {
                    string sql = "select seqno from i_D000119wocuku where co='" + this.Request.BizObjectId + "' and status=1";
                    System.Data.DataTable result = this.Request.Engine.Query.QueryTable(sql, null);
                    if(result != null && result.Rows.Count > 0)
                    {
                        int i = 0;
                        foreach(System.Data.DataRow row in result.Rows)
                        {
                            if(i == 0)
                            {
                                msg += "销售出库：" + row["seqno"];
                            }
                            else
                            {
                                msg += "," + row["seqno"];
                            }
                            i++;
                        }
                    }
                }
                //组装拆卸
                {
                    string sql = "select seqno from i_D000119AssembleSheet where F0000120='" + this.Request.BizObject["SeqNo"] + "' and status=1";
                    System.Data.DataTable result = this.Request.Engine.Query.QueryTable(sql, null);
                    if(result != null && result.Rows.Count > 0)
                    {
                        int i = 0;
                        foreach(System.Data.DataRow row in result.Rows)
                        {
                            if(i == 0)
                            {
                                msg += "销售出库：" + row["seqno"];
                            }
                            else
                            {
                                msg += "," + row["seqno"];
                            }
                            i++;
                        }
                    }
                }
                //销售回款
                {
                    string sql = "select seqno from i_D000119AR01 where co='" + this.Request.BizObjectId + "' and status=1";
                    System.Data.DataTable result = this.Request.Engine.Query.QueryTable(sql, null);
                    if(result != null && result.Rows.Count > 0)
                    {
                        int i = 0;
                        foreach(System.Data.DataRow row in result.Rows)
                        {
                            if(i == 0)
                            {
                                msg += "销售回款：" + row["seqno"];
                            }
                            else
                            {
                                msg += "," + row["seqno"];
                            }
                            i++;
                        }
                    }
                }
                //发货申请
                {
                    string sql = "select t1.seqno from i_D000119DeliveryApply t1, i_D000119CoDetail t2 where t1.objectid=t2.parentobjectid and t2.co='" + this.Request.BizObjectId + "'";
                    System.Data.DataTable result = this.Request.Engine.Query.QueryTable(sql, null);
                    if(result != null && result.Rows.Count > 0)
                    {
                        int i = 0;
                        foreach(System.Data.DataRow row in result.Rows)
                        {
                            if(i == 0)
                            {
                                msg += "发货申请：" + row["seqno"];
                            }
                            else
                            {
                                msg += "," + row["seqno"];
                            }
                            i++;
                        }
                    }
                }
                if(msg != "")
                {
                    msg = "有以下表单生效或进行中数据，无法执行反审核.\n" + msg;
                }
                response.ReturnData = new Dictionary<string, object>();
                response.ReturnData.Add("msg", msg);
            }
            // end
            if(actionName == "GetCustomerId")
            {
                string cname = this.Request["cname"] + string.Empty;
                string objectId = string.Empty;
                decimal creditmoney = 0;
                decimal creditdate = 0;
                string cnameshortvalidate = "";
                decimal Discount = 0; //折扣
                string RT4 = ""; //客户等级
                // string Country = "";
                string address2 = "";//客户地址2
                string CalculateAPdays = "";  //收货地址
                string JKStatus = "";  //收货人
                string how = ""; //收货电话
                string currency = "";
                decimal when = 0;
                decimal FixAPDays = 0;
                string salesmanager = "";
                string fullname = "";//全称
                H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();
                filter.Matcher = new H3.Data.Filter.ItemMatcher("cnameshort", H3.Data.ComparisonOperatorType.Equal, cname);
                H3.DataModel.BizObject[] list = H3.DataModel.BizObject.GetList(this.Engine, this.Request.UserContext.UserId, this.Engine.BizObjectManager.GetPublishedSchema("D000119createcustomer"),
                    H3.DataModel.GetListScopeType.GlobalAll, filter);
                if(list != null && list.Length > 0)
                {
                    objectId = list[0].ObjectId;
                    RT4 = list[0]["Cgrade"] + string.Empty;
                    Discount = Convert.ToDecimal(list[0]["RD5"]);
                    // Country = list[0]["ccountry"] + string.Empty;
                    address2 = list[0]["RT3"] + string.Empty;
                    CalculateAPdays = list[0]["Bankaccount"] + string.Empty;//收货地址 ********
                    JKStatus = list[0]["RT5"] + string.Empty; //收货人
                    how = list[0]["Tel"] + string.Empty;//收货电话
                    currency = list[0]["cweb"] + string.Empty;
                    salesmanager = list[0]["OwnerId"] + string.Empty;
                    fullname = list[0]["cname"] + string.Empty;
                }
                if(Discount == 0 || Discount == null)
                {
                    Discount = 1;
                }
                response.ReturnData = new Dictionary<string, object>();
                response.ReturnData.Add("clientId", objectId);
                response.ReturnData.Add("RT4", RT4);
                response.ReturnData.Add("salesmanager", salesmanager);
                response.ReturnData.Add("Discount", Discount);
                response.ReturnData.Add("creditmoney", creditmoney);
                response.ReturnData.Add("creditdate", creditdate);
                // response.ReturnData.Add("Country", Country);
                response.ReturnData.Add("address2", address2);
                response.ReturnData.Add("CalculateAPdays", CalculateAPdays);//收货地址
                response.ReturnData.Add("JKStatus", JKStatus);
                response.ReturnData.Add("how", how);
                response.ReturnData.Add("currency", currency);
                response.ReturnData.Add("fullname", fullname);
            }
            if(actionName == "GetOverdueTotal")
            {
                DateTime now = System.DateTime.Today;
                Decimal inamount = 0;
                decimal aRTotal = 0;
                string cname = this.Request["cname"] + string.Empty;
                string seqnoid = "HSeqNo";
                try
                {
                    H3.DataModel.BizObject temp = new H3.DataModel.BizObject(this.Engine, this.Engine.BizObjectManager.GetPublishedSchema("D000119IOlistFinance"), this.Request.UserContext.UserId);
                    temp[seqnoid] = "test";
                }
                catch(Exception e)
                {
                    seqnoid = "SeqNo";
                }
                string sql = "select ifnull(DrSide,0) as drside,ifnull(OpeningYF,0) as openingyf ,ifnull(Currency,'') as currency from I_D000119ARAccounting where  Cname='" + cname + "' and DrSide!=ifnull(OpeningYF,0) and SupposeDate <'" + now + "' ";
                System.Data.DataTable result = this.Engine.Query.QueryTable(sql, null);
                if(result != null && result.Rows.Count > 0)
                {
                    foreach(System.Data.DataRow row in result.Rows)
                    {
                        string Currency = row["currency"] + string.Empty;
                        decimal rate = PriceRelated.GetExChangeRate(this.Engine, this.Request.UserContext.UserId, Currency, DateTime.Now);
                        inamount += (Convert.ToDecimal(row["drside"]) - Convert.ToDecimal(row["openingyf"])) * rate;
                    }
                }
                string sql1 = "select ifnull(DrBalance,0) as balance from I_D000119ARAccounting where Cname='" + cname + "' order by " + seqnoid + " desc limit 1";
                System.Data.DataTable result1 = this.Engine.Query.QueryTable(sql1, null);
                if(result1 != null && result1.Rows.Count > 0)
                {
                    aRTotal = Convert.ToDecimal(result1.Rows[0]["balance"]);
                }
                response.ReturnData = new Dictionary<string, object>();
                response.ReturnData.Add("OverdueTotal", inamount);
                response.ReturnData.Add("ARTotal", aRTotal);
            }
            if(actionName == "GetPriceByPcode")
            {

                string productId = this.Request["ProductId"] + string.Empty;
                string accountId = this.Request["AccountId"] + string.Empty;
                string currency = this.Request["Currency"] + string.Empty;
                string Unit = this.Request["unit"] + string.Empty;
                decimal discount = Convert.ToDecimal(this.Request["Discount"]);
                decimal vatPrice = 0;
                double Tax = 0;
                decimal netPrice = 0;
                decimal stockSum = 0;
                decimal stockYX = 0;
                decimal processqty = 0; //建议生产数量
                string  procode = string.Empty;
                string  Proname = string.Empty;
                string Prospec = string.Empty;
                string DJ1 = string.Empty;
                string DJ2 = string.Empty;
                string  DJ3 = string.Empty;
                string  DJ4 = string.Empty;
                string  DJ5 = string.Empty;
                string  RT2 = string.Empty;
                string  RT1 = string.Empty;
                string  huoqu = string.Empty;
                bool flag = true;
                H3.DataModel.BizObject product = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId, this.Request.Engine, "D000119Product_sale", productId, false);
                if(product != null)
                {
                    procode = product["Procode"] + string.Empty;
                    Proname = product["Proname"] + string.Empty;
                    Prospec = product["Prospec"] + string.Empty;
                    DJ1 = product["DJ1"] + string.Empty;
                    DJ2 = product["DJ2"] + string.Empty;
                    DJ3 = product["DJ3"] + string.Empty;
                    DJ4 = product["DJ4"] + string.Empty;
                    DJ5 = product["DJ5"] + string.Empty;
                    huoqu = product["huoqu"] + string.Empty;
                    if(Unit == "")
                    {
                        Unit = product["Unit"] + string.Empty;
                    }
                }
                try
                {
                    H3.DataModel.BizObject[] SPsales = H3.DataModel.BizObject.GetList(this.Request.Engine, this.Request.UserContext.UserId,
                        this.Request.Engine.BizObjectManager.GetPublishedSchema("D000119IntialSetup"), H3.DataModel.GetListScopeType.GlobalAll,
                        new H3.Data.Filter.Filter());
                    if(SPsales != null && SPsales.Length > 0)
                    {
                        Tax = Convert.ToDouble(SPsales[0]["F0000156"]);
                    }
                }
                catch(Exception e)
                {
                }
                H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();  //构建过滤器
                H3.Data.Filter.And andMatcher = new H3.Data.Filter.And();    //构造And匹配器
                H3.Data.Filter.Or orMatcher = new H3.Data.Filter.Or();
                andMatcher.Add(new H3.Data.Filter.ItemMatcher("Pcode", H3.Data.ComparisonOperatorType.Equal, productId)); //添加查询条件
                andMatcher.Add(new H3.Data.Filter.ItemMatcher("Unit", H3.Data.ComparisonOperatorType.Equal, Unit));
                andMatcher.Add(new H3.Data.Filter.ItemMatcher("Pstatus", H3.Data.ComparisonOperatorType.Equal, "正常使用"));
                andMatcher.Add(new H3.Data.Filter.ItemMatcher("Ccode", H3.Data.ComparisonOperatorType.Equal, accountId));
                filter.Matcher = andMatcher;
                H3.DataModel.BizObjectSchema priceSchema = this.Request.Engine.BizObjectManager.GetPublishedSchema("D000119PriceIndex"); //最新售价
                H3.DataModel.BizObject[] prices = H3.DataModel.BizObject.GetList(this.Request.Engine, this.Request.UserContext.UserId,
                    priceSchema, H3.DataModel.GetListScopeType.GlobalAll, filter);
                if(prices != null && prices.Length > 0)
                {
                    vatPrice = Convert.ToDecimal(prices[0]["VATPrice"]) * discount;
                    Tax = Convert.ToDouble(prices[0]["Tax"]);
                    netPrice = Convert.ToDecimal(prices[0]["Netprice"]) * discount;
                    flag = false;
                }
                if(flag) //没有终端报价，再找渠道报价 2019/04/07
                {
                    H3.Data.Filter.Filter filter2nd = new H3.Data.Filter.Filter();  //构建过滤器
                    H3.Data.Filter.And andMatcher2nd = new H3.Data.Filter.And();    //构造And匹配器
                    H3.Data.Filter.Or orMatcher2nd = new H3.Data.Filter.Or();    //构造And匹配器
                    andMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Pcode", H3.Data.ComparisonOperatorType.Equal, productId)); //添加查询条件
                    andMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Unit", H3.Data.ComparisonOperatorType.Equal, Unit));
                    andMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Pstatus", H3.Data.ComparisonOperatorType.Equal, "正常使用"));
                    orMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Ccode", H3.Data.ComparisonOperatorType.Equal, ""));
                    orMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Ccode", H3.Data.ComparisonOperatorType.IsNull, ""));
                    andMatcher2nd.Add(orMatcher2nd);
                    filter2nd.Matcher = andMatcher2nd;
                    H3.DataModel.BizObjectSchema priceSchema2nd = this.Request.Engine.BizObjectManager.GetPublishedSchema("D000119PriceIndex"); //最新售价
                    H3.DataModel.BizObject[] prices2nd = H3.DataModel.BizObject.GetList(this.Request.Engine, this.Request.UserContext.UserId,
                        priceSchema2nd, H3.DataModel.GetListScopeType.GlobalAll, filter2nd);
                    if(prices2nd != null && prices2nd.Length > 0)
                    {
                        vatPrice = Convert.ToDecimal(prices2nd[0]["VATPrice"]) * discount;
                        Tax = Convert.ToDouble(prices2nd[0]["Tax"]);
                        netPrice = Convert.ToDecimal(prices2nd[0]["Netprice"]) * discount;
                    }
                }
                //******** 动态获取库存和有效库存
                decimal gdqtysum = StockRelated.GetGdStockSum(this.Engine, this.Request.UserContext.UserId, productId, procode);
                decimal avaqtysum = StockRelated.GetAvaStockSum(this.Engine, this.Request.UserContext.UserId, productId, procode);
                decimal packrate = StockRelated.packrate(this.Engine, productId, Unit); //******** 新增
                gdqtysum = gdqtysum / packrate; //******** 新增
                avaqtysum = avaqtysum / packrate; //******** 新增
                response.ReturnData = new Dictionary<string, object>();
                response.ReturnData.Add("stock", gdqtysum);
                response.ReturnData.Add("stockYX", avaqtysum);
                response.ReturnData.Add("vatPrice", vatPrice);
                response.ReturnData.Add("Tax", Tax);
                response.ReturnData.Add("netPrice", netPrice);
                response.ReturnData.Add("Proname", Proname);
                response.ReturnData.Add("Prospec", Prospec);
                response.ReturnData.Add("Unit", Unit);
                response.ReturnData.Add("DJ1", DJ1);
                response.ReturnData.Add("DJ2", DJ2);
                response.ReturnData.Add("DJ3", DJ3);
                response.ReturnData.Add("DJ4", DJ4);
                response.ReturnData.Add("DJ5", DJ5);
                response.ReturnData.Add("huoqu", huoqu);
            }
            if(actionName == "GetPriceByPcodelink")
            {
                string productIdlink = this.Request["ProductIdlink"] + string.Empty;
                decimal discount = Convert.ToDecimal(this.Request["Discount"]);

                string pcode = "";
                string procode = "";
                string Proname = string.Empty;
                string Prospec = string.Empty;
                string Unit = string.Empty;
                string DJ1 = string.Empty;
                string DJ2 = string.Empty;
                string  DJ3 = string.Empty;
                string  DJ4 = string.Empty;
                string  DJ5 = string.Empty;
                string  huoqu = string.Empty;
                decimal vatPrice = 0;
                double Tax = 0;
                decimal netPrice = 0;
                decimal stockSum = 0;
                decimal stockYX = 0;
                decimal processqty = 0; //建议生产数量
                H3.DataModel.BizObject price = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId, this.Request.Engine, "D000119PriceIndex", productIdlink, false);
                if(price != null)
                {
                    pcode = price["Pcode"] + string.Empty;
                    H3.DataModel.BizObject product = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId, this.Request.Engine, "D000119Product_sale", pcode, false);
                    if(product != null)
                    {
                        procode = product["Procode"] + string.Empty;
                        Proname = product["Proname"] + string.Empty;
                        Prospec = product["Prospec"] + string.Empty;

                        DJ1 = product["DJ1"] + string.Empty;
                        DJ2 = product["DJ2"] + string.Empty;
                        DJ3 = product["DJ3"] + string.Empty;
                        DJ4 = product["DJ4"] + string.Empty;
                        DJ5 = product["DJ5"] + string.Empty;
                        huoqu = product["huoqu"] + string.Empty;
                    }
                    Unit = price["Unit"] + string.Empty; //20201112 无法带出单位，把unit改为Unit
                    vatPrice = Convert.ToDecimal(price["VATPrice"]) * discount;
                    Tax = Convert.ToDouble(price["Tax"]);
                    netPrice = Convert.ToDecimal(price["Netprice"]) * discount;
                }
                //******** 动态获取库存和有效库存
                decimal gdqtysum = StockRelated.GetGdStockSum(this.Engine, this.Request.UserContext.UserId, pcode, procode);
                decimal avaqtysum = StockRelated.GetAvaStockSum(this.Engine, this.Request.UserContext.UserId, pcode, procode);
                decimal packrate = StockRelated.packrate(this.Engine, pcode, Unit); //******** 新增
                avaqtysum = avaqtysum / packrate; //******** 新增
                if(avaqtysum < 0)
                {
                    avaqtysum = 0;
                }
                response.ReturnData = new Dictionary<string, object>();
                response.ReturnData.Add("pcode", pcode);
                response.ReturnData.Add("stock", gdqtysum);
                response.ReturnData.Add("stockYX", avaqtysum);
                response.ReturnData.Add("vatPrice", vatPrice);
                response.ReturnData.Add("Tax", Tax);
                response.ReturnData.Add("netPrice", netPrice);
                response.ReturnData.Add("Proname", Proname);
                response.ReturnData.Add("Prospec", Prospec);
                response.ReturnData.Add("Unit", Unit);
                response.ReturnData.Add("DJ1", DJ1);
                response.ReturnData.Add("DJ2", DJ2);
                response.ReturnData.Add("DJ3", DJ3);
                response.ReturnData.Add("DJ4", DJ4);
                response.ReturnData.Add("DJ5", DJ5);
                response.ReturnData.Add("huoqu", huoqu);
            }
            if(actionName == "GetPriceByUnit")
            {
                string productId = this.Request["ProductId"] + string.Empty;
                string accountId = this.Request["AccountId"] + string.Empty;
                string currency = this.Request["Currency"] + string.Empty;
                string Unit = this.Request["Unit"] + string.Empty;
                decimal discount = Convert.ToDecimal(this.Request["Discount"]);
                decimal vatPrice = 0;
                double Tax = 0;
                decimal netPrice = 0;
                decimal stockSum = 0;
                decimal stockYX = 0;
                decimal processqty = 0; //建议生产数量
                string  Proname = string.Empty;
                string Prospec = string.Empty;
                string DJ1 = string.Empty;
                string DJ2 = string.Empty;
                string  DJ3 = string.Empty;
                string  DJ4 = string.Empty;
                string  DJ5 = string.Empty;
                string  RT2 = string.Empty;
                string  RT1 = string.Empty;
                string  huoqu = string.Empty;
                bool flag = true;
                try
                {
                    H3.DataModel.BizObject[] SPsales = H3.DataModel.BizObject.GetList(this.Request.Engine, this.Request.UserContext.UserId,
                        this.Request.Engine.BizObjectManager.GetPublishedSchema("D000119IntialSetup"), H3.DataModel.GetListScopeType.GlobalAll,
                        new H3.Data.Filter.Filter());
                    if(SPsales != null && SPsales.Length > 0)
                    {
                        Tax = Convert.ToDouble(SPsales[0]["F0000156"]);
                    }
                }
                catch(Exception e)
                {
                }
                H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();  //构建过滤器
                H3.Data.Filter.And andMatcher = new H3.Data.Filter.And();    //构造And匹配器
                H3.Data.Filter.Or orMatcher = new H3.Data.Filter.Or();
                andMatcher.Add(new H3.Data.Filter.ItemMatcher("Pcode", H3.Data.ComparisonOperatorType.Equal, productId)); //添加查询条件
                andMatcher.Add(new H3.Data.Filter.ItemMatcher("Unit", H3.Data.ComparisonOperatorType.Equal, Unit));
                andMatcher.Add(new H3.Data.Filter.ItemMatcher("Pstatus", H3.Data.ComparisonOperatorType.Equal, "正常使用"));
                andMatcher.Add(new H3.Data.Filter.ItemMatcher("Ccode", H3.Data.ComparisonOperatorType.Equal, accountId));
                filter.Matcher = andMatcher;
                H3.DataModel.BizObjectSchema priceSchema = this.Request.Engine.BizObjectManager.GetPublishedSchema("D000119PriceIndex"); //最新售价
                H3.DataModel.BizObject[] prices = H3.DataModel.BizObject.GetList(this.Request.Engine, this.Request.UserContext.UserId,
                    priceSchema, H3.DataModel.GetListScopeType.GlobalAll, filter);
                if(prices != null && prices.Length > 0)
                {
                    vatPrice = Convert.ToDecimal(prices[0]["VATPrice"]) * discount;
                    Tax = Convert.ToDouble(prices[0]["Tax"]);
                    netPrice = Convert.ToDecimal(prices[0]["Netprice"]) * discount;
                    flag = false;
                }
                if(flag) //没有终端报价，再找渠道报价 2019/04/07
                {
                    H3.Data.Filter.Filter filter2nd = new H3.Data.Filter.Filter();  //构建过滤器
                    H3.Data.Filter.And andMatcher2nd = new H3.Data.Filter.And();    //构造And匹配器
                    H3.Data.Filter.Or orMatcher2nd = new H3.Data.Filter.Or();    //构造And匹配器
                    andMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Pcode", H3.Data.ComparisonOperatorType.Equal, productId)); //添加查询条件
                    andMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Unit", H3.Data.ComparisonOperatorType.Equal, Unit));
                    andMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Pstatus", H3.Data.ComparisonOperatorType.Equal, "正常使用"));
                    orMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Ccode", H3.Data.ComparisonOperatorType.Equal, ""));
                    orMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Ccode", H3.Data.ComparisonOperatorType.IsNull, ""));
                    andMatcher2nd.Add(orMatcher2nd);
                    filter2nd.Matcher = andMatcher2nd;
                    H3.DataModel.BizObjectSchema priceSchema2nd = this.Request.Engine.BizObjectManager.GetPublishedSchema("D000119PriceIndex"); //最新售价
                    H3.DataModel.BizObject[] prices2nd = H3.DataModel.BizObject.GetList(this.Request.Engine, this.Request.UserContext.UserId,
                        priceSchema2nd, H3.DataModel.GetListScopeType.GlobalAll, filter2nd);
                    if(prices2nd != null && prices2nd.Length > 0)
                    {
                        vatPrice = Convert.ToDecimal(prices2nd[0]["VATPrice"]) * discount;
                        Tax = Convert.ToDouble(prices2nd[0]["Tax"]);
                        netPrice = Convert.ToDecimal(prices2nd[0]["Netprice"]) * discount;
                    }
                }
                string procode = "";
                string sqlpd = "select procode from i_D000119Product_sale where objectid='" + productId + "'";
                System.Data.DataTable resultpd = this.Request.Engine.Query.QueryTable(sqlpd, null);
                if(resultpd != null && resultpd.Rows.Count > 0)
                {
                    procode = Convert.ToString(resultpd.Rows[0]["procode"]);
                }
                //******** 动态获取库存和有效库存
                decimal gdqtysum = StockRelated.GetGdStockSum(this.Engine, this.Request.UserContext.UserId, productId, procode);
                decimal avaqtysum = StockRelated.GetAvaStockSum(this.Engine, this.Request.UserContext.UserId, productId, procode);
                decimal packrate = StockRelated.packrate(this.Engine, productId, Unit); //******** 新增
                gdqtysum = gdqtysum / packrate; //******** 新增
                avaqtysum = avaqtysum / packrate; //******** 新增

                H3.DataModel.BizObject product = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId, this.Request.Engine, "D000119Product_sale", productId, false);
                if(product != null)
                {
                    Proname = product["Proname"] + string.Empty;
                    Prospec = product["Prospec"] + string.Empty;
                    DJ1 = product["DJ1"] + string.Empty;
                    DJ2 = product["DJ2"] + string.Empty;
                    DJ3 = product["DJ3"] + string.Empty;
                    DJ4 = product["DJ4"] + string.Empty;
                    DJ5 = product["DJ5"] + string.Empty;
                    huoqu = product["huoqu"] + string.Empty;
                }
                response.ReturnData = new Dictionary<string, object>();
                response.ReturnData.Add("stock", gdqtysum);
                response.ReturnData.Add("stockYX", avaqtysum);
                response.ReturnData.Add("vatPrice", vatPrice);
                response.ReturnData.Add("Tax", Tax);
                response.ReturnData.Add("netPrice", netPrice);
                response.ReturnData.Add("Proname", Proname);
                response.ReturnData.Add("Prospec", Prospec);
                response.ReturnData.Add("DJ1", DJ1);
                response.ReturnData.Add("DJ2", DJ2);
                response.ReturnData.Add("DJ3", DJ3);
                response.ReturnData.Add("DJ4", DJ4);
                response.ReturnData.Add("DJ5", DJ5);
                response.ReturnData.Add("huoqu", huoqu);
            }

            //获取辅助编码
            if(actionName == "GetSecondPcode")
            {
                string pcode = this.Request["pcode"] + string.Empty;
                string ccode = this.Request["ccode"] + string.Empty;
                string secpcode = "";
                string sql = "select t1.Addremarks from I_D000119SecondLevelMBOM t1, I_D000119createcustomer t2 where t1.pcode='" + pcode + "' and t1.Huoqu=t2.ccode and t2.objectid='" + ccode + "'";
                System.Data.DataTable result = this.Request.Engine.Query.QueryTable(sql, null);
                if(result != null && result.Rows.Count > 0)
                {
                    secpcode = result.Rows[0]["Addremarks"] + string.Empty;
                }
                response.ReturnData = new Dictionary<string, object>();
                response.ReturnData.Add("secpcode", secpcode);
                response.ReturnData.Add("sql", sql);
            }
            if(actionName == "GetBOM")
            {
                string  bomflag = "";
                H3.DataModel.BizObjectSchema schema = this.Engine.BizObjectManager.GetPublishedSchema("D000119sckdetail"); //20201118 代码升级为一张销售出库单可以包含多个CO，即CO入子表
                if(schema.GetProperty("**********") != null)
                {
                    //订单价格覆盖报价20190919
                    H3.DataModel.BizObject[] setup = H3.DataModel.BizObject.GetList(this.Request.Engine, this.Request.UserContext.UserId,
                        this.Request.Engine.BizObjectManager.GetPublishedSchema("D000119IntialSetup"), H3.DataModel.GetListScopeType.GlobalAll,
                        new H3.Data.Filter.Filter());
                    if(setup != null && setup.Length > 0)
                    {
                        if(setup[0]["F0000022"] + string.Empty == "是")
                        {
                            bomflag = "ok";
                        }
                    }
                }
                response.Message = bomflag;
            }
            if(actionName == "Submit" || actionName == "Save")//判断，走不同的审批路线
            {
                this.Request.BizObject["currency"] = postValue.Data.ContainsKey("currency") ? postValue.Data["currency"] : this.Request.BizObject["currency"];
                this.Request.BizObject["ccode"] = postValue.Data.ContainsKey("ccode") ? postValue.Data["ccode"] : this.Request.BizObject["ccode"];
                this.Request.BizObject["ARTotal"] = postValue.Data.ContainsKey("ARTotal") ? postValue.Data["ARTotal"] : this.Request.BizObject["ARTotal"];
                this.Request.BizObject["OverdueTotal"] = postValue.Data.ContainsKey("OverdueTotal") ? postValue.Data["OverdueTotal"] : this.Request.BizObject["OverdueTotal"];
                //20200529
                this.Request.BizObject["OpenClose"] = postValue.Data.ContainsKey("OpenClose") ? postValue.Data["OpenClose"] : this.Request.BizObject["OpenClose"];
                this.Request.BizObject["CalculateAPdays"] = postValue.Data.ContainsKey("CalculateAPdays") ? postValue.Data["CalculateAPdays"] : this.Request.BizObject["CalculateAPdays"];
                this.Request.BizObject["Yesnodiscount"] = postValue.Data.ContainsKey("Yesnodiscount") ? postValue.Data["Yesnodiscount"] : this.Request.BizObject["Yesnodiscount"];
                this.Request.BizObject["JKStatus"] = postValue.Data.ContainsKey("JKStatus") ? postValue.Data["JKStatus"] : this.Request.BizObject["JKStatus"];
                this.Request.BizObject["how"] = postValue.Data.ContainsKey("how") ? postValue.Data["how"] : this.Request.BizObject["how"];
                if(actionName == "Submit")
                {
                    H3.DataModel.BizObject[] details = (H3.DataModel.BizObject[]) this.Request.BizObject["D000119orderlist"];
                    if(details != null && details.Length > 0)
                    {
                        decimal dd = 0;
                        foreach(H3.DataModel.BizObject detail in details)
                        {
                            decimal check0 = 1;
                            if(Convert.ToDecimal(detail["sellprice"]) >= Convert.ToDecimal(detail["SalesPricebeforediscount"])) //销售价格>=销售报价
                            {
                                check0 = 0;
                            }

                            dd += Convert.ToDecimal(check0);
                            string productId = Convert.ToString(detail["pcode"]);
                            string unit = Convert.ToString(detail["unit"]);
                            string produnit = "";
                            string sqlpd = "select Unit from i_D000119Product_sale where objectid='" + productId + "'";
                            System.Data.DataTable resultpd = this.Request.Engine.Query.QueryTable(sqlpd, null);
                            if(resultpd != null && resultpd.Rows.Count > 0)
                            {
                                produnit = Convert.ToString(resultpd.Rows[0]["Unit"]);
                            }
                            if(unit != produnit)
                            {
                                string sqlpo = "select objectid from i_D0001191stlayerBOM where Pcode = '" + productId + "'and ifnull(RT1,'') = '" + unit + "'";
                                System.Data.DataTable resultpo = this.Request.Engine.Query.QueryTable(sqlpo, null);
                                if(resultpo == null || resultpo.Rows.Count == 0)
                                {
                                    response.Errors.Add("请先到:产品/基础设置/中设置销售单位|" + unit + "！");
                                    return;
                                }
                            }
                        }
                        this.Request.BizObject["InvoiceAmount"] = dd;
                    }
                    bool rateexist = false;
                    rateexist = PriceRelated.ExistChangeRate(this.Engine, this.Request.UserContext.UserId, this.Request.BizObject["currency"] + string.Empty, DateTime.Now);
                    if(!rateexist)
                    {
                        if(this.Request.BizObject["currency"] + string.Empty != "CNY")
                        {
                            response.Errors.Add("因订单货币是" + this.Request.BizObject["currency"] + "," + "请通知财务人员设置汇率赋值");
                            return;
                        }
                    }

                }
            }
        }
        else
        {
            if(actionName == "Remove")
            {
                H3.DataModel.BizObject ob = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId, this.Engine, this.Request.Schema.SchemaCode, this.Request.BizObjectId, false);
                H3.DataModel.BizObject thisObj = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId, this.Engine, ob.Schema.SchemaCode, ob.ObjectId, false);
                var formStatue = this.Request.BizObject.Status; //获取表单状态 cui 2018/06/01
                H3.DataModel.BizObjectHeader header = null;
                mBizObject.RemoveBizObject(this.Engine, this.Request.UserContext.UserId, this.Request.BizObject, out header, true);
                if(header != null)
                {
                    string content = header.SchemaDisplayName + ":" + header.Name;
                    response.Errors.Add("删除失败，数据被" + content + "引用，请先删除此表单！");
                    return;
                }
                if(formStatue == H3.DataModel.BizObjectStatus.Effective) //只有表单在提交生效时，才执行回滚 cui 2018/06/01
                {
                    ReExec(this.Engine, this.Request.UserContext.UserId, ob);
                }
                response.ClosePage = true;
                return;
            }
            if(actionName == "btnAntiSubmit")
            {
                //response.Message="test";
                ReExec(this.Engine, this.Request.UserContext.UserId, this.Request.BizObject);
                AntiSubmitRemoving(this.Engine, this.Request.UserContext.UserId, this.Request.BizObject);
                H3.Workflow.Messages.ActivateInstanceMessage   activiteinstanceMessage = new H3.Workflow.Messages.ActivateInstanceMessage(this.Request.InstanceId); //传递参数为流程实例ID
                this.Request.Engine.WorkflowInstanceManager.SendMessage(activiteinstanceMessage);//只会激活流程，并不会触发运行的节点

                H3.Workflow.Instance.WorkflowInstance wfInstance = this.Request.Engine.WorkflowInstanceManager.GetWorkflowInstance(this.Request.BizObject.WorkflowInstanceId);
                if(wfInstance != null)
                {
                    H3.Workflow.Instance.IToken[] tokens = wfInstance.Tokens;
                    H3.Workflow.Instance.IToken lastToken = tokens[tokens.Length - 2];
                    H3.Workflow.Messages.ActivateActivityMessage activiteMessage = new H3.Workflow.Messages.ActivateActivityMessage(this.Request.InstanceId,
                        lastToken.Activity, H3.Workflow.Instance.Token.UnspecifiedId, new string[] {}, null, false, H3.Workflow.WorkItem.ActionEventType.Adjust);//参数对应描述：流程实例ID，活动节点编码，令牌ID，参与者，前驱令牌，是否检测入口条件，激活类型
                    this.Request.Engine.WorkflowInstanceManager.SendMessage(activiteMessage);//1.不会取消正在运行的节点。2.进行中的流程才能激活调整。

                }

                response.Message = "反审核执行完成";

            }
            if(actionName == "CheckRelation")
            {
                string msg = "";
                //采购订单
                {
                    string sql = "select seqno from i_D000119PO where co='" + this.Request.BizObjectId + "' and status=1";
                    System.Data.DataTable result = this.Request.Engine.Query.QueryTable(sql, null);
                    if(result != null && result.Rows.Count > 0)
                    {
                        int i = 0;
                        foreach(System.Data.DataRow row in result.Rows)
                        {
                            if(i == 0)
                            {
                                msg += "采购订单：" + row["seqno"];
                            }
                            else
                            {
                                msg += "," + row["seqno"];
                            }
                            i++;
                        }
                    }
                }
                //生产计划
                {
                    string sql = "select seqno from i_D000119productionplan where co='" + this.Request.BizObjectId + "' and status=1";
                    System.Data.DataTable result = this.Request.Engine.Query.QueryTable(sql, null);
                    if(result != null && result.Rows.Count > 0)
                    {
                        int i = 0;
                        foreach(System.Data.DataRow row in result.Rows)
                        {
                            if(i == 0)
                            {
                                msg += "生产计划：" + row["seqno"];
                            }
                            else
                            {
                                msg += "," + row["seqno"];
                            }
                            i++;
                        }
                    }
                }
                //销售出库
                {
                    string sql = "select seqno from i_D000119wocuku where co='" + this.Request.BizObjectId + "' and status=1";
                    System.Data.DataTable result = this.Request.Engine.Query.QueryTable(sql, null);
                    if(result != null && result.Rows.Count > 0)
                    {
                        int i = 0;
                        foreach(System.Data.DataRow row in result.Rows)
                        {
                            if(i == 0)
                            {
                                msg += "销售出库：" + row["seqno"];
                            }
                            else
                            {
                                msg += "," + row["seqno"];
                            }
                            i++;
                        }
                    }
                }
                //组装拆卸
                {
                    string sql = "select seqno from i_D000119AssembleSheet where F0000120='" + this.Request.BizObject["SeqNo"] + "' and status=1";
                    System.Data.DataTable result = this.Request.Engine.Query.QueryTable(sql, null);
                    if(result != null && result.Rows.Count > 0)
                    {
                        int i = 0;
                        foreach(System.Data.DataRow row in result.Rows)
                        {
                            if(i == 0)
                            {
                                msg += "销售出库：" + row["seqno"];
                            }
                            else
                            {
                                msg += "," + row["seqno"];
                            }
                            i++;
                        }
                    }
                }
                //销售回款
                {
                    string sql = "select seqno from i_D000119AR01 where co='" + this.Request.BizObjectId + "' and status=1";
                    System.Data.DataTable result = this.Request.Engine.Query.QueryTable(sql, null);
                    if(result != null && result.Rows.Count > 0)
                    {
                        int i = 0;
                        foreach(System.Data.DataRow row in result.Rows)
                        {
                            if(i == 0)
                            {
                                msg += "销售回款：" + row["seqno"];
                            }
                            else
                            {
                                msg += "," + row["seqno"];
                            }
                            i++;
                        }
                    }
                }
                //发货申请
                {
                    string sql = "select t1.seqno from i_D000119DeliveryApply t1, i_D000119CoDetail t2 where t1.objectid=t2.parentobjectid and t2.co='" + this.Request.BizObjectId + "'";
                    System.Data.DataTable result = this.Request.Engine.Query.QueryTable(sql, null);
                    if(result != null && result.Rows.Count > 0)
                    {
                        int i = 0;
                        foreach(System.Data.DataRow row in result.Rows)
                        {
                            if(i == 0)
                            {
                                msg += "发货申请：" + row["seqno"];
                            }
                            else
                            {
                                msg += "," + row["seqno"];
                            }
                            i++;
                        }
                    }
                }
                if(msg != "")
                {
                    msg = "有以下表单生效或进行中数据，无法执行反审核.\n" + msg;
                }
                response.ReturnData = new Dictionary<string, object>();
                response.ReturnData.Add("msg", msg);
            }
            if(actionName == "GetOverdueTotal")
            {
                DateTime now = System.DateTime.Today;
                Decimal inamount = 0;
                decimal aRTotal = 0;
                string Code = "";
                string accountId = this.Request["accountId"] + string.Empty;
                string sqlpo = "select ccode from i_D000119createcustomer where objectid = '" + accountId + "'";
                System.Data.DataTable resultpo = this.Request.Engine.Query.QueryTable(sqlpo, null);
                if(resultpo != null && resultpo.Rows.Count > 0)
                {
                    Code = Convert.ToString(resultpo.Rows[0]["ccode"]);
                }
                string seqnoid = "HSeqNo";
                try
                {
                    H3.DataModel.BizObject temp = new H3.DataModel.BizObject(this.Engine, this.Engine.BizObjectManager.GetPublishedSchema("D000119IOlistFinance"), this.Request.UserContext.UserId);
                    temp[seqnoid] = "test";
                }
                catch(Exception e)
                {
                    seqnoid = "SeqNo";
                }
                string sql = "select ifnull(DrSide,0) as drside,ifnull(OpeningYF,0) as openingyf ,ifnull(Currency,'') as currency from I_D000119ARAccounting where  Code='" + Code + "' and DrSide!=ifnull(OpeningYF,0) and SupposeDate <'" + now + "' ";
                System.Data.DataTable result = this.Engine.Query.QueryTable(sql, null);
                if(result != null && result.Rows.Count > 0)
                {
                    foreach(System.Data.DataRow row in result.Rows)
                    {
                        string Currency = row["currency"] + string.Empty;
                        decimal rate = PriceRelated.GetExChangeRate(this.Engine, this.Request.UserContext.UserId, Currency, DateTime.Now);
                        inamount += (Convert.ToDecimal(row["drside"]) - Convert.ToDecimal(row["openingyf"])) * rate;
                    }
                }
                string sql1 = "select ifnull(DrBalance,0) as balance from I_D000119ARAccounting where Code='" + Code + "' order by " + seqnoid + " desc limit 1";
                System.Data.DataTable result1 = this.Engine.Query.QueryTable(sql1, null);
                if(result1 != null && result1.Rows.Count > 0)
                {
                    aRTotal = Convert.ToDecimal(result1.Rows[0]["balance"]);
                }
                response.ReturnData = new Dictionary<string, object>();
                response.ReturnData.Add("OverdueTotal", inamount);
                response.ReturnData.Add("ARTotal", aRTotal);
            }
            if(actionName == "GetPriceByPcode2nd")
            {
                string ynwl = this.Request["ynwl"] + string.Empty; //v3.0 210711 ls
                string productId = this.Request["ProductId"] + string.Empty;
                string accountId = this.Request["AccountId"] + string.Empty;
                string currency = this.Request["Currency"] + string.Empty;
                string Unit = this.Request["unit"] + string.Empty;
                decimal discount = Convert.ToDecimal(this.Request["Discount"]);
                decimal vatPrice = 0;
                double Tax = 0;
                decimal netPrice = 0;
                decimal stockSum = 0;
                decimal stockYX = 0;
                decimal processqty = 0; //建议生产数量
                string procode = "";
                string  Proname = string.Empty;
                string Prospec = string.Empty;
                string DJ1 = string.Empty;
                string DJ2 = string.Empty;
                string  DJ3 = string.Empty;
                string  DJ4 = string.Empty;
                string  DJ5 = string.Empty;
                string  RT2 = string.Empty;
                string  RT1 = string.Empty;
                string  huoqu = string.Empty;

                bool flag = true;
                H3.DataModel.BizObject product = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId, this.Request.Engine, "D000119Product_sale", productId, false);
                if(product != null)
                {
                    procode = product["Procode"] + string.Empty;
                    Proname = product["Proname"] + string.Empty;
                    Prospec = product["Prospec"] + string.Empty;
                    DJ1 = product["DJ1"] + string.Empty;
                    DJ2 = product["DJ2"] + string.Empty;
                    DJ3 = product["DJ3"] + string.Empty;
                    DJ4 = product["DJ4"] + string.Empty;
                    DJ5 = product["DJ5"] + string.Empty;
                    huoqu = product["huoqu"] + string.Empty;

                    if(Unit == "")
                    {
                        Unit = product["Unit"] + string.Empty;
                    }
                }
                try
                {
                    H3.DataModel.BizObject[] SPsales = H3.DataModel.BizObject.GetList(this.Request.Engine, this.Request.UserContext.UserId,
                        this.Request.Engine.BizObjectManager.GetPublishedSchema("D000119IntialSetup"), H3.DataModel.GetListScopeType.GlobalAll,
                        new H3.Data.Filter.Filter());
                    if(SPsales != null && SPsales.Length > 0)
                    {
                        Tax = Convert.ToDouble(SPsales[0]["F0000156"]);
                    }
                }
                catch(Exception e)
                {
                }
                H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();  //构建过滤器
                H3.Data.Filter.And andMatcher = new H3.Data.Filter.And();    //构造And匹配器
                H3.Data.Filter.Or orMatcher = new H3.Data.Filter.Or();
                andMatcher.Add(new H3.Data.Filter.ItemMatcher("Pcode", H3.Data.ComparisonOperatorType.Equal, productId)); //添加查询条件
                andMatcher.Add(new H3.Data.Filter.ItemMatcher("Unit", H3.Data.ComparisonOperatorType.Equal, Unit));
                andMatcher.Add(new H3.Data.Filter.ItemMatcher("Pstatus", H3.Data.ComparisonOperatorType.Equal, "正常使用"));
                andMatcher.Add(new H3.Data.Filter.ItemMatcher("Ccode", H3.Data.ComparisonOperatorType.Equal, accountId));
                andMatcher.Add(new H3.Data.Filter.ItemMatcher("Anticurrency", H3.Data.ComparisonOperatorType.Equal, currency));
                filter.Matcher = andMatcher;
                H3.DataModel.BizObjectSchema priceSchema = this.Request.Engine.BizObjectManager.GetPublishedSchema("D000119PriceIndex"); //最新售价
                H3.DataModel.BizObject[] prices = H3.DataModel.BizObject.GetList(this.Request.Engine, this.Request.UserContext.UserId,
                    priceSchema, H3.DataModel.GetListScopeType.GlobalAll, filter);
                if(prices != null && prices.Length > 0)
                {
                    vatPrice = Convert.ToDecimal(prices[0]["VATPrice"]) * discount;
                    Tax = Convert.ToDouble(prices[0]["Tax"]);
                    netPrice = Convert.ToDecimal(prices[0]["Netprice"]) * discount;
                    flag = false;
                }
                if(flag) //没有终端报价，再找渠道报价 2019/04/07
                {
                    H3.Data.Filter.Filter filter2nd = new H3.Data.Filter.Filter();  //构建过滤器
                    H3.Data.Filter.And andMatcher2nd = new H3.Data.Filter.And();    //构造And匹配器
                    H3.Data.Filter.Or orMatcher2nd = new H3.Data.Filter.Or();    //构造And匹配器
                    andMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Pcode", H3.Data.ComparisonOperatorType.Equal, productId)); //添加查询条件
                    andMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Anticurrency", H3.Data.ComparisonOperatorType.Equal, currency));
                    andMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Unit", H3.Data.ComparisonOperatorType.Equal, Unit));
                    andMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Pstatus", H3.Data.ComparisonOperatorType.Equal, "正常使用"));
                    orMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Ccode", H3.Data.ComparisonOperatorType.Equal, ""));
                    orMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Ccode", H3.Data.ComparisonOperatorType.IsNull, ""));
                    andMatcher2nd.Add(orMatcher2nd);
                    filter2nd.Matcher = andMatcher2nd;
                    H3.DataModel.BizObjectSchema priceSchema2nd = this.Request.Engine.BizObjectManager.GetPublishedSchema("D000119PriceIndex"); //最新售价
                    H3.DataModel.BizObject[] prices2nd = H3.DataModel.BizObject.GetList(this.Request.Engine, this.Request.UserContext.UserId,
                        priceSchema2nd, H3.DataModel.GetListScopeType.GlobalAll, filter2nd);
                    if(prices2nd != null && prices2nd.Length > 0)
                    {
                        vatPrice = Convert.ToDecimal(prices2nd[0]["VATPrice"]) * discount;
                        Tax = Convert.ToDouble(prices2nd[0]["Tax"]);
                        netPrice = Convert.ToDecimal(prices2nd[0]["Netprice"]) * discount;
                    }
                }
                //******** 动态获取库存和有效库存
                decimal gdqtysum = 0;
                decimal avaqtysum = 0;
                if(ynwl == "是") //v3.0 出库绑定客户时计算有效库存
                {
                    gdqtysum = StockRelated.GetYxStockSum3rd(this.Engine, this.Request.UserContext.UserId, productId, procode, accountId);
                    avaqtysum = StockRelated.GetAvaStockSum3rd(this.Engine, this.Request.UserContext.UserId, productId, procode, accountId);
                }
                else
                {
                    gdqtysum = StockRelated.GetYxStockSum(this.Engine, this.Request.UserContext.UserId, productId, procode);
                    avaqtysum = StockRelated.GetAvaStockSum(this.Engine, this.Request.UserContext.UserId, productId, procode);
                }
                // decimal packrate = StockRelated.packrate(this.Engine, productId, Unit); //******** 新增
                // gdqtysum = gdqtysum / packrate; //******** 新增
                // avaqtysum = avaqtysum / packrate; //******** 新增
                // if(avaqtysum < 0)
                // {
                //     avaqtysum = 0;
                // }
                //可选配件加载
                // string spId = "";
                // string sqlsp = "select objectid from i_D000119SalesOption where  status=1 and F0000006='" + procode + "' and ifnull(F0000001,'')='标准配件' order by createdtime desc limit 1 ";
                // System.Data.DataTable resultsp = this.Request.Engine.Query.QueryTable(sqlsp, null);
                // if(resultsp != null && resultsp.Rows.Count > 0)
                // {
                //     spId = Convert.ToString(resultsp.Rows[0]["objectid"]);
                // }

                //有无bom判断，没有通知研发
                string ynbom = this.Request["ynbom"] + string.Empty;
                if(huoqu == "自制" || huoqu == "委外")
                {
                    string sqlbom = "select objectid from i_D000119MbomCreated where Pcode ='" + productId + "'";
                    System.Data.DataTable resultbom = this.Request.Engine.Query.QueryTable(sqlbom, null);
                    if(resultbom == null || resultbom.Rows.Count == 0)
                    {
                        if(ynbom == "")
                        {
                            ynbom = procode + "|" + Proname;
                        }
                        else
                        {
                            ynbom = ynbom + ";" + procode + "|" + Proname;
                        }
                    }
                }

                response.ReturnData = new Dictionary<string, object>();
                response.ReturnData.Add("stock", gdqtysum);
                response.ReturnData.Add("stockYX", avaqtysum);
                response.ReturnData.Add("vatPrice", vatPrice);
                response.ReturnData.Add("Tax", Tax);
                response.ReturnData.Add("netPrice", netPrice);
                response.ReturnData.Add("Proname", Proname);
                response.ReturnData.Add("Prospec", Prospec);
                response.ReturnData.Add("Unit", Unit);
                response.ReturnData.Add("DJ1", DJ1);
                response.ReturnData.Add("DJ2", DJ2);
                response.ReturnData.Add("DJ3", DJ3);
                response.ReturnData.Add("DJ4", DJ4);
                response.ReturnData.Add("DJ5", DJ5);
                response.ReturnData.Add("huoqu", huoqu);
                response.ReturnData.Add("procode", procode);
                response.ReturnData.Add("ynbom", ynbom);

            }
            if(actionName == "GetPriceByPcodelink2nd")
            {
                string accountId = this.Request["AccountId"] + string.Empty; //v3.0 210711 ls
                string productIdlink = this.Request["ProductIdlink"] + string.Empty;
                decimal discount = Convert.ToDecimal(this.Request["Discount"]);
                string ynwl = this.Request["ynwl"] + string.Empty; //v3.0 210711 ls
                string pcode = "";
                string procode = "";
                string Proname = string.Empty;
                string Prospec = string.Empty;
                string Unit = string.Empty;
                string DJ1 = string.Empty;
                string DJ2 = string.Empty;
                string  DJ3 = string.Empty;
                string  DJ4 = string.Empty;
                string  DJ5 = string.Empty;
                string  huoqu = string.Empty;
                decimal vatPrice = 0;
                double Tax = 0;
                decimal netPrice = 0;
                decimal stockSum = 0;
                decimal stockYX = 0;
                decimal processqty = 0; //建议生产数量
                string productId = "";
                H3.DataModel.BizObject price = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId, this.Request.Engine, "D000119PriceIndex", productIdlink, false);
                if(price != null)
                {
                    pcode = price["Pcode"] + string.Empty;
                    H3.DataModel.BizObject product = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId, this.Request.Engine, "D000119Product_sale", pcode, false);
                    if(product != null)
                    {
                        productId = product["ObjectId"] + string.Empty;
                        procode = product["Procode"] + string.Empty;
                        Proname = product["Proname"] + string.Empty;
                        Prospec = product["Prospec"] + string.Empty;

                        DJ1 = product["DJ1"] + string.Empty;
                        DJ2 = product["DJ2"] + string.Empty;
                        DJ3 = product["DJ3"] + string.Empty;
                        DJ4 = product["DJ4"] + string.Empty;
                        DJ5 = product["DJ5"] + string.Empty;
                        huoqu = product["huoqu"] + string.Empty;
                    }
                    Unit = price["Unit"] + string.Empty; //20201112 无法带出单位，把unit改为Unit
                    vatPrice = Convert.ToDecimal(price["VATPrice"]) * discount;
                    Tax = Convert.ToDouble(price["Tax"]);
                    netPrice = Convert.ToDecimal(price["Netprice"]) * discount;
                }
                decimal gdqtysum = 0;
                decimal avaqtysum = 0;
                if(ynwl == "是") //v3.0 出库绑定客户时计算有效库存
                {
                    gdqtysum = StockRelated.GetYxStockSum3rd(this.Engine, this.Request.UserContext.UserId, productId, procode, accountId);
                    avaqtysum = StockRelated.GetAvaStockSum3rd(this.Engine, this.Request.UserContext.UserId, productId, procode, accountId);
                }
                else
                {
                    //******** 动态获取库存和有效库存
                    gdqtysum = StockRelated.GetYxStockSum(this.Engine, this.Request.UserContext.UserId, productId, procode);
                    avaqtysum = StockRelated.GetAvaStockSum(this.Engine, this.Request.UserContext.UserId, productId, procode);
                }
                decimal packrate = StockRelated.packrate(this.Engine, pcode, Unit); //******** 新增
                avaqtysum = avaqtysum / packrate; //******** 新增
                if(avaqtysum < 0)
                {
                    avaqtysum = 0;
                }
                //可选配件加载
                string spId = "";
                string sqlsp = "select objectid from i_D000119SalesOption where  status=1 and F0000006='" + procode + "' and ifnull(F0000001,'')='标准配件' order by createdtime desc limit 1 ";
                System.Data.DataTable resultsp = this.Request.Engine.Query.QueryTable(sqlsp, null);
                if(resultsp != null && resultsp.Rows.Count > 0)
                {
                    spId = Convert.ToString(resultsp.Rows[0]["objectid"]);
                }
                response.ReturnData = new Dictionary<string, object>();
                response.ReturnData.Add("pcode", pcode);
                response.ReturnData.Add("stock", gdqtysum);
                response.ReturnData.Add("stockYX", avaqtysum);
                response.ReturnData.Add("vatPrice", vatPrice);
                response.ReturnData.Add("Tax", Tax);
                response.ReturnData.Add("netPrice", netPrice);
                response.ReturnData.Add("Proname", Proname);
                response.ReturnData.Add("Prospec", Prospec);
                response.ReturnData.Add("Unit", Unit);
                response.ReturnData.Add("DJ1", DJ1);
                response.ReturnData.Add("DJ2", DJ2);
                response.ReturnData.Add("DJ3", DJ3);
                response.ReturnData.Add("DJ4", DJ4);
                response.ReturnData.Add("DJ5", DJ5);
                response.ReturnData.Add("huoqu", huoqu);
                response.ReturnData.Add("procode", procode);
            }
            if(actionName == "GetPriceByUnit2nd")
            {
                string ynwl = this.Request["ynwl"] + string.Empty; //v3.0 210711 ls
                string productId = this.Request["ProductId"] + string.Empty;
                string accountId = this.Request["AccountId"] + string.Empty;
                string currency = this.Request["Currency"] + string.Empty;
                string Unit = this.Request["Unit"] + string.Empty;
                decimal discount = Convert.ToDecimal(this.Request["Discount"]);
                decimal vatPrice = 0;
                double Tax = 0;
                decimal netPrice = 0;
                decimal stockSum = 0;
                decimal stockYX = 0;
                decimal processqty = 0; //建议生产数量
                string  Proname = string.Empty;
                string Prospec = string.Empty;
                string DJ1 = string.Empty;
                string DJ2 = string.Empty;
                string  DJ3 = string.Empty;
                string  DJ4 = string.Empty;
                string  DJ5 = string.Empty;
                string  RT2 = string.Empty;
                string  RT1 = string.Empty;
                string  huoqu = string.Empty;
                bool flag = true;
                try
                {
                    H3.DataModel.BizObject[] SPsales = H3.DataModel.BizObject.GetList(this.Request.Engine, this.Request.UserContext.UserId,
                        this.Request.Engine.BizObjectManager.GetPublishedSchema("D000119IntialSetup"), H3.DataModel.GetListScopeType.GlobalAll,
                        new H3.Data.Filter.Filter());
                    if(SPsales != null && SPsales.Length > 0)
                    {
                        Tax = Convert.ToDouble(SPsales[0]["F0000156"]);
                    }
                }
                catch(Exception e)
                {
                }
                H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();  //构建过滤器
                H3.Data.Filter.And andMatcher = new H3.Data.Filter.And();    //构造And匹配器
                H3.Data.Filter.Or orMatcher = new H3.Data.Filter.Or();
                andMatcher.Add(new H3.Data.Filter.ItemMatcher("Pcode", H3.Data.ComparisonOperatorType.Equal, productId)); //添加查询条件
                andMatcher.Add(new H3.Data.Filter.ItemMatcher("Unit", H3.Data.ComparisonOperatorType.Equal, Unit));
                andMatcher.Add(new H3.Data.Filter.ItemMatcher("Pstatus", H3.Data.ComparisonOperatorType.Equal, "正常使用"));
                andMatcher.Add(new H3.Data.Filter.ItemMatcher("Ccode", H3.Data.ComparisonOperatorType.Equal, accountId));
                andMatcher.Add(new H3.Data.Filter.ItemMatcher("Anticurrency", H3.Data.ComparisonOperatorType.Equal, currency));
                filter.Matcher = andMatcher;
                H3.DataModel.BizObjectSchema priceSchema = this.Request.Engine.BizObjectManager.GetPublishedSchema("D000119PriceIndex"); //最新售价
                H3.DataModel.BizObject[] prices = H3.DataModel.BizObject.GetList(this.Request.Engine, this.Request.UserContext.UserId,
                    priceSchema, H3.DataModel.GetListScopeType.GlobalAll, filter);
                if(prices != null && prices.Length > 0)
                {
                    vatPrice = Convert.ToDecimal(prices[0]["VATPrice"]) * discount;
                    Tax = Convert.ToDouble(prices[0]["Tax"]);
                    netPrice = Convert.ToDecimal(prices[0]["Netprice"]) * discount;
                    flag = false;
                }
                if(flag) //没有终端报价，再找渠道报价 2019/04/07
                {
                    H3.Data.Filter.Filter filter2nd = new H3.Data.Filter.Filter();  //构建过滤器
                    H3.Data.Filter.And andMatcher2nd = new H3.Data.Filter.And();    //构造And匹配器
                    H3.Data.Filter.Or orMatcher2nd = new H3.Data.Filter.Or();    //构造And匹配器
                    andMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Pcode", H3.Data.ComparisonOperatorType.Equal, productId)); //添加查询条件
                    andMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Unit", H3.Data.ComparisonOperatorType.Equal, Unit));
                    andMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Anticurrency", H3.Data.ComparisonOperatorType.Equal, currency));
                    andMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Pstatus", H3.Data.ComparisonOperatorType.Equal, "正常使用"));
                    orMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Ccode", H3.Data.ComparisonOperatorType.Equal, ""));
                    orMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Ccode", H3.Data.ComparisonOperatorType.IsNull, ""));
                    andMatcher2nd.Add(orMatcher2nd);
                    filter2nd.Matcher = andMatcher2nd;
                    H3.DataModel.BizObjectSchema priceSchema2nd = this.Request.Engine.BizObjectManager.GetPublishedSchema("D000119PriceIndex"); //最新售价
                    H3.DataModel.BizObject[] prices2nd = H3.DataModel.BizObject.GetList(this.Request.Engine, this.Request.UserContext.UserId,
                        priceSchema2nd, H3.DataModel.GetListScopeType.GlobalAll, filter2nd);
                    if(prices2nd != null && prices2nd.Length > 0)
                    {
                        vatPrice = Convert.ToDecimal(prices2nd[0]["VATPrice"]) * discount;
                        Tax = Convert.ToDouble(prices2nd[0]["Tax"]);
                        netPrice = Convert.ToDecimal(prices2nd[0]["Netprice"]) * discount;
                    }
                }
                string procode = "";
                string sqlpd = "select procode from i_D000119Product_sale where objectid='" + productId + "'";
                System.Data.DataTable resultpd = this.Request.Engine.Query.QueryTable(sqlpd, null);
                if(resultpd != null && resultpd.Rows.Count > 0)
                {
                    procode = Convert.ToString(resultpd.Rows[0]["procode"]);
                }
                decimal gdqtysum = 0;
                decimal avaqtysum = 0;
                if(ynwl == "是") //v3.0 出库绑定客户时计算有效库存
                {
                    gdqtysum = StockRelated.GetYxStockSum3rd(this.Engine, this.Request.UserContext.UserId, productId, procode, accountId);
                    avaqtysum = StockRelated.GetAvaStockSum3rd(this.Engine, this.Request.UserContext.UserId, productId, procode, accountId);
                }
                else
                {
                    //******** 动态获取库存和有效库存
                    gdqtysum = StockRelated.GetYxStockSum(this.Engine, this.Request.UserContext.UserId, productId, procode);
                    avaqtysum = StockRelated.GetAvaStockSum(this.Engine, this.Request.UserContext.UserId, productId, procode);
                }
                decimal packrate = StockRelated.packrate(this.Engine, productId, Unit); //******** 新增
                gdqtysum = gdqtysum / packrate; //******** 新增
                avaqtysum = avaqtysum / packrate; //******** 新增

                H3.DataModel.BizObject product = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId, this.Request.Engine, "D000119Product_sale", productId, false);
                if(product != null)
                {
                    Proname = product["Proname"] + string.Empty;
                    Prospec = product["Prospec"] + string.Empty;
                    DJ1 = product["DJ1"] + string.Empty;
                    DJ2 = product["DJ2"] + string.Empty;
                    DJ3 = product["DJ3"] + string.Empty;
                    DJ4 = product["DJ4"] + string.Empty;
                    DJ5 = product["DJ5"] + string.Empty;
                    huoqu = product["huoqu"] + string.Empty;
                }
                response.ReturnData = new Dictionary<string, object>();
                response.ReturnData.Add("stock", gdqtysum);
                response.ReturnData.Add("stockYX", avaqtysum);
                response.ReturnData.Add("vatPrice", vatPrice);
                response.ReturnData.Add("Tax", Tax);
                response.ReturnData.Add("netPrice", netPrice);
                response.ReturnData.Add("Proname", Proname);
                response.ReturnData.Add("Prospec", Prospec);
                response.ReturnData.Add("DJ1", DJ1);
                response.ReturnData.Add("DJ2", DJ2);
                response.ReturnData.Add("DJ3", DJ3);
                response.ReturnData.Add("DJ4", DJ4);
                response.ReturnData.Add("DJ5", DJ5);
                response.ReturnData.Add("huoqu", huoqu);
            }
            if(actionName == "GetPriceByQty2nd")
            {
                string accountId = this.Request["AccountId"] + string.Empty;//v3.0 210711 ls
                string ynwl = this.Request["ynwl"] + string.Empty; //v3.0 210711 ls
                string productId = this.Request["ProductId"] + string.Empty;
                string Unit = this.Request["Unit"] + string.Empty;
                string procode = "";
                string sqlpd = "select procode from i_D000119Product_sale where objectid='" + productId + "'";
                System.Data.DataTable resultpd = this.Request.Engine.Query.QueryTable(sqlpd, null);
                if(resultpd != null && resultpd.Rows.Count > 0)
                {
                    procode = Convert.ToString(resultpd.Rows[0]["procode"]);
                }
                decimal gdqtysum = 0;
                decimal avaqtysum = 0;
                if(ynwl == "是") //v3.0 出库绑定客户时计算有效库存
                {
                    gdqtysum = StockRelated.GetYxStockSum3rd(this.Engine, this.Request.UserContext.UserId, productId, procode, accountId);
                    avaqtysum = StockRelated.GetAvaStockSum3rd(this.Engine, this.Request.UserContext.UserId, productId, procode, accountId);
                }
                else
                {
                    //******** 动态获取库存和有效库存
                    gdqtysum = StockRelated.GetYxStockSum(this.Engine, this.Request.UserContext.UserId, productId, procode);
                    avaqtysum = StockRelated.GetAvaStockSum(this.Engine, this.Request.UserContext.UserId, productId, procode);
                }
                decimal packrate = StockRelated.packrate(this.Engine, productId, Unit); //******** 新增
                avaqtysum = avaqtysum / packrate; //******** 新增
                response.ReturnData = new Dictionary<string, object>();
                response.ReturnData.Add("stockYX", avaqtysum);
            }
            if(actionName == "GetSparePartByRT2") //获取配件清单
            {
                string productId = this.Request["ProductId"] + string.Empty;
                string procode = "";
                H3.DataModel.BizObject product = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId, this.Request.Engine, "D000119Product_sale", productId, false);
                if(product != null)
                {
                    procode = product["Procode"] + string.Empty;
                }
                //可选配件加载
                string spId = "";
                string sqlsp = "select objectid from i_D000119SalesOption where  status=1 and F0000006='" + procode + "' and ifnull(F0000001,'')='标准配件' order by createdtime desc limit 1 ";
                System.Data.DataTable resultsp = this.Request.Engine.Query.QueryTable(sqlsp, null);
                if(resultsp != null && resultsp.Rows.Count > 0)
                {
                    spId = Convert.ToString(resultsp.Rows[0]["objectid"]);
                }
                response.ReturnData = new Dictionary<string, object>();
                response.ReturnData.Add("spId", spId);
            }
            if(actionName == "UpdatePrice")
            {
                string fgmark = "";
                string ynwl = "";
                H3.DataModel.BizObject[] setup = H3.DataModel.BizObject.GetList(this.Request.Engine, this.Request.UserContext.UserId,
                    this.Request.Engine.BizObjectManager.GetPublishedSchema("D000119IntialSetup"), H3.DataModel.GetListScopeType.GlobalAll,
                    new H3.Data.Filter.Filter());
                if(setup != null && setup.Length > 0)
                {
                    if(setup[0]["F0000022"] + string.Empty == "是")
                    {
                        fgmark = "覆盖";
                    }
                    ynwl = setup[0]["F0000052"] + string.Empty;
                }
                response.ReturnData = new Dictionary<string, object>();
                response.ReturnData.Add("fgmark", fgmark);
                response.ReturnData.Add("ynwl", ynwl);
            }
            //获取辅助编码
            if(actionName == "GetSecondPcode")
            {
                string pcode = this.Request["pcode"] + string.Empty;
                string ccode = this.Request["ccode"] + string.Empty;
                string secpcode = "";
                string sql = "select t1.Addremarks from I_D000119SecondLevelMBOM t1, I_D000119createcustomer t2 where t1.pcode='" + pcode + "' and t1.Huoqu=t2.ccode and t2.objectid='" + ccode + "'";
                System.Data.DataTable result = this.Request.Engine.Query.QueryTable(sql, null);
                if(result != null && result.Rows.Count > 0)
                {
                    secpcode = result.Rows[0]["Addremarks"] + string.Empty;
                }
                response.ReturnData = new Dictionary<string, object>();
                response.ReturnData.Add("secpcode", secpcode);
                response.ReturnData.Add("sql", sql);
            }
            if(actionName == "GetBOM")
            {
                string  bomflag = "";
                H3.DataModel.BizObjectSchema schema = this.Engine.BizObjectManager.GetPublishedSchema("D000119sckdetail"); //20201118 代码升级为一张销售出库单可以包含多个CO，即CO入子表
                if(schema.GetProperty("**********") != null)
                {
                    //订单价格覆盖报价20190919
                    H3.DataModel.BizObject[] setup = H3.DataModel.BizObject.GetList(this.Request.Engine, this.Request.UserContext.UserId,
                        this.Request.Engine.BizObjectManager.GetPublishedSchema("D000119IntialSetup"), H3.DataModel.GetListScopeType.GlobalAll,
                        new H3.Data.Filter.Filter());
                    if(setup != null && setup.Length > 0)
                    {
                        if(setup[0]["F0000022"] + string.Empty == "是")
                        {
                            bomflag = "ok";
                        }
                    }
                }
                response.Message = bomflag;
            }
            if(actionName == "Submit" || actionName == "Save")//判断，走不同的审批路线
            {
                this.Request.BizObject["currency"] = postValue.Data.ContainsKey("currency") ? postValue.Data["currency"] : this.Request.BizObject["currency"];
                this.Request.BizObject["ccode"] = postValue.Data.ContainsKey("ccode") ? postValue.Data["ccode"] : this.Request.BizObject["ccode"];
                this.Request.BizObject["ARTotal"] = postValue.Data.ContainsKey("ARTotal") ? postValue.Data["ARTotal"] : this.Request.BizObject["ARTotal"];
                this.Request.BizObject["OverdueTotal"] = postValue.Data.ContainsKey("OverdueTotal") ? postValue.Data["OverdueTotal"] : this.Request.BizObject["OverdueTotal"];
                //20200529
                this.Request.BizObject["OpenClose"] = postValue.Data.ContainsKey("OpenClose") ? postValue.Data["OpenClose"] : this.Request.BizObject["OpenClose"];
                this.Request.BizObject["CalculateAPdays"] = postValue.Data.ContainsKey("CalculateAPdays") ? postValue.Data["CalculateAPdays"] : this.Request.BizObject["CalculateAPdays"];
                this.Request.BizObject["Yesnodiscount"] = postValue.Data.ContainsKey("Yesnodiscount") ? postValue.Data["Yesnodiscount"] : this.Request.BizObject["Yesnodiscount"];
                this.Request.BizObject["JKStatus"] = postValue.Data.ContainsKey("JKStatus") ? postValue.Data["JKStatus"] : this.Request.BizObject["JKStatus"];
                this.Request.BizObject["how"] = postValue.Data.ContainsKey("how") ? postValue.Data["how"] : this.Request.BizObject["how"];
                this.Request.BizObject["RT2"] = postValue.Data.ContainsKey("RT2") ? postValue.Data["RT2"] : this.Request.BizObject["RT2"];
                if(actionName == "Submit")
                {
                    H3.DataModel.BizObject[] details = (H3.DataModel.BizObject[]) this.Request.BizObject["D000119orderlist"];
                    if(details != null && details.Length > 0)
                    {
                        decimal dd = 0;
                        foreach(H3.DataModel.BizObject detail in details)
                        {
                            decimal check0 = 1;
                            if(Convert.ToDecimal(detail["sellprice"]) >= Convert.ToDecimal(detail["SalesPricebeforediscount"])) //销售价格>=销售报价
                            {
                                check0 = 0;
                            }

                            dd += Convert.ToDecimal(check0);
                            string productId = Convert.ToString(detail["pcode"]);
                            string unit = Convert.ToString(detail["unit"]);
                            string produnit = "";
                            string sqlpd = "select Unit from i_D000119Product_sale where objectid='" + productId + "'";
                            System.Data.DataTable resultpd = this.Request.Engine.Query.QueryTable(sqlpd, null);
                            if(resultpd != null && resultpd.Rows.Count > 0)
                            {
                                produnit = Convert.ToString(resultpd.Rows[0]["Unit"]);
                            }
                            if(unit != produnit)
                            {
                                string sqlpo = "select objectid from i_D0001191stlayerBOM where Pcode = '" + productId + "'and ifnull(RT1,'') = '" + unit + "'";
                                System.Data.DataTable resultpo = this.Request.Engine.Query.QueryTable(sqlpo, null);
                                if(resultpo == null || resultpo.Rows.Count == 0)
                                {
                                    response.Errors.Add("请先到:产品/基础设置/中设置销售单位|" + unit + "！");
                                    return;
                                }
                            }
                        }
                        this.Request.BizObject["InvoiceAmount"] = dd;
                    }
                    bool rateexist = false;
                    rateexist = PriceRelated.ExistChangeRate(this.Engine, this.Request.UserContext.UserId, this.Request.BizObject["currency"] + string.Empty, DateTime.Now);
                    if(!rateexist)
                    {
                        if(this.Request.BizObject["currency"] + string.Empty != "CNY")
                        {
                            response.Errors.Add("因订单货币是" + this.Request.BizObject["currency"] + "," + "请通知财务人员设置汇率赋值");
                            return;
                        }
                    }

                }
            }
        }
        if(actionName == "Submit" && this.Request.BizObject.Status == H3.DataModel.BizObjectStatus.Effective)
        {
            decimal checkclose = 0;
            H3.DataModel.BizObject[] details = (H3.DataModel.BizObject[]) this.Request.BizObject["D000119orderlist"];
            if(details != null && details.Length > 0)
            {
                foreach(H3.DataModel.BizObject detail in details)
                {
                    if(detail["pstatus"] + string.Empty == "关闭")
                    {
                        checkclose += 1;
                    }
                }
            }
            if(details.Length == checkclose)
            {
                this.Request.BizObject["PartialStock"] = "手工关闭";
            }
            //20201124 编辑修改后，写入删除日志
            mBizObject.CreateEditLog(this.Engine, this.Request.UserContext.UserId, this.Request.BizObject);
        }
        if(actionName == "Submit" && (this.Request.IsCreateMode || this.Request.BizObject.Status == H3.DataModel.BizObjectStatus.Draft))
        {
            if(this.Request.FormData.FormDataType == H3.SmartForm.SmartFormDataType.BizObject)
                this.Calcate();
        }
        //非流程删除出库单
        if(actionName == H3.SmartForm.SmartFormController.Button_Remove && this.Request.BizObject.Status == H3.DataModel.BizObjectStatus.Effective)
        {
            if(this.Request.FormData.FormDataType == H3.SmartForm.SmartFormDataType.BizObject)
            {
                this.CancelCalcate();
            }
        }
        //审批流程结束（finished状态下直接删除），就直接删除也会触发业务规则执行。在submit里面处理
        if(actionName == H3.SmartForm.SmartFormController.Button_Remove && this.Request.FormData.FormDataType == H3.SmartForm.SmartFormDataType.Workflow)
        {
            if(this.Request.WorkflowInstance != null && this.Request.WorkflowInstance.State == H3.Workflow.Instance.WorkflowInstanceState.Finished)
            {
                this.CancelCalcate();
            }
        }
        base.OnSubmit(actionName, postValue, response);
    }
    protected override void OnWorkflowInstanceStateChanged(H3.Workflow.Instance.WorkflowInstanceState oldState, H3.Workflow.Instance.WorkflowInstanceState newState)
    {
        //流程审批结束,业务规则执行。还有个审批流程结束（finished状态下直接删除），就直接删除也会触发业务规则执行。在submit里面处理
        if(oldState == H3.Workflow.Instance.WorkflowInstanceState.Running && newState == H3.Workflow.Instance.WorkflowInstanceState.Finished)
        {
            this.Calcate();
        }
        //流程审批结束后，重新激活，业务规则会执行。还有个审批流程结束（finished状态下直接删除），就直接删除也会触发业务规则执行。在submit里面处理
        if(oldState == H3.Workflow.Instance.WorkflowInstanceState.Finished && newState == H3.Workflow.Instance.WorkflowInstanceState.Running)
        {
            this.CancelCalcate();
        }
        base.OnWorkflowInstanceStateChanged(oldState, newState);
    }
    public void Calcate()
    {
        H3.DataModel.BizObjectSchema schema = this.Engine.BizObjectManager.GetPublishedSchema("D000119sckdetail"); //20201118 代码升级为一张销售出库单可以包含多个CO，即CO入子表
        if(schema.GetProperty("**********") == null)
        {
            this.CalcateOld();
        }
        else
        {
            this.Calcate20201118();
        }
    }
    public void CalcateOld()
    {
        string wouserid = this.Request.UserContext.UserId;
        H3.Workflow.Instance.WorkflowInstance wfInstance = this.Request.Engine.WorkflowInstanceManager.GetWorkflowInstance(this.Request.BizObject.WorkflowInstanceId);
        if(wfInstance != null)
        {
            wouserid = wfInstance.Originator;
        }
        bool flagJY = false; //简易流程20190919
        bool flagFG = false; //订单价格覆盖报价20190919
        bool flagNo = false; //不下推
        bool flagAB = false; //组装查询
        bool flagSY = false; //系统选择
        bool flagPL = false; //生产计划
        H3.DataModel.BizObject[] setup = H3.DataModel.BizObject.GetList(this.Request.Engine, this.Request.UserContext.UserId,
            this.Request.Engine.BizObjectManager.GetPublishedSchema("D000119IntialSetup"), H3.DataModel.GetListScopeType.GlobalAll,
            new H3.Data.Filter.Filter());
        if(setup != null && setup.Length > 0)
        {
            if(setup[0]["SalesSP"] + string.Empty == "是")
                flagJY = true;
            if(setup[0]["F0000022"] + string.Empty == "是")
                flagFG = true;
            if(setup[0]["F0000024"] + string.Empty == "不下推")
            {
                flagNo = true;
            }
            else if(setup[0]["F0000024"] + string.Empty == "组装拆卸")
            {
                flagAB = true;
            }
            else if(setup[0]["F0000024"] + string.Empty == "系统选择")
            {
                flagSY = true;
            }
            else
            {
                flagPL = true;
            }
        }
        string ccode = "";
        string fullname = "";
        H3.DataModel.BizObject customer = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId, this.Engine, "D000119createcustomer", this.Request.BizObject["ccode"] + string.Empty, false);
        if(customer != null)
        {
            ccode = customer["ccode"] + string.Empty;
            fullname = customer["cname"] + string.Empty; //********
            this.Request.BizObject["OpenClose"] = customer["cname"]; //全称
            // this.Request.BizObject["Yesnodiscount"] = customer["RT3"]; //客户地址
            // this.Request.BizObject["CalculateAPdays"] = customer["Bankaccount"]; //收货地址
            // this.Request.BizObject["JKStatus"] = customer["RT5"]; //收货人
            // this.Request.BizObject["how"] = customer["Tel"];//联系电话
        }
        string currency = this.Request.BizObject["currency"] + string.Empty;
        decimal rate = 1;
        Decimal aa = 0; //订单总数
        Decimal bb = 0; //生产总数
        decimal cc = 0; //采购或者库存充足
        rate = PriceRelated.GetExChangeRate(this.Engine, this.Request.UserContext.UserId, currency, DateTime.Now);
        H3.DataModel.BizObject[] details = (H3.DataModel.BizObject[]) this.Request.BizObject["D000119orderlist"];
        if(details != null && details.Length > 0)
        {
            foreach(H3.DataModel.BizObject detail in details)
            {
                //更新配件单
                if(this.Engine.BizObjectManager.GetPublishedSchema("D000119SalesOption") != null && detail["OptionalPart"] + string.Empty != "")
                {
                    string sql = "update I_D000119SalesOption set CO='" + this.Request.BizObject["ObjectId"] + "' where objectid='" + detail["OptionalPart"] + "' and ifnull(CO,'')=''";
                    sql += ";update I_D000119OptionList set lineId='" + detail["pcode"] + "' where parentObjectId='" + detail["OptionalPart"] + "'";
                    this.Engine.EngineConfig.CreateCommand().ExecuteScalar(sql);
                }

                if(flagFG)
                {
                    string productId = detail["pcode"] + string.Empty;
                    string unit = detail["unit"] + string.Empty;
                    bool flagx = true;
                    bool flagy = true;
                    H3.Data.Filter.Filter pricefilter = new H3.Data.Filter.Filter();  //构建过滤器
                    H3.Data.Filter.And andMatcherprice = new H3.Data.Filter.And();    //构造And匹配器
                    H3.Data.Filter.Or orMatcher = new H3.Data.Filter.Or();
                    andMatcherprice.Add(new H3.Data.Filter.ItemMatcher("Pcode", H3.Data.ComparisonOperatorType.Equal, productId)); //添加查询条件
                    andMatcherprice.Add(new H3.Data.Filter.ItemMatcher("Unit", H3.Data.ComparisonOperatorType.Equal, unit)); //增加单位一维 ********
                    andMatcherprice.Add(new H3.Data.Filter.ItemMatcher("Ccode", H3.Data.ComparisonOperatorType.Equal, this.Request.BizObject["ccode"] + string.Empty));
                    pricefilter.Matcher = andMatcherprice;
                    H3.DataModel.BizObjectSchema priceSchema = this.Request.Engine.BizObjectManager.GetPublishedSchema("D000119PriceIndex"); //最新售价
                    H3.DataModel.BizObject[] prices = H3.DataModel.BizObject.GetList(this.Request.Engine, this.Request.UserContext.UserId,
                        priceSchema, H3.DataModel.GetListScopeType.GlobalAll, pricefilter);
                    if(prices != null && prices.Length > 0)
                    {
                        prices[0]["Tax"] = detail["Tax"];
                        prices[0]["VATPrice"] = detail["sellprice"];
                        prices[0]["Netprice"] = detail["Netprice"];
                        prices[0].Update();
                        flagx = false;
                        flagy = false;
                    }
                    if(flagx) //没有终端报价，再找渠道报价 2019/04/07
                    {
                        H3.Data.Filter.Filter filter2nd = new H3.Data.Filter.Filter();  //构建过滤器
                        H3.Data.Filter.And andMatcher2nd = new H3.Data.Filter.And();    //构造And匹配器
                        H3.Data.Filter.Or orMatcher2nd = new H3.Data.Filter.Or();
                        andMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Pcode", H3.Data.ComparisonOperatorType.Equal, productId)); //添加查询条件
                        andMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Anticurrency", H3.Data.ComparisonOperatorType.Equal, this.Request.BizObject["currency"] + string.Empty));
                        andMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Unit", H3.Data.ComparisonOperatorType.Equal, unit)); //增加单位一维 ********
                        orMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Ccode", H3.Data.ComparisonOperatorType.IsNull, null));
                        orMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Ccode", H3.Data.ComparisonOperatorType.Equal, ""));
                        andMatcher2nd.Add(orMatcher2nd);
                        filter2nd.Matcher = andMatcher2nd;
                        H3.DataModel.BizObjectSchema priceSchema2nd = this.Request.Engine.BizObjectManager.GetPublishedSchema("D000119PriceIndex"); //最新售价
                        H3.DataModel.BizObject[] prices2nd = H3.DataModel.BizObject.GetList(this.Request.Engine, this.Request.UserContext.UserId,
                            priceSchema2nd, H3.DataModel.GetListScopeType.GlobalAll, filter2nd);
                        if(prices2nd != null && prices2nd.Length > 0)
                        {
                            prices2nd[0]["Tax"] = detail["Tax"];
                            prices2nd[0]["VATPrice"] = detail["sellprice"];
                            prices2nd[0]["Netprice"] = detail["Netprice"];
                            prices2nd[0].Update();
                            flagy = false;
                        }
                    }
                    if(flagy)
                    {
                        H3.DataModel.BizObject model = new H3.DataModel.BizObject(this.Engine, this.Engine.BizObjectManager.GetPublishedSchema("D000119PriceIndex"), this.Request.UserContext.UserId);
                        model["Ccode"] = this.Request.BizObject["ccode"] + string.Empty;
                        model["Cname"] = this.Request.BizObject["cnameshort"] + string.Empty;
                        model["Pcode"] = detail["pcode"];
                        model["Pname"] = detail["pname"];
                        model["Pspec"] = detail["pspec"];
                        model["Unit"] = detail["unit"];
                        model["Pstatus"] = "正常使用";
                        model["Netprice"] = detail["Netprice"];
                        model["Tax"] = detail["Tax"];
                        model["VATPrice"] = detail["sellprice"];
                        model["Anticurrency"] = this.Request.BizObject["currency"];
                        model["QuoteDate"] = DateTime.Now;
                        model["ExpireDate"] = "";
                        // model["ActualMargin"] = actualprofit;
                        model["DJ1"] = detail["DJ1"];
                        model["DJ2"] = detail["DJ2"];
                        model["DJ3"] = detail["DJ3"];
                        model["DJ4"] = detail["DJ4"];
                        model["DJ5"] = detail["DJ5"];
                        if(this.Request.BizObject["salesmanager"] != null)
                        {
                            model["OwnerId"] = this.Request.BizObject["salesmanager"];
                        }
                        else
                        {
                            model["OwnerId"] = this.Request.BizObject["OwnerId"];
                        }
                        model.Status = H3.DataModel.BizObjectStatus.Effective;
                        model.Create();
                    }
                }
                decimal check1 = 1;
                if(detail["makebuy"] + string.Empty == "采购" || detail["makebuy"] + string.Empty == "客供")
                {
                    check1 = 0;
                }
                if((detail["makebuy"] + string.Empty == "自制" || detail["makebuy"] + string.Empty == "委外") && Convert.ToDecimal(detail["processqty"]) == 0)
                {
                    check1 = 0;
                }
                detail.Update();
                aa += Convert.ToDecimal(detail["qty"]);
                bb += Convert.ToDecimal(detail["processqty"]);
                cc += check1;
                //20210123 待出库数量
                detail["check1"] = detail["qty"];
            }
            this.Request.BizObject["OrderQty"] = aa;
            this.Request.BizObject["ProductionQty"] = bb;
            this.Request.BizObject["CNY"] = Convert.ToDecimal(this.Request.BizObject["totalsales"]) * rate; //含税RMB
            this.Request.BizObject["SalesDomestic"] = Convert.ToDecimal(this.Request.BizObject["totalsalesVAT"]) * rate; //不含税RMB
            this.Request.BizObject["check1sum"] = 0;
            if(aa > 0 && bb == 0)
            {
                this.Request.BizObject["orderstatus"] = "订单进行：有库存";
            }
            else if(aa > bb && bb != 0)
            {
                this.Request.BizObject["orderstatus"] = "订单进行：部分库存";
            }
            else if(aa == bb && bb != 0 && cc != 0)
            {
                this.Request.BizObject["orderstatus"] = "订单进行：待计划";
            }
            else if(aa == bb && bb != 0 && cc == 0)
            {
                this.Request.BizObject["orderstatus"] = "订单进行：待采购";
            }
            else
            {
                this.Request.BizObject["orderstatus"] = "订单进行：待处理";
            }
        }
        //销售订单下推
        if(flagNo)
        {
        }
        else if(flagAB && bb > 0)
        {
            if(true) //生成组装拆卸
            {
                H3.DataModel.BizObject thisObj = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId, this.Engine, this.Request.BizObject.Schema.SchemaCode, this.Request.BizObjectId, false);
                SalesOrder salesOrder = new SalesOrder();
                salesOrder.CreateAssemble(this.Engine, wouserid, thisObj, flagAB);
            }
            if(true)//生产采购订单
            {
                foreach(H3.DataModel.BizObject detail in details) //details 是销售订单明细
                {
                    if(detail["makebuy"] + string.Empty == "采购" && Convert.ToDecimal(detail["processqty"]) > 0)
                    {
                        DateTime maindate = Convert.ToDateTime(this.Request.BizObject["deliverydate"]);
                        DateTime detaildate = Convert.ToDateTime(detail["RE1"]);
                        string coId = this.Request.BizObjectId;
                        string cname = Convert.ToString(this.Request.BizObject["cnameshort"]);
                        string remark = Convert.ToString(this.Request.BizObject["ProdBase"]);
                        string cusseqno = "";
                        try
                        {
                            cusseqno = Convert.ToString(this.Request.BizObject["CoNo20200722"]);
                        }
                        catch(Exception e)
                        { }
                        decimal packrate = StockRelated.packrate(this.Engine, Convert.ToString(detail["pcode"]), Convert.ToString(detail["unit"])); //******** 新增
                        SalesOrder.CreatePo(this.Engine, wouserid, detail["pcode"] + string.Empty, Convert.ToDecimal(detail["processqty"]) * packrate, "销售订单", this.Request.BizObjectId, maindate, detaildate, coId, cname, cusseqno, remark, "");
                    }
                }
            }
        }
        else if(flagSY && bb > 0)
        {
            if(true) //生成组装拆卸
            {
                H3.DataModel.BizObject thisObj = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId, this.Engine, this.Request.BizObject.Schema.SchemaCode, this.Request.BizObjectId, false);
                SalesOrder salesOrder = new SalesOrder();
                salesOrder.CreateAssemble(this.Engine, wouserid, thisObj, flagAB);
            }
            if(true) //生成生产计划
            {
                H3.DataModel.BizObject thisObj = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId, this.Engine, this.Request.BizObject.Schema.SchemaCode, this.Request.BizObjectId, false);
                SalesOrder salesOrder = new SalesOrder();
                salesOrder.CreatePlan(this.Engine, wouserid, thisObj, flagPL);
            }
            if(true)//生产采购订单
            {
                foreach(H3.DataModel.BizObject detail in details) //details 是销售订单明细
                {
                    if(detail["makebuy"] + string.Empty == "采购" && Convert.ToDecimal(detail["processqty"]) > 0)
                    {
                        DateTime maindate = Convert.ToDateTime(this.Request.BizObject["deliverydate"]);
                        DateTime detaildate = Convert.ToDateTime(detail["RE1"]);
                        string coId = this.Request.BizObjectId;
                        string cname = Convert.ToString(this.Request.BizObject["cnameshort"]);
                        string remark = Convert.ToString(this.Request.BizObject["ProdBase"]);
                        string cusseqno = "";
                        try
                        {
                            cusseqno = Convert.ToString(this.Request.BizObject["CoNo20200722"]);
                        }
                        catch(Exception e)
                        { }
                        decimal packrate = StockRelated.packrate(this.Engine, Convert.ToString(detail["pcode"]), Convert.ToString(detail["unit"])); //******** 新增
                        SalesOrder.CreatePo(this.Engine, wouserid, detail["pcode"] + string.Empty, Convert.ToDecimal(detail["processqty"]) * packrate, "销售订单", this.Request.BizObjectId, maindate, detaildate, coId, cname, cusseqno, remark, "");
                    }
                }
            }
        }
        else if(bb > 0)
        {
            if(true) //生成生产计划
            {
                H3.DataModel.BizObject thisObj = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId, this.Engine, this.Request.BizObject.Schema.SchemaCode, this.Request.BizObjectId, false);
                SalesOrder salesOrder = new SalesOrder();
                salesOrder.CreatePlan(this.Engine, wouserid, thisObj, flagPL);
            }
            if(true)//生产采购订单
            {
                foreach(H3.DataModel.BizObject detail in details) //details 是销售订单明细
                {
                    if(detail["makebuy"] + string.Empty == "采购" && Convert.ToDecimal(detail["processqty"]) > 0)
                    {
                        DateTime maindate = Convert.ToDateTime(this.Request.BizObject["deliverydate"]);
                        DateTime detaildate = Convert.ToDateTime(detail["RE1"]);
                        string coId = this.Request.BizObjectId;
                        string cname = Convert.ToString(this.Request.BizObject["cnameshort"]);
                        string remark = Convert.ToString(this.Request.BizObject["ProdBase"]);
                        string cusseqno = "";
                        try
                        {
                            cusseqno = Convert.ToString(this.Request.BizObject["CoNo20200722"]);
                        }
                        catch(Exception e)
                        { }
                        decimal packrate = StockRelated.packrate(this.Engine, Convert.ToString(detail["pcode"]), Convert.ToString(detail["unit"])); //******** 新增
                        SalesOrder.CreatePo(this.Engine, wouserid, detail["pcode"] + string.Empty, Convert.ToDecimal(detail["processqty"]) * packrate, "销售订单", this.Request.BizObjectId, maindate, detaildate, coId, cname, cusseqno, remark, "");
                    }
                }
            }
        }
        //销售端简易流程 - 生成销售出库单
        H3.DataModel.BizObjectSchema chukuSchema = this.Engine.BizObjectManager.GetPublishedSchema("D000119wocuku");
        H3.DataModel.BizObjectSchema thisSchema = this.Request.Schema;
        H3.DataModel.BizObject po = new H3.DataModel.BizObject(this.Engine, chukuSchema, this.Request.UserContext.UserId);
        if(flagJY)
        {
            List < H3.DataModel.BizObject > detailList = new List<H3.DataModel.BizObject>();
            H3.DataModel.BizObjectSchema poChildSchema = this.Engine.BizObjectManager.GetPublishedSchema("D000119sckdetail");
            H3.DataModel.BizObject[] thisDetails = (H3.DataModel.BizObject[]) this.Request.BizObject["D000119orderlist"];
            if(thisDetails != null && thisDetails.Length > 0)
            {
                foreach(H3.DataModel.BizObject thisDetail in thisDetails)
                {
                    H3.DataModel.BizObject poDetail = new H3.DataModel.BizObject(this.Engine, poChildSchema, this.Request.UserContext.UserId);
                    // 增加子表信息
                    poDetail["Deliverydate"] = thisDetail["RE1"];
                    poDetail["QtyApply"] = thisDetail["qty"];
                    poDetail["orderqty"] = thisDetail["qty"];
                    poDetail["pcode"] = thisDetail["pcode"];
                    poDetail["Unit2"] = thisDetail["PcodeS"];
                    poDetail["pname"] = thisDetail["pname"];
                    poDetail["pspec"] = thisDetail["pspec"];
                    poDetail["unit"] = thisDetail["unit"];
                    poDetail["Unit2"] = thisDetail["PcodeS"]; //辅助编码
                    poDetail["DJ1"] = thisDetail["DJ1"];
                    poDetail["DJ2"] = thisDetail["DJ2"];
                    poDetail["DJ3"] = thisDetail["DJ3"];
                    poDetail["DJ4"] = thisDetail["DJ4"];
                    poDetail["DJ5"] = thisDetail["DJ5"];
                    poDetail["Realnetprice"] = thisDetail["Netprice"];
                    poDetail["Tax"] = thisDetail["Tax"];
                    poDetail["netprice"] = thisDetail["sellprice"];
                    poDetail["Othertx"] = thisDetail["requirement"]; //20191212 赋值
                    detailList.Add(poDetail);
                }
            }
            po["D000119sckdetail"] = detailList.ToArray();
            //增加主表信息
            po["sckstatus"] = "订单进行：待出库";
            if(chukuSchema.GetProperty("Reasons") != null)
            {
                po["Reasons"] = this.Request.BizObject["ProdBase"]; // ******** 订单说明
            }
            if(chukuSchema.GetProperty("Customer") != null)
            {
                po["Customer"] = fullname;
                po["Employee"] = this.Request.BizObject["salesmanager"];
            }
            po["Costatusbefore"] = "简易";
            po["RT4"] = this.Request.BizObject["CalculateAPdays"]; //收货地址 ********
            po["Currency"] = this.Request.BizObject["JKStatus"]; //收货人 ********
            po["Codes"] = this.Request.BizObject["how"]; //电话 ********
            po["RT5"] = this.Request.BizObject["RT5"]; //订单类型
            po["cnameshort"] = this.Request.BizObject["cnameshort"];
            po["deliverydate"] = this.Request.BizObject["deliverydaterequired"];
            // po["ckqtytotal"] = this.Request.BizObject["OrderQty"]; //
            po["orderqtytotal"] = this.Request.BizObject["OrderQty"]; //
            po["ccode"] = this.Request.BizObject["ccode"]; //
            po["co"] = this.Request.BizObjectId; //
            po["COString"] = this.Request.BizObject["SeqNo"];
            try
            {
                po["CoNo20200722"] = this.Request.BizObject["CoNo20200722"];
            }
            catch(Exception e)
            {
            }
            //销售订单 - 客户信息相关的可传递
            po["RT1"] = this.Request.BizObject["RT1"];
            po["RT2"] = this.Request.BizObject["RT2"];
            po["RT3"] = this.Request.BizObject["RT3"];
            po["RD1"] = this.Request.BizObject["RD1"];
            po["RD2"] = this.Request.BizObject["RD2"];
            po["RD3"] = this.Request.BizObject["RD3"];
            po["RE1"] = this.Request.BizObject["RE1"];
            po["RP1"] = this.Request.BizObject["RP1"];
            string instanceId = System.Guid.NewGuid().ToString();
            if(string.IsNullOrEmpty(po.WorkflowInstanceId))
            {
                po.WorkflowInstanceId = instanceId;
            }
            H3.ErrorCode createResult = po.Create();
            if(createResult == H3.ErrorCode.Success)
            {
                //启动流程
                string workItemID = string.Empty;
                string errorMsg = string.Empty;
                H3.Workflow.Template.WorkflowTemplate wfTemp = this.Request.Engine.WorkflowTemplateManager.GetDefaultWorkflow(po.Schema.SchemaCode);
                this.Request.Engine.Interactor.OriginateInstance(wouserid, po.Schema.SchemaCode,
                    wfTemp.WorkflowVersion, po.ObjectId, po.WorkflowInstanceId, H3.Workflow.WorkItem.AccessMethod.Web,
                    true, string.Empty, true, out workItemID, out  errorMsg);
            }
            this.Request.BizObject["InvoiceNo"] = "简易流程";
        }
        // 如果是预收订单，生成客户回款
        H3.DataModel.BizObjectSchema poSchema1 = this.Engine.BizObjectManager.GetPublishedSchema("D000119AR01");
        H3.DataModel.BizObjectSchema thisSchema1 = this.Request.Schema;
        if(this.Request.BizObject["YNPayAdvance"] + string.Empty == "是" && Convert.ToDecimal(this.Request.BizObject["YSAmount"]) > 0) //是否是预付
        {
            H3.DataModel.BizObject po1 = new H3.DataModel.BizObject(this.Engine, poSchema1, this.Request.UserContext.UserId);
            H3.IEngine engine = this.Engine;
            string userId = this.Request.UserContext.UserId;
            var cnames = this.Request.BizObject["cnameshort"] + string.Empty;
            if(poSchema1.GetProperty("Customer") != null)
            {
                po1["Customer"] = this.Request.BizObject["cnameshort"] + string.Empty;
            }
            if(poSchema1.GetProperty("Fd658be0f186c4f6387747718ebd94259") != null)
            {
                //   po1["PAamount"] = this.Request.BizObject["YSAmount"]; //回款金额  20201116 屏蔽，改为不同的控件 Fd658be0f186c4f6387747718ebd94259
                po1["Fd658be0f186c4f6387747718ebd94259"] = this.Request.BizObject["YSAmount"]; //回款金额
            }
            if(poSchema1.GetProperty("PerCo0724") != null) //新增 20201116
            {
                po1["PerCo0724"] = this.Request.BizObjectId;
                po1["CoNo20200722"] = this.Request.BizObject["CoNo20200722"];
            }
            po1["CustomerCurrency"] = this.Request.BizObject["currency"] + string.Empty;
            po1["Remark"] = "收" + this.Request.BizObject["cnameshort"] + "的预付款";
            po1["CodeplusDate"] = "系统提交";
            po1["RT1"] = "销售预收";
            po1["Ccode"] = this.Request.BizObject["ccode"];
            po1["CO"] = this.Request.BizObjectId;
            // po1["Codes"] = "是";//定向回款订单 20190603
            string instanceId = System.Guid.NewGuid().ToString();
            if(string.IsNullOrEmpty(po1.WorkflowInstanceId))
            {
                po1.WorkflowInstanceId = instanceId;
            }
            H3.ErrorCode createResult = po1.Create();
            if(createResult == H3.ErrorCode.Success)
            {
                //启动流程
                string workItemID = string.Empty;
                string errorMsg = string.Empty;
                H3.Workflow.Template.WorkflowTemplate wfTemp = this.Request.Engine.WorkflowTemplateManager.GetDefaultWorkflow(po1.Schema.SchemaCode);
                this.Request.Engine.Interactor.OriginateInstance(wouserid, po1.Schema.SchemaCode,
                    wfTemp.WorkflowVersion, po1.ObjectId, po1.WorkflowInstanceId, H3.Workflow.WorkItem.AccessMethod.Web,
                    true, string.Empty, true, out workItemID, out  errorMsg);
            }
        }
        this.Request.BizObject["ApprovedDate"] = DateTime.Now; //LS added on Jan 25th,2018
        this.Request.BizObject["COchange"] = "订单提交"; //LS added on Jan 26th,2018
        this.Request.BizObject.Update();

        //KPI 销售订单达成率
        decimal  order = Convert.ToDecimal(this.Request.BizObject["CNY"]); // 合同总金额
        System.DateTime currentTime = new System.DateTime();
        currentTime = System.DateTime.Now;
        //获取当前年，月
        var kpiyear = Convert.ToString(currentTime.Year);
        var kpimonth = Convert.ToDecimal(currentTime.Month);
        H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();
        filter.Matcher = new H3.Data.Filter.ItemMatcher("SingleSubmit", H3.Data.ComparisonOperatorType.Equal, kpiyear);
        H3.DataModel.BizObject[] list = H3.DataModel.BizObject.GetList(this.Engine, this.Request.UserContext.UserId, this.Engine.BizObjectManager.GetPublishedSchema("D000119orderachrate"),
            H3.DataModel.GetListScopeType.GlobalAll, filter);
        if(list != null && list.Length > 0)
        {
            H3.DataModel.BizObject[] kpidetails = (H3.DataModel.BizObject[]) list[0]["D000119orderweek"];
            if(kpidetails != null && kpidetails.Length > 0)
            {
                foreach(H3.DataModel.BizObject detail in kpidetails)
                {
                    if(Convert.ToDecimal(detail["month"]) == kpimonth)
                    {
                        detail["order"] = Convert.ToDecimal(detail["order"]) + order;
                        if(Convert.ToDecimal(detail["Mordertarget"]) > 0)
                        {
                            detail["actual"] = Convert.ToDecimal(detail["order"]) / Convert.ToDecimal(detail["Mordertarget"]);
                        }
                        detail.Update();
                        break;
                    }
                }
            }
        }
        else
        {
            H3.DataModel.BizObjectSchema kpiSchema = this.Engine.BizObjectManager.GetPublishedSchema("D000119orderachrate");
            H3.DataModel.BizObjectSchema kpichildSchema = this.Engine.BizObjectManager.GetPublishedSchema("D000119orderweek");
            H3.DataModel.BizObject kpi = new H3.DataModel.BizObject(this.Engine, kpiSchema, this.Request.UserContext.UserId);
            List < H3.DataModel.BizObject > detailList = new List<H3.DataModel.BizObject>();
            for(int i = 0;i < 12; i++)
            {
                H3.DataModel.BizObject kpichild = new H3.DataModel.BizObject(this.Engine, kpichildSchema, this.Request.UserContext.UserId);
                kpichild["month"] = i + 1;
                if(kpimonth == i + 1)
                {
                    kpichild["order"] = Convert.ToDecimal(kpichild["order"]) + order;
                    if(Convert.ToDecimal(kpichild["Mordertarget"]) > 0)
                    {
                        kpichild["actual"] = Convert.ToDecimal(kpichild["order"]) / Convert.ToDecimal(kpichild["Mordertarget"]);
                    }
                }
                detailList.Add(kpichild);
            }
            kpi["D000119orderweek"] = detailList.ToArray();
            kpi["SingleSubmit"] = kpiyear;
            kpi.Status = H3.DataModel.BizObjectStatus.Effective;
            kpi.Create();
        }
    }
    public void Calcate20201118()
    {
        //20210704 v3.0 代码升级
        bool flag30 = false;
        H3.DataModel.BizObjectSchema schema30 = this.Engine.BizObjectManager.GetPublishedSchema("D000119OpenGuide");
        if(schema30.GetProperty("rev20210831") != null)
        {
            flag30 = true;
        }
        string wouserid = this.Request.UserContext.UserId;
        H3.Workflow.Instance.WorkflowInstance wfInstance = this.Request.Engine.WorkflowInstanceManager.GetWorkflowInstance(this.Request.BizObject.WorkflowInstanceId);
        if(wfInstance != null)
        {
            wouserid = wfInstance.Originator;
        }
        bool flagJY = false; //简易流程20190919
        bool flagFG = false; //订单价格覆盖报价20190919
        bool flagNo = false; //不下推
        bool flagAB = false; //组装查询
        bool flagSY = false; //系统选择
        bool flagPL = false; //生产计划
        H3.DataModel.BizObject[] setup = H3.DataModel.BizObject.GetList(this.Request.Engine, this.Request.UserContext.UserId,
            this.Request.Engine.BizObjectManager.GetPublishedSchema("D000119IntialSetup"), H3.DataModel.GetListScopeType.GlobalAll,
            new H3.Data.Filter.Filter());
        if(setup != null && setup.Length > 0)
        {
            if(setup[0]["SalesSP"] + string.Empty == "是")
                flagJY = true;
            if(setup[0]["F0000022"] + string.Empty == "是")
                flagFG = true;
            if(setup[0]["F0000024"] + string.Empty == "不下推")
            {
                flagNo = true;
            }
            else if(setup[0]["F0000024"] + string.Empty == "组装拆卸")
            {
                flagAB = true;
            }
            else if(setup[0]["F0000024"] + string.Empty == "系统选择")
            {
                flagSY = true;
            }
            else
            {
                flagPL = true;
            }
        }
        string ccode = "";
        string fullname = "";
        H3.DataModel.BizObject customer = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId, this.Engine, "D000119createcustomer", this.Request.BizObject["ccode"] + string.Empty, false);
        if(customer != null)
        {
            ccode = customer["ccode"] + string.Empty;
            fullname = customer["cname"] + string.Empty; //********
            this.Request.BizObject["OpenClose"] = customer["cname"]; //全称
        }
        string currency = this.Request.BizObject["currency"] + string.Empty;
        decimal rate = 1;
        Decimal aa = 0; //订单总数
        Decimal bb = 0; //生产总数
        decimal cc = 0; //采购或者库存充足
        rate = PriceRelated.GetExChangeRate(this.Engine, this.Request.UserContext.UserId, currency, DateTime.Now);
        H3.DataModel.BizObject[] details = (H3.DataModel.BizObject[]) this.Request.BizObject["D000119orderlist"];
        if(details != null && details.Length > 0)
        {
            foreach(H3.DataModel.BizObject detail in details)
            {
                string productId = detail["pcode"] + string.Empty;
                string unit = detail["unit"] + string.Empty;
                string procode = "";
                string sqlpd = "select procode from i_D000119Product_sale where objectid='" + productId + "'";
                System.Data.DataTable resultpd = this.Request.Engine.Query.QueryTable(sqlpd, null);
                if(resultpd != null && resultpd.Rows.Count > 0)
                {
                    procode = Convert.ToString(resultpd.Rows[0]["procode"]);
                }
                //更新配件单
                if(this.Engine.BizObjectManager.GetPublishedSchema("D000119SalesOption") != null && detail["OptionalPart"] + string.Empty != "")
                {
                    List < H3.DataModel.BizObject > detailList = new List<H3.DataModel.BizObject>();
                    H3.DataModel.BizObject psObj = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId, this.Request.Engine, "D000119SalesOption", detail["OptionalPart"] + string.Empty, false);
                    if(psObj != null)
                    {
                        H3.DataModel.BizObject[] spdetails = (H3.DataModel.BizObject[]) psObj["D000119OptionList"];
                        if(spdetails != null && spdetails.Length > 0)
                        {
                            foreach(H3.DataModel.BizObject spdetail in spdetails)
                            {
                                decimal qty = Convert.ToDecimal(spdetail["qty"]);
                                if(qty <= 0)
                                {
                                    continue;
                                }
                                H3.DataModel.BizObject d = new H3.DataModel.BizObject(this.Engine, this.Engine.BizObjectManager.GetPublishedSchema("D000119OptionList"), this.Request.UserContext.UserId);
                                d["pcode"] = spdetail["pcode"];
                                d["pname"] = spdetail["pname"];
                                d["psepc"] = spdetail["psepc"];
                                d["unit"] = spdetail["unit"];
                                d["makebuy"] = spdetail["makebuy"];
                                d["qty"] = spdetail["qty"]; //订单用量
                                detailList.Add(d);
                            }
                        }
                        if(detailList != null && detailList.Count > 0)
                        {
                            H3.DataModel.BizObjectSchema spSchema = this.Engine.BizObjectManager.GetPublishedSchema("D000119SalesOption");
                            H3.DataModel.BizObject wocuku = new H3.DataModel.BizObject(this.Engine, spSchema, this.Request.UserContext.UserId);
                            wocuku["F0000006"] = procode;
                            wocuku["F0000007"] = DateTime.Now.ToString("yyyy-MM-dd");
                            wocuku["F0000001"] = "销售配件";
                            wocuku["F0000009"] = detail["qty"];
                            wocuku["CO"] = this.Request.BizObjectId;
                            wocuku["D000119OptionList"] = detailList.ToArray();
                            wocuku.Status = H3.DataModel.BizObjectStatus.Effective;
                            wocuku.Create();
                            detail["OptionalPart"] = wocuku.ObjectId;

                        }
                        psObj.Update();
                    }
                }
                if(flagFG)
                {
                    string YNfg = detail["Color"] + string.Empty; //是否覆盖，选择是或空覆盖，选择否不覆盖，主要解决同一个产品，子表多行的问题
                    if(YNfg == "" || YNfg == "是")
                    {
                        bool flagx = true;
                        bool flagy = true;
                        H3.Data.Filter.Filter pricefilter = new H3.Data.Filter.Filter();  //构建过滤器
                        H3.Data.Filter.And andMatcherprice = new H3.Data.Filter.And();    //构造And匹配器
                        H3.Data.Filter.Or orMatcher = new H3.Data.Filter.Or();
                        andMatcherprice.Add(new H3.Data.Filter.ItemMatcher("Pcode", H3.Data.ComparisonOperatorType.Equal, productId)); //添加查询条件
                        andMatcherprice.Add(new H3.Data.Filter.ItemMatcher("Unit", H3.Data.ComparisonOperatorType.Equal, unit)); //增加单位一维 ********
                        andMatcherprice.Add(new H3.Data.Filter.ItemMatcher("Ccode", H3.Data.ComparisonOperatorType.Equal, this.Request.BizObject["ccode"] + string.Empty));
                        pricefilter.Matcher = andMatcherprice;
                        H3.DataModel.BizObjectSchema priceSchema = this.Request.Engine.BizObjectManager.GetPublishedSchema("D000119PriceIndex"); //最新售价
                        H3.DataModel.BizObject[] prices = H3.DataModel.BizObject.GetList(this.Request.Engine, this.Request.UserContext.UserId,
                            priceSchema, H3.DataModel.GetListScopeType.GlobalAll, pricefilter);
                        if(prices != null && prices.Length > 0)
                        {
                            prices[0]["Tax"] = detail["Tax"];
                            prices[0]["VATPrice"] = detail["sellprice"];
                            prices[0]["Netprice"] = detail["Netprice"];
                            prices[0].Update();
                            flagx = false;
                            flagy = false;
                        }
                        if(flagx) //没有终端报价，再找渠道报价 2019/04/07
                        {
                            H3.Data.Filter.Filter filter2nd = new H3.Data.Filter.Filter();  //构建过滤器
                            H3.Data.Filter.And andMatcher2nd = new H3.Data.Filter.And();    //构造And匹配器
                            H3.Data.Filter.Or orMatcher2nd = new H3.Data.Filter.Or();
                            andMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Pcode", H3.Data.ComparisonOperatorType.Equal, productId)); //添加查询条件
                            andMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Anticurrency", H3.Data.ComparisonOperatorType.Equal, this.Request.BizObject["currency"] + string.Empty));
                            andMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Unit", H3.Data.ComparisonOperatorType.Equal, unit)); //增加单位一维 ********
                            orMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Ccode", H3.Data.ComparisonOperatorType.IsNull, null));
                            orMatcher2nd.Add(new H3.Data.Filter.ItemMatcher("Ccode", H3.Data.ComparisonOperatorType.Equal, ""));
                            andMatcher2nd.Add(orMatcher2nd);
                            filter2nd.Matcher = andMatcher2nd;
                            H3.DataModel.BizObjectSchema priceSchema2nd = this.Request.Engine.BizObjectManager.GetPublishedSchema("D000119PriceIndex"); //最新售价
                            H3.DataModel.BizObject[] prices2nd = H3.DataModel.BizObject.GetList(this.Request.Engine, this.Request.UserContext.UserId,
                                priceSchema2nd, H3.DataModel.GetListScopeType.GlobalAll, filter2nd);
                            if(prices2nd != null && prices2nd.Length > 0)
                            {
                                prices2nd[0]["Tax"] = detail["Tax"];
                                prices2nd[0]["VATPrice"] = detail["sellprice"];
                                prices2nd[0]["Netprice"] = detail["Netprice"];
                                prices2nd[0].Update();
                                flagy = false;
                            }
                        }
                        if(flagy)
                        {
                            H3.DataModel.BizObject model = new H3.DataModel.BizObject(this.Engine, this.Engine.BizObjectManager.GetPublishedSchema("D000119PriceIndex"), this.Request.UserContext.UserId);
                            model["Ccode"] = this.Request.BizObject["ccode"] + string.Empty;
                            model["Cname"] = this.Request.BizObject["cnameshort"] + string.Empty;
                            model["Pcode"] = detail["pcode"];
                            model["Pname"] = detail["pname"];
                            model["Pspec"] = detail["pspec"];
                            model["Unit"] = detail["unit"];
                            model["Pstatus"] = "正常使用";
                            model["Netprice"] = detail["Netprice"];
                            model["Tax"] = detail["Tax"];
                            model["VATPrice"] = detail["sellprice"];
                            model["Anticurrency"] = this.Request.BizObject["currency"];
                            model["QuoteDate"] = DateTime.Now;
                            model["ExpireDate"] = "";
                            // model["ActualMargin"] = actualprofit;
                            model["DJ1"] = detail["DJ1"];
                            model["DJ2"] = detail["DJ2"];
                            model["DJ3"] = detail["DJ3"];
                            model["DJ4"] = detail["DJ4"];
                            model["DJ5"] = detail["DJ5"];
                            if(this.Request.BizObject["salesmanager"] != null)
                            {
                                model["OwnerId"] = this.Request.BizObject["salesmanager"];
                            }
                            else
                            {
                                model["OwnerId"] = this.Request.BizObject["OwnerId"];
                            }
                            model.Status = H3.DataModel.BizObjectStatus.Effective;
                            model.Create();
                        }
                    }
                }
                decimal check1 = 1;
                if(detail["makebuy"] + string.Empty == "采购" || detail["makebuy"] + string.Empty == "客供")
                {
                    check1 = 0;
                }
                if((detail["makebuy"] + string.Empty == "自制" || detail["makebuy"] + string.Empty == "委外") && Convert.ToDecimal(detail["processqty"]) == 0)
                {
                    check1 = 0;
                }
                detail.Update();
                aa += Convert.ToDecimal(detail["qty"]);
                bb += Convert.ToDecimal(detail["processqty"]);
                cc += check1;
                //安全库存触发
                StockRelated.TriggerbyYXstock(this.Engine, this.Request.UserContext.UserId, productId, procode);
                //20210123 待出库数量
                detail["check1"] = detail["qty"];
            }
            this.Request.BizObject["OrderQty"] = aa;
            this.Request.BizObject["ProductionQty"] = bb;
            this.Request.BizObject["CNY"] = Convert.ToDecimal(this.Request.BizObject["totalsales"]) * rate; //含税RMB
            this.Request.BizObject["SalesDomestic"] = Convert.ToDecimal(this.Request.BizObject["totalsalesVAT"]) * rate; //不含税RMB
            this.Request.BizObject["check1sum"] = 0;
            if(aa > 0 && bb == 0)
            {
                this.Request.BizObject["orderstatus"] = "订单进行：有库存";
            }
            else if(aa > bb && bb != 0)
            {
                this.Request.BizObject["orderstatus"] = "订单进行：部分库存";
            }
            else if(aa == bb && bb != 0 && cc != 0)
            {
                this.Request.BizObject["orderstatus"] = "订单进行：待计划";
            }
            else if(aa == bb && bb != 0 && cc == 0)
            {
                this.Request.BizObject["orderstatus"] = "订单进行：待采购";
            }
            else
            {
                this.Request.BizObject["orderstatus"] = "订单进行：待处理";
            }
        }
        //销售订单下推
        if(flagNo)
        {
        }
        else if(flagAB && bb > 0)
        {
            if(true) //生成组装拆卸
            {
                H3.DataModel.BizObject thisObj = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId, this.Engine, this.Request.BizObject.Schema.SchemaCode, this.Request.BizObjectId, false);
                SalesOrder salesOrder = new SalesOrder();
                salesOrder.CreateAssemble2nd(this.Engine, wouserid, thisObj, flagAB);
            }
            if(true)//生产采购订单
            {
                DateTime maindate = Convert.ToDateTime(this.Request.BizObject["deliverydate"]);
                string coId = this.Request.BizObjectId;
                string cname = Convert.ToString(this.Request.BizObject["cnameshort"]);
                string remark = Convert.ToString(this.Request.BizObject["ProdBase"]);
                string cusseqno = "";
                try
                {
                    cusseqno = Convert.ToString(this.Request.BizObject["CoNo20200722"]);
                }
                catch(Exception e)
                { }
                string sqlpo = "select t2.objectid as objectid, t2.pcode as pcode, t2.unit as unit,ifnull(t2.RE1,now()) as detaildate, ifnull(sum(t2.processqty),0) as processqty from i_D000119orderlist t2,i_D000119salesorder t1 where t2.parentobjectid=t1.objectid and t1.objectid='" + coId + "' and ifnull(t2.makebuy,'')='采购' and ifnull(t2.processqty,0)>0 group by t2.pcode ";
                System.Data.DataTable resultpo = this.Request.Engine.Query.QueryTable(sqlpo, null);
                if(resultpo != null && resultpo.Rows.Count > 0)
                {
                    foreach(System.Data.DataRow row in resultpo.Rows)
                    {
                        string lineId = Convert.ToString(row["objectid"]);
                        string pcode = Convert.ToString(row["pcode"]);
                        string unit = Convert.ToString(row["unit"]);
                        DateTime detaildate = Convert.ToDateTime(row["detaildate"]);
                        decimal processqty = Convert.ToDecimal(row["processqty"]);
                        decimal packrate = StockRelated.packrate(this.Engine, pcode, unit); //******** 新增
                        SalesOrder.CreatePo2nd(this.Engine, wouserid, lineId, pcode, processqty * packrate, "销售订单", this.Request.BizObjectId, maindate, detaildate, coId, cname, cusseqno, remark, "");
                    }
                }

            }
        }
        else if(flagSY && bb > 0)
        {
            if(true) //生成组装拆卸
            {
                H3.DataModel.BizObject thisObj = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId, this.Engine, this.Request.BizObject.Schema.SchemaCode, this.Request.BizObjectId, false);
                SalesOrder salesOrder = new SalesOrder();
                salesOrder.CreateAssemble2nd(this.Engine, wouserid, thisObj, flagAB);
            }
            if(true) //生成生产计划
            {
                H3.DataModel.BizObject thisObj = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId, this.Engine, this.Request.BizObject.Schema.SchemaCode, this.Request.BizObjectId, false);
                SalesOrder salesOrder = new SalesOrder();
                salesOrder.CreatePlan2nd(this.Engine, wouserid, thisObj, flagPL);
            }
            if(true)//生产采购订单
            {
                DateTime maindate = Convert.ToDateTime(this.Request.BizObject["deliverydate"]);
                string coId = this.Request.BizObjectId;
                string cname = Convert.ToString(this.Request.BizObject["cnameshort"]);
                string remark = Convert.ToString(this.Request.BizObject["ProdBase"]);
                string cusseqno = "";
                try
                {
                    cusseqno = Convert.ToString(this.Request.BizObject["CoNo20200722"]);
                }
                catch(Exception e)
                { }
                string sqlpo = "select t2.objectid as objectid,t2.pcode as pcode, t2.unit as unit,ifnull(t2.RE1,now()) as detaildate, ifnull(sum(t2.processqty),0) as processqty from i_D000119orderlist t2,i_D000119salesorder t1 where t2.parentobjectid=t1.objectid and t1.objectid='" + coId + "' and ifnull(t2.makebuy,'')='采购' and ifnull(t2.processqty,0)>0 group by t2.pcode ";
                System.Data.DataTable resultpo = this.Request.Engine.Query.QueryTable(sqlpo, null);
                if(resultpo != null && resultpo.Rows.Count > 0)
                {
                    foreach(System.Data.DataRow row in resultpo.Rows)
                    {
                        string lineId = Convert.ToString(row["objectid"]);
                        string pcode = Convert.ToString(row["pcode"]);
                        string unit = Convert.ToString(row["unit"]);
                        DateTime detaildate = Convert.ToDateTime(row["detaildate"]);
                        decimal processqty = Convert.ToDecimal(row["processqty"]);
                        decimal packrate = StockRelated.packrate(this.Engine, pcode, unit); //******** 新增
                        SalesOrder.CreatePo2nd(this.Engine, wouserid, lineId, pcode, processqty * packrate, "销售订单", this.Request.BizObjectId, maindate, detaildate, coId, cname, cusseqno, remark, "");
                    }
                }

            }
        }
        else if(bb > 0)
        {
            if(true) //生成生产计划
            {
                H3.DataModel.BizObject thisObj = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId, this.Engine, this.Request.BizObject.Schema.SchemaCode, this.Request.BizObjectId, false);
                SalesOrder salesOrder = new SalesOrder();
                salesOrder.CreatePlan2nd(this.Engine, wouserid, thisObj, flagPL);
            }
            if(true)//生产采购订单
            {
                DateTime maindate = Convert.ToDateTime(this.Request.BizObject["deliverydate"]);
                string coId = this.Request.BizObjectId;
                string cname = Convert.ToString(this.Request.BizObject["cnameshort"]);
                string remark = Convert.ToString(this.Request.BizObject["ProdBase"]);
                string cusseqno = "";
                try
                {
                    cusseqno = Convert.ToString(this.Request.BizObject["CoNo20200722"]);
                }
                catch(Exception e)
                { }
                string sqlpo = "select t2.objectid as objectid,t2.pcode as pcode, t2.unit as unit,ifnull(t2.RE1,now()) as detaildate, ifnull(sum(t2.processqty),0) as processqty from i_D000119orderlist t2,i_D000119salesorder t1 where t2.parentobjectid=t1.objectid and t1.objectid='" + coId + "' and ifnull(t2.makebuy,'')='采购' and ifnull(t2.processqty,0)>0 group by t2.pcode ";
                System.Data.DataTable resultpo = this.Request.Engine.Query.QueryTable(sqlpo, null);
                if(resultpo != null && resultpo.Rows.Count > 0)
                {
                    foreach(System.Data.DataRow row in resultpo.Rows)
                    {
                        string lineId = Convert.ToString(row["objectid"]);
                        string pcode = Convert.ToString(row["pcode"]);
                        string unit = Convert.ToString(row["unit"]);
                        DateTime detaildate = Convert.ToDateTime(row["detaildate"]);
                        decimal processqty = Convert.ToDecimal(row["processqty"]);
                        decimal packrate = StockRelated.packrate(this.Engine, pcode, unit); //******** 新增
                        SalesOrder.CreatePo2nd(this.Engine, wouserid, lineId, pcode, processqty * packrate, "销售订单", this.Request.BizObjectId, maindate, detaildate, coId, cname, cusseqno, remark, "");
                    }
                }

            }
        }
        //销售端简易流程 - 生成销售出库单
        H3.DataModel.BizObjectSchema chukuSchema = this.Engine.BizObjectManager.GetPublishedSchema("D000119wocuku");
        H3.DataModel.BizObjectSchema thisSchema = this.Request.Schema;
        H3.DataModel.BizObject po = new H3.DataModel.BizObject(this.Engine, chukuSchema, this.Request.UserContext.UserId);
        if(flagJY)
        {
            string ynwl = "";//v3.0 210711 ls
            string sql = "select ifnull(F0000052,'否') as ynwl from I_D000119IntialSetup order by createdtime desc limit 1";
            System.Data.DataTable datas = this.Engine.Query.QueryTable(sql, null);
            if(datas != null)
            {
                ynwl = datas.Rows[0]["ynwl"] + string.Empty;
            }
            H3.DataModel.BizObjectSchema wocukuSchema = this.Engine.BizObjectManager.GetPublishedSchema("D000119wocuku");
            H3.DataModel.BizObjectSchema psSchema = this.Engine.BizObjectManager.GetPublishedSchema("D000119PickStock");
            List < H3.DataModel.BizObject > detailList = new List<H3.DataModel.BizObject>();
            List < string > psarr = new List<string>(); //选择库存
            H3.DataModel.BizObject[] thisdetails = (H3.DataModel.BizObject[]) this.Request.BizObject["D000119orderlist"];
            if(thisdetails != null && thisdetails.Length > 0)
            {
                foreach(H3.DataModel.BizObject detail in thisdetails)
                {
                    decimal qty = Convert.ToDecimal(detail["qty"]);
                    if(qty <= 0)
                    {
                        continue;
                    }
                    H3.DataModel.BizObject d = new H3.DataModel.BizObject(this.Engine, this.Engine.BizObjectManager.GetPublishedSchema("D000119sckdetail"), this.Request.UserContext.UserId);
                    d["Deliverydate"] = detail["RE1"];
                    d["orderqty"] = detail["qty"];
                    d["QtyApply"] = detail["qty"];
                    d["**********"] = this.Request.BizObject["SeqNo"];
                    d["CoNo20201118"] = this.Request.BizObject["CoNo20200722"];
                    d["CoType20201118"] = this.Request.BizObject["RT5"];
                    d["Remark20201118"] = this.Request.BizObject["ProdBase"];
                    string productId = Convert.ToString(detail["pcode"]);
                    string procode = "";
                    string pname = "";
                    string pspec = "";
                    string unit = "";
                    string DJ1 = "";
                    string DJ2 = "";
                    string DJ3 = "";
                    string DJ4 = "";
                    string DJ5 = "";
                    string sqlpd = "select procode,proname,prospec,unit,DJ1,DJ2,DJ3,DJ4,DJ5 from i_D000119Product_sale where objectid='" + productId + "'";
                    System.Data.DataTable resultpd = this.Request.Engine.Query.QueryTable(sqlpd, null);
                    if(resultpd != null && resultpd.Rows.Count > 0)
                    {
                        procode = Convert.ToString(resultpd.Rows[0]["procode"]);
                        pname = Convert.ToString(resultpd.Rows[0]["proname"]);
                        pspec = Convert.ToString(resultpd.Rows[0]["prospec"]);
                        unit = Convert.ToString(resultpd.Rows[0]["unit"]);
                        DJ1 = Convert.ToString(resultpd.Rows[0]["DJ1"]);
                        DJ2 = Convert.ToString(resultpd.Rows[0]["DJ2"]);
                        DJ3 = Convert.ToString(resultpd.Rows[0]["DJ3"]);
                        DJ4 = Convert.ToString(resultpd.Rows[0]["DJ4"]);
                        DJ5 = Convert.ToString(resultpd.Rows[0]["DJ5"]);
                    }
                    d["Pcode20201118"] = procode;
                    d["unit"] = detail["unit"];
                    d["Unit2"] = detail["PcodeS"];
                    d["Size20201118"] = detail["Size"];
                    d["pname"] = pname;
                    d["pspec"] = pspec;
                    d["DJ1"] = DJ1;
                    d["DJ2"] = DJ2;
                    d["DJ3"] = DJ3;
                    d["DJ4"] = DJ4;
                    d["DJ5"] = DJ5;
                    if(flag30)  //v3.0 自定义6,7,8,9 其中6,7多行文本，8为图片,9为附件，
                    {
                        d["DJ620210830"] = detail["DJ620210830"];
                        d["DJ720210830"] = detail["DJ720210830"];
                        this.Request.Engine.BizObjectManager.CopyFiles("D000119salesorder", "D000119orderlist", "DJ820210830", detail.ObjectId, "D000119wocuku", "D000119sckdetail", "DJ820210830", d.ObjectId, true, true);
                        this.Request.Engine.BizObjectManager.CopyFiles("D000119salesorder", "D000119orderlist", "DJ920210830", detail.ObjectId, "D000119wocuku", "D000119sckdetail", "DJ920210830", d.ObjectId, true, true);
                    }
                    d["Realnetprice"] = detail["Netprice"]; //不含税20190823
                    d["Tax"] = detail["Tax"];
                    d["netprice"] = detail["sellprice"];
                    d["Othertx"] = detail["requirement"]; //20191212 赋值
                    d["colineId1118"] = detail["ObjectId"]; //20191212 赋值
                    //生成“选择库存表单”
                    H3.DataModel.BizObject psObj = new H3.DataModel.BizObject(this.Engine, psSchema, this.Request.UserContext.UserId);
                    psObj["**********"] = this.Request.BizObject["ObjectId"];
                    psObj["Pcode20201118"] = procode;
                    psObj["unit"] = detail["unit"];
                    psObj["Unit2"] = ynwl; //v3.0 改为库存绑定客户 210711 ls
                    psObj["pname"] = pname;
                    psObj["pspec"] = pspec;
                    psObj["DJ1"] = DJ1;
                    psObj["DJ2"] = DJ2;
                    psObj["DJ3"] = DJ3;
                    psObj["DJ4"] = DJ4;
                    psObj["DJ5"] = DJ5;
                    psObj["Size20201118"] = detail["Size"];
                    psObj["Costatusbefore"] = "简易";
                    psObj["QtyApply"] = detail["qty"];
                    psObj["lineXd1118"] = d["ObjectId"];
                    psObj["colineId1118"] = detail["ObjectId"]; //20191212 赋值
                    psObj["SheetName"] = "选择库存";
                    psObj.Status = H3.DataModel.BizObjectStatus.Draft;
                    psObj.Create();
                    d["PickStock"] = psObj.ObjectId;
                    d["totalqty"] = StockRelated.GetGdStockSum(this.Engine, this.Request.UserContext.UserId, productId, procode);
                    detailList.Add(d);
                    if(!psarr.Contains(d["PickStock"] + string.Empty))
                    {
                        psarr.Add(d["PickStock"] + string.Empty);
                    }
                }
            }
            if(detailList != null && detailList.Count > 0)
            {
                H3.DataModel.BizObject wocuku = new H3.DataModel.BizObject(this.Engine, wocukuSchema, this.Request.UserContext.UserId);
                // wocuku["DeliveryApply"] = this.Request.BizObjectId;
                wocuku["Costatusbefore"] = "简易";
                wocuku["sckstatus"] = "待处理";
                wocuku["RT3"] = ynwl; //v3.0 改为库存绑定客户 210711 ls
                wocuku["ccode"] = this.Request.BizObject["ccode"];
                wocuku["co"] = this.Request.BizObjectId;
                wocuku["Customer"] = this.Request.BizObject["OpenClose"];
                wocuku["cnameshort"] = this.Request.BizObject["cnameshort"];
                wocuku["Employee"] = this.Request.BizObject["salesmanager"];
                wocuku["ApprovedDate"] = DateTime.Now;
                wocuku["RT4"] = this.Request.BizObject["CalculateAPdays"]; //收货地址 ********
                wocuku["Currency"] = this.Request.BizObject["JKStatus"]; //收货人 ********
                wocuku["Codes"] = this.Request.BizObject["how"]; //电话 ********
                wocuku["D000119sckdetail"] = detailList.ToArray();
                string instanceId = System.Guid.NewGuid().ToString();
                if(string.IsNullOrEmpty(wocuku.WorkflowInstanceId))
                {
                    wocuku.WorkflowInstanceId = instanceId;
                }
                H3.ErrorCode createResult = wocuku.Create();
                if(createResult == H3.ErrorCode.Success)
                {
                    //启动流程
                    string workItemID = string.Empty;
                    string errorMsg = string.Empty;
                    H3.Workflow.Template.WorkflowTemplate wfTemp = this.Request.Engine.WorkflowTemplateManager.GetDefaultWorkflow(wocuku.Schema.SchemaCode);
                    this.Request.Engine.Interactor.OriginateInstance(this.Request.UserContext.UserId, wocuku.Schema.SchemaCode,
                        wfTemp.WorkflowVersion, wocuku.ObjectId, wocuku.WorkflowInstanceId, H3.Workflow.WorkItem.AccessMethod.Web,
                        true, string.Empty, true, out workItemID, out errorMsg);
                }
                if(psarr != null && psarr.Count > 0)
                {
                    foreach(string psId in psarr)
                    {
                        H3.DataModel.BizObject psObj = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId, this.Request.Engine, "D000119PickStock", psId, false);
                        if(psObj != null)
                        {
                            psObj["wocuku"] = wocuku.ObjectId;
                            psObj.Update();
                        }
                    }
                }
            }
            this.Request.BizObject["InvoiceNo"] = "简易流程";
        }
        // 如果是预收订单，生成客户回款
        H3.DataModel.BizObjectSchema poSchema1 = this.Engine.BizObjectManager.GetPublishedSchema("D000119AR01");
        H3.DataModel.BizObjectSchema thisSchema1 = this.Request.Schema;
        if(this.Request.BizObject["YNPayAdvance"] + string.Empty == "是" && Convert.ToDecimal(this.Request.BizObject["YSAmount"]) > 0) //是否是预付
        {
            H3.DataModel.BizObject po1 = new H3.DataModel.BizObject(this.Engine, poSchema1, this.Request.UserContext.UserId);
            H3.IEngine engine = this.Engine;
            string userId = this.Request.UserContext.UserId;
            var cnames = this.Request.BizObject["cnameshort"] + string.Empty;
            if(poSchema1.GetProperty("Customer") != null)
            {
                po1["Customer"] = this.Request.BizObject["cnameshort"] + string.Empty;
            }
            if(poSchema1.GetProperty("Fd658be0f186c4f6387747718ebd94259") != null)
            {
                //   po1["PAamount"] = this.Request.BizObject["YSAmount"]; //回款金额  20201116 屏蔽，改为不同的控件 Fd658be0f186c4f6387747718ebd94259
                po1["Fd658be0f186c4f6387747718ebd94259"] = this.Request.BizObject["YSAmount"]; //回款金额
            }
            if(poSchema1.GetProperty("PerCo0724") != null) //新增 20201116
            {
                po1["PerCo0724"] = this.Request.BizObjectId;
                po1["CoNo20200722"] = this.Request.BizObject["CoNo20200722"];
            }
            po1["CustomerCurrency"] = this.Request.BizObject["currency"] + string.Empty;
            po1["Remark"] = "收" + this.Request.BizObject["cnameshort"] + "的预付款";
            po1["CodeplusDate"] = "系统提交";
            po1["RT1"] = "销售预收";
            po1["Ccode"] = this.Request.BizObject["ccode"];
            po1["CO"] = this.Request.BizObjectId;
            // po1["Codes"] = "是";//定向回款订单 20190603
            string instanceId = System.Guid.NewGuid().ToString();
            if(string.IsNullOrEmpty(po1.WorkflowInstanceId))
            {
                po1.WorkflowInstanceId = instanceId;
            }
            H3.ErrorCode createResult = po1.Create();
            if(createResult == H3.ErrorCode.Success)
            {
                //启动流程
                string workItemID = string.Empty;
                string errorMsg = string.Empty;
                H3.Workflow.Template.WorkflowTemplate wfTemp = this.Request.Engine.WorkflowTemplateManager.GetDefaultWorkflow(po1.Schema.SchemaCode);
                this.Request.Engine.Interactor.OriginateInstance(wouserid, po1.Schema.SchemaCode,
                    wfTemp.WorkflowVersion, po1.ObjectId, po1.WorkflowInstanceId, H3.Workflow.WorkItem.AccessMethod.Web,
                    true, string.Empty, true, out workItemID, out  errorMsg);
            }
        }
        // 如果有代客付款，生成应收对账
        if(Convert.ToDecimal(this.Request.BizObject["CKqtyTotal"]) > 0)
        {
            string code = ccode;
            string cnameshort = Convert.ToString(this.Request.BizObject["cnameshort"]);
            DateTime SupposeDate = DateTime.Now;
            string CoNo20200722 = Convert.ToString(this.Request.BizObject["CoNo20200722"]);
            string Pcode2nd = "";
            string CO = this.Request.BizObject["SeqNo"] + string.Empty;
            string wocuku = CO + "代"; //为了删除时，找到该记录
            string currencyS = this.Request.BizObject["currency"] + string.Empty;
            string procode = "";
            string pname = "";
            string pspec = "";
            string unit = "";
            string remark = "代客付款";
            decimal qty = 1;
            decimal vatprice = Convert.ToDecimal(this.Request.BizObject["CKqtyTotal"]);
            decimal tax = 0.13m;
            decimal netprice = vatprice / (1 + tax);
            string fptype = "";
            PriceRelated.ARcreated(this.Engine, this.Request.UserContext.UserId, code, cnameshort, SupposeDate, CoNo20200722, Pcode2nd, CO, wocuku, currencyS, procode, pname, pspec, unit, remark, qty, vatprice, netprice, tax, fptype);
        }
        //KPI 销售订单达成率
        decimal  order = Convert.ToDecimal(this.Request.BizObject["CNY"]); // 合同总金额
        System.DateTime currentTime = new System.DateTime();
        currentTime = System.DateTime.Now;
        //获取当前年，月
        var kpiyear = Convert.ToString(currentTime.Year);
        var kpimonth = Convert.ToDecimal(currentTime.Month);
        H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();
        filter.Matcher = new H3.Data.Filter.ItemMatcher("SingleSubmit", H3.Data.ComparisonOperatorType.Equal, kpiyear);
        H3.DataModel.BizObject[] list = H3.DataModel.BizObject.GetList(this.Engine, this.Request.UserContext.UserId, this.Engine.BizObjectManager.GetPublishedSchema("D000119orderachrate"),
            H3.DataModel.GetListScopeType.GlobalAll, filter);
        if(list != null && list.Length > 0)
        {
            H3.DataModel.BizObject[] kpidetails = (H3.DataModel.BizObject[]) list[0]["D000119orderweek"];
            if(kpidetails != null && kpidetails.Length > 0)
            {
                foreach(H3.DataModel.BizObject detail in kpidetails)
                {
                    if(Convert.ToDecimal(detail["month"]) == kpimonth)
                    {
                        detail["order"] = Convert.ToDecimal(detail["order"]) + order;
                        if(Convert.ToDecimal(detail["Mordertarget"]) > 0)
                        {
                            detail["actual"] = Convert.ToDecimal(detail["order"]) / Convert.ToDecimal(detail["Mordertarget"]);
                        }
                        detail.Update();
                        break;
                    }
                }
            }
        }
        else
        {
            H3.DataModel.BizObjectSchema kpiSchema = this.Engine.BizObjectManager.GetPublishedSchema("D000119orderachrate");
            H3.DataModel.BizObjectSchema kpichildSchema = this.Engine.BizObjectManager.GetPublishedSchema("D000119orderweek");
            H3.DataModel.BizObject kpi = new H3.DataModel.BizObject(this.Engine, kpiSchema, this.Request.UserContext.UserId);
            List < H3.DataModel.BizObject > detailList = new List<H3.DataModel.BizObject>();
            for(int i = 0;i < 12; i++)
            {
                H3.DataModel.BizObject kpichild = new H3.DataModel.BizObject(this.Engine, kpichildSchema, this.Request.UserContext.UserId);
                kpichild["month"] = i + 1;
                if(kpimonth == i + 1)
                {
                    kpichild["order"] = Convert.ToDecimal(kpichild["order"]) + order;
                    if(Convert.ToDecimal(kpichild["Mordertarget"]) > 0)
                    {
                        kpichild["actual"] = Convert.ToDecimal(kpichild["order"]) / Convert.ToDecimal(kpichild["Mordertarget"]);
                    }
                }
                detailList.Add(kpichild);
            }
            kpi["D000119orderweek"] = detailList.ToArray();
            kpi["SingleSubmit"] = kpiyear;
            kpi.Status = H3.DataModel.BizObjectStatus.Effective;
            kpi.Create();
        }
        this.Request.BizObject["ApprovedDate"] = DateTime.Now; //LS added on Jan 25th,2018
        this.Request.BizObject["COchange"] = "订单提交"; //LS added on Jan 26th,2018
        this.Request.BizObject.Update();
    }
    public void CancelCalcate()
    {
        H3.DataModel.BizObject thisObj = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId, this.Engine, this.Request.Schema.SchemaCode, this.Request.BizObjectId, false);
        // SalesOrder.ReExec(this.Engine, thisObj);
        ReExec(this.Engine, this.Request.UserContext.UserId, thisObj);
    }
    public static void ReExec(H3.IEngine engine, string userid, H3.DataModel.BizObject thisObj) {
        //删除未审批的生产计划
        H3.DataModel.BizObjectSchema schema = engine.BizObjectManager.GetPublishedSchema("D000119sckdetail"); //20201118 代码升级为一张销售出库单可以包含多个CO，即CO入子表
        if(schema.GetProperty("**********") == null)
        {
            H3.Data.Filter.Filter jhFilter = new H3.Data.Filter.Filter();
            H3.Data.Filter.And jhAnd = new H3.Data.Filter.And();
            jhAnd.Add(new H3.Data.Filter.ItemMatcher("co", H3.Data.ComparisonOperatorType.Equal, thisObj.ObjectId));
            jhAnd.Add(new H3.Data.Filter.ItemMatcher("Status", H3.Data.ComparisonOperatorType.Equal, H3.DataModel.BizObjectStatus.Running));
            jhFilter.Matcher = jhAnd;
            H3.DataModel.BizObject[] jhs = H3.DataModel.BizObject.GetList(engine, H3.Organization.User.SystemUserId, engine.BizObjectManager.GetPublishedSchema("D000119productionplan"),
                H3.DataModel.GetListScopeType.GlobalAll, jhFilter);
            if(jhs != null && jhs.Length > 0)
            {
                foreach(H3.DataModel.BizObject jh in jhs)
                {
                    //jh.Remove();
                    mBizObject.RemoveBizObject(engine, H3.Organization.User.SystemUserId, jh);
                }
            }
            //KPI 销售订单达成率
            decimal  order = Convert.ToDecimal(thisObj["CNY"]); // 合同总金额
            System.DateTime currentTime = new System.DateTime();
            currentTime = System.DateTime.Now;
            //获取当前年，月
            var kpiyear = Convert.ToString(currentTime.Year);
            var kpimonth = Convert.ToDecimal(currentTime.Month);
            H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();
            filter.Matcher = new H3.Data.Filter.ItemMatcher("SingleSubmit", H3.Data.ComparisonOperatorType.Equal, kpiyear);
            H3.DataModel.BizObject[] list = H3.DataModel.BizObject.GetList(engine, userid, engine.BizObjectManager.GetPublishedSchema("D000119orderachrate"),
                H3.DataModel.GetListScopeType.GlobalAll, filter);
            if(list != null && list.Length > 0)
            {
                H3.DataModel.BizObject[] kpidetails = (H3.DataModel.BizObject[]) list[0]["D000119orderweek"];
                if(kpidetails != null && kpidetails.Length > 0)
                {
                    foreach(H3.DataModel.BizObject detail in kpidetails)
                    {
                        if(Convert.ToDecimal(detail["month"]) == kpimonth)
                        {
                            detail["order"] = Convert.ToDecimal(detail["order"]) - order;
                            if(Convert.ToDecimal(detail["Mordertarget"]) > 0)
                            {
                                detail["actual"] = Convert.ToDecimal(detail["order"]) / Convert.ToDecimal(detail["Mordertarget"]);
                            }
                            detail.Update();
                            break;
                        }
                    }
                }
            }
        }
        else
        {
            H3.Data.Filter.Filter jhFilter = new H3.Data.Filter.Filter();
            H3.Data.Filter.And jhAnd = new H3.Data.Filter.And();
            jhAnd.Add(new H3.Data.Filter.ItemMatcher("co", H3.Data.ComparisonOperatorType.Equal, thisObj.ObjectId));
            jhAnd.Add(new H3.Data.Filter.ItemMatcher("Status", H3.Data.ComparisonOperatorType.Equal, H3.DataModel.BizObjectStatus.Running));
            jhFilter.Matcher = jhAnd;
            H3.DataModel.BizObject[] jhs = H3.DataModel.BizObject.GetList(engine, H3.Organization.User.SystemUserId, engine.BizObjectManager.GetPublishedSchema("D000119productionplan"),
                H3.DataModel.GetListScopeType.GlobalAll, jhFilter);
            if(jhs != null && jhs.Length > 0)
            {
                foreach(H3.DataModel.BizObject jh in jhs)
                {
                    //jh.Remove();
                    mBizObject.RemoveBizObject(engine, H3.Organization.User.SystemUserId, jh);
                }
            }
            string code = "";
            string co = thisObj["SeqNo"] + string.Empty;
            H3.DataModel.BizObject customer = H3.DataModel.BizObject.Load(userid, engine, "D000119createcustomer", thisObj["ccode"] + string.Empty, false);
            if(customer != null)
            {
                code = customer["ccode"] + string.Empty;
            }
            PriceRelated.AccountingRemove(engine, userid, "AR", code, co + "代");
            //KPI 销售订单达成率
            decimal  order = Convert.ToDecimal(thisObj["CNY"]); // 合同总金额
            System.DateTime currentTime = new System.DateTime();
            currentTime = System.DateTime.Now;
            //获取当前年，月
            var kpiyear = Convert.ToString(currentTime.Year);
            var kpimonth = Convert.ToDecimal(currentTime.Month);
            H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();
            filter.Matcher = new H3.Data.Filter.ItemMatcher("SingleSubmit", H3.Data.ComparisonOperatorType.Equal, kpiyear);
            H3.DataModel.BizObject[] list = H3.DataModel.BizObject.GetList(engine, userid, engine.BizObjectManager.GetPublishedSchema("D000119orderachrate"),
                H3.DataModel.GetListScopeType.GlobalAll, filter);
            if(list != null && list.Length > 0)
            {
                H3.DataModel.BizObject[] kpidetails = (H3.DataModel.BizObject[]) list[0]["D000119orderweek"];
                if(kpidetails != null && kpidetails.Length > 0)
                {
                    foreach(H3.DataModel.BizObject detail in kpidetails)
                    {
                        if(Convert.ToDecimal(detail["month"]) == kpimonth)
                        {
                            detail["order"] = Convert.ToDecimal(detail["order"]) - order;
                            if(Convert.ToDecimal(detail["Mordertarget"]) > 0)
                            {
                                detail["actual"] = Convert.ToDecimal(detail["order"]) / Convert.ToDecimal(detail["Mordertarget"]);
                            }
                            detail.Update();
                            break;
                        }
                    }
                }
            }
        }
    }
    public static void AntiSubmitRemoving(H3.IEngine engine, string userid, H3.DataModel.BizObject thisObj)
    {
        //反审核，删除单据
        {
            //采购订单
            {
                H3.DataModel.BizObjectSchema schemaXX = engine.BizObjectManager.GetPublishedSchema("D000119PO");
                H3.Data.Filter.Filter filterXX = new H3.Data.Filter.Filter();
                H3.Data.Filter.And andMatcherXX = new H3.Data.Filter.And();
                andMatcherXX.Add(new H3.Data.Filter.ItemMatcher("CO", H3.Data.ComparisonOperatorType.Equal, thisObj.ObjectId));
                andMatcherXX.Add(new H3.Data.Filter.ItemMatcher("Status", H3.Data.ComparisonOperatorType.NotEqual, H3.DataModel.BizObjectStatus.Effective));
                filterXX.Matcher = andMatcherXX;
                H3.DataModel.BizObject[] records = H3.DataModel.BizObject.GetList(engine, userid,
                    schemaXX, H3.DataModel.GetListScopeType.GlobalAll, filterXX);
                if(records != null && records.Length > 0)
                {
                    foreach(H3.DataModel.BizObject record in records)
                    {
                        mBizObject.RemoveBizObject(engine, userid, record, true, "反审核");
                    }
                }
            }
            //生产计划
            {
                H3.DataModel.BizObjectSchema schemaXX = engine.BizObjectManager.GetPublishedSchema("D000119productionplan");
                H3.Data.Filter.Filter filterXX = new H3.Data.Filter.Filter();
                H3.Data.Filter.And andMatcherXX = new H3.Data.Filter.And();
                andMatcherXX.Add(new H3.Data.Filter.ItemMatcher("co", H3.Data.ComparisonOperatorType.Equal, thisObj.ObjectId));
                andMatcherXX.Add(new H3.Data.Filter.ItemMatcher("Status", H3.Data.ComparisonOperatorType.NotEqual, H3.DataModel.BizObjectStatus.Effective));
                filterXX.Matcher = andMatcherXX;
                H3.DataModel.BizObject[] records = H3.DataModel.BizObject.GetList(engine, userid,
                    schemaXX, H3.DataModel.GetListScopeType.GlobalAll, filterXX);
                if(records != null && records.Length > 0)
                {
                    foreach(H3.DataModel.BizObject record in records)
                    {
                        mBizObject.RemoveBizObject(engine, userid, record, true, "反审核");
                    }
                }
            }
            //销售出库
            {
                H3.DataModel.BizObjectSchema schemaXX = engine.BizObjectManager.GetPublishedSchema("D000119wocuku");
                H3.Data.Filter.Filter filterXX = new H3.Data.Filter.Filter();
                H3.Data.Filter.And andMatcherXX = new H3.Data.Filter.And();
                andMatcherXX.Add(new H3.Data.Filter.ItemMatcher("co", H3.Data.ComparisonOperatorType.Equal, thisObj.ObjectId));
                andMatcherXX.Add(new H3.Data.Filter.ItemMatcher("Status", H3.Data.ComparisonOperatorType.NotEqual, H3.DataModel.BizObjectStatus.Effective));
                filterXX.Matcher = andMatcherXX;
                H3.DataModel.BizObject[] records = H3.DataModel.BizObject.GetList(engine, userid,
                    schemaXX, H3.DataModel.GetListScopeType.GlobalAll, filterXX);
                if(records != null && records.Length > 0)
                {
                    foreach(H3.DataModel.BizObject record in records)
                    {
                        mBizObject.RemoveBizObject(engine, userid, record, true, "反审核");
                    }
                }
            }
            //销售回款
            {
                H3.DataModel.BizObjectSchema schemaXX = engine.BizObjectManager.GetPublishedSchema("D000119AR01");
                H3.Data.Filter.Filter filterXX = new H3.Data.Filter.Filter();
                H3.Data.Filter.And andMatcherXX = new H3.Data.Filter.And();
                andMatcherXX.Add(new H3.Data.Filter.ItemMatcher("co", H3.Data.ComparisonOperatorType.Equal, thisObj.ObjectId));
                andMatcherXX.Add(new H3.Data.Filter.ItemMatcher("Status", H3.Data.ComparisonOperatorType.NotEqual, H3.DataModel.BizObjectStatus.Effective));
                filterXX.Matcher = andMatcherXX;
                H3.DataModel.BizObject[] records = H3.DataModel.BizObject.GetList(engine, userid,
                    schemaXX, H3.DataModel.GetListScopeType.GlobalAll, filterXX);
                if(records != null && records.Length > 0)
                {
                    foreach(H3.DataModel.BizObject record in records)
                    {
                        mBizObject.RemoveBizObject(engine, userid, record, true, "反审核");
                    }
                }
            }
            //组装拆卸
            {
                H3.DataModel.BizObjectSchema schemaXX = engine.BizObjectManager.GetPublishedSchema("D000119AssembleSheet");
                H3.Data.Filter.Filter filterXX = new H3.Data.Filter.Filter();
                H3.Data.Filter.And andMatcherXX = new H3.Data.Filter.And();
                andMatcherXX.Add(new H3.Data.Filter.ItemMatcher("F0000120", H3.Data.ComparisonOperatorType.Equal, thisObj["SeqNo"] + string.Empty));
                andMatcherXX.Add(new H3.Data.Filter.ItemMatcher("Status", H3.Data.ComparisonOperatorType.NotEqual, H3.DataModel.BizObjectStatus.Effective));
                filterXX.Matcher = andMatcherXX;
                H3.DataModel.BizObject[] records = H3.DataModel.BizObject.GetList(engine, userid,
                    schemaXX, H3.DataModel.GetListScopeType.GlobalAll, filterXX);
                if(records != null && records.Length > 0)
                {
                    foreach(H3.DataModel.BizObject record in records)
                    {
                        mBizObject.RemoveBizObject(engine, userid, record, true, "反审核");
                    }
                }
            }

        }
    }

    //阶梯报价
    public static Dictionary < string, decimal > GetPriceByBreakdown(H3.IEngine engine, string objId, decimal num)
    {
        Dictionary < string, decimal > dic = new Dictionary<string, decimal>();
        H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();
        H3.Data.Filter.And and = new H3.Data.Filter.And();
        and.Add(new H3.Data.Filter.ItemMatcher("ParentObjectId", H3.Data.ComparisonOperatorType.Equal, objId));
        and.Add(new H3.Data.Filter.ItemMatcher("Qtyfrom", H3.Data.ComparisonOperatorType.NotAbove, num));
        and.Add(new H3.Data.Filter.ItemMatcher("Qtyto", H3.Data.ComparisonOperatorType.NotBelow, num));
        filter.Matcher = and;
        H3.DataModel.BizObjectSchema schema = engine.BizObjectManager.GetPublishedSchema("D000119BreakdownSelling");
        if(schema != null)
        {
            H3.DataModel.BizObject[] details = H3.DataModel.BizObject.GetList(engine, H3.Organization.User.SystemUserId, schema,
                H3.DataModel.GetListScopeType.GlobalAll, filter);

            if(details != null && details.Length > 0)
            {
                dic.Add("VATprice", Convert.ToDecimal(details[0]["VATprice"]));
                dic.Add("Tax", Convert.ToDecimal(details[0]["Tax"]));
                dic.Add("NetPrice", Convert.ToDecimal(details[0]["NetPrice"]));
            }
        }
        return dic;
    }
}
public class SalesOrder
{
    public void CreatePlan(H3.IEngine engine, string userId, H3.DataModel.BizObject thisObj, bool flag) {
        H3.DataModel.BizObjectSchema productionplanSchema = engine.BizObjectManager.GetPublishedSchema("D000119productionplan");
        H3.DataModel.BizObject productionplan = new H3.DataModel.BizObject(engine, productionplanSchema, userId);
        productionplan["YesnoStock"] = "客户订单";
        productionplan["Cname"] = thisObj["cnameshort"];
        productionplan["deliverydate"] = thisObj["deliverydaterequired"]; //承诺交期
        productionplan["RE2"] = thisObj["deliverydaterequired"]; //要求交期
        productionplan["ppstatus"] = "订单进行：待计划";
        productionplan["Planner"] = thisObj["Planner"]; // LS added on Jan 22nd, 2018
        productionplan["CoLink"] = thisObj["SeqNo"];
        try
        {
            productionplan["CoNo20200722"] = thisObj["CoNo20200722"];
        }
        catch(Exception e)
        {

        }
        productionplan["co"] = thisObj.ObjectId;
        productionplan["ccode"] = thisObj["ccode"];
        productionplan["RD4"] = thisObj["OrderQty"]; //销售订单数量
        productionplan["RT1"] = thisObj["RT1"]; // LS added on Apr 9th, 2018
        productionplan["RT2"] = thisObj["RT2"]; // LS added on Apr 9th, 2018
        productionplan["RT3"] = thisObj["RT3"]; // LS added on Apr 9th, 2018
        productionplan["RD1"] = thisObj["RD1"]; // LS added on Apr 9th, 2018
        productionplan["RD2"] = thisObj["RD2"]; // LS added on Apr 9th, 2018
        productionplan["RD3"] = thisObj["RD3"]; // LS added on Apr 9th, 2018
        productionplan["RE1"] = thisObj["RE1"]; // LS added on Apr 9th, 2018
        productionplan["RP1"] = thisObj["RP1"]; // LS added on Apr 9th, 2018
        productionplan["ProdBase"] = thisObj["ProdBase"]; //简易说明 20200309
        List < H3.DataModel.BizObject > detailList = new List<H3.DataModel.BizObject>();
        Dictionary < string, string > objectIds = new Dictionary<string, string>();
        H3.DataModel.BizObject[] details = (H3.DataModel.BizObject[]) thisObj["D000119orderlist"];
        if(details != null && details.Length > 0)
        {
            foreach(H3.DataModel.BizObject detail in details)
            {
                if(flag) //下推生产计划，直接生成
                {
                    if(detail["makebuy"] + string.Empty != "采购" && Convert.ToDecimal(detail["processqty"]) > 0)
                    {
                        H3.DataModel.BizObject productSale = H3.DataModel.BizObject.Load(userId, engine, "D000119Product_sale", detail["pcode"] + string.Empty, false);

                        H3.DataModel.BizObject plandetail = new H3.DataModel.BizObject(engine, engine.BizObjectManager.GetPublishedSchema("D000119plandetail"), userId);
                        plandetail["pcode"] = detail["pcode"];
                        plandetail["Pcodes"] = detail["PcodeS"]; //辅助编码
                        plandetail["pname"] = productSale["Proname"];
                        plandetail["requirement"] = detail["requirement"];
                        plandetail["PstatusBefore"] = thisObj["SeqNo"] + ":" + detail["requirement"];
                        plandetail["LinkStatus"] = thisObj["SeqNo"]; //销售订单号码写到子表，为了订单合并时，记录都是那个销售订单的
                        plandetail["InspectionStandard"] = detail["InspectionStandard"]; // LS added on Jan 22nd, 2018
                        plandetail["pspec"] = productSale["Prospec"];
                        plandetail["unit"] = productSale["Unit"];
                        plandetail["DJ1"] = productSale["DJ1"]; // LS added on Jan 22nd, 2018
                        plandetail["DJ2"] = productSale["DJ2"]; // LS added on Jan 22nd, 2018
                        plandetail["DJ3"] = productSale["DJ3"]; // LS added on Jan 22nd, 2018
                        plandetail["DJ4"] = productSale["DJ4"]; // LS added on Jan 22nd, 2018
                        plandetail["DJ5"] = productSale["DJ5"]; // LS added on Jan 22nd, 2018
                        plandetail["PgroupS"] = productSale["PgroupS"]; // LS added on Apr 5th, 2018
                        plandetail["Pgroup"] = productSale["Pgroup"]; // LS added on Apr 5th, 2018
                        plandetail["PgroupM"] = productSale["PgroupM"]; // LS added on Apr 5th, 2018
                        plandetail["PgroupR"] = productSale["PgroupR"]; // LS added on Apr 5th, 2018
                        plandetail["makebuy"] = productSale["huoqu"];
                        plandetail["orderqty"] = detail["qty"];
                        decimal packrate = StockRelated.packrate(engine, Convert.ToString(detail["pcode"]), Convert.ToString(detail["unit"])); //******** 新增
                        if(detail["makebuy"] + string.Empty != "采购")
                        {
                            plandetail["recomqty"] = GetBumByVaribleLossRate(engine, detail["pcode"] + string.Empty, Convert.ToDecimal(detail["processqty"]) * packrate);
                            plandetail["RD3"] = GetBumByVaribleLossRate(engine, detail["pcode"] + string.Empty, Convert.ToDecimal(detail["processqty"]) * packrate); //计划数量 γ1.6
                        }
                        else
                        {
                            plandetail["recomqty"] = Convert.ToDecimal(detail["processqty"]) * packrate;
                            plandetail["RD3"] = Convert.ToDecimal(detail["processqty"]) * packrate; //计划数量 γ1.6
                        }
                        plandetail["RE1"] = detail["RE1"]; //交货日期
                        if(detail["RE1"] + string.Empty != "")
                        {
                            int Days = 0;
                            H3.DataModel.BizObject[] SPsales = H3.DataModel.BizObject.GetList(engine, userId,
                                engine.BizObjectManager.GetPublishedSchema("D000119IntialSetup"), H3.DataModel.GetListScopeType.GlobalAll,
                                new H3.Data.Filter.Filter());
                            if(SPsales != null && SPsales.Length > 0)
                            {
                                Days = -Convert.ToInt32(SPsales[0]["F0000126"]);
                            }
                            DateTime time = Convert.ToDateTime(detail["RE1"]);
                            time = time.AddDays(Days);
                            plandetail["startdate"] = time;//开工日期，系统默认提前7天
                        }

                        // plandetail["startdate"] = productionplan["deliverydate"] != null && productionplan["deliverydate"] + string.Empty != "" ? Convert.ToDateTime(productionplan["deliverydate"]).AddDays(-7) + string.Empty : "";

                        // H3.Data.Filter.Filter bomFilter = new H3.Data.Filter.Filter();
                        // H3.Data.Filter.And and = new H3.Data.Filter.And();
                        // and.Add(new H3.Data.Filter.ItemMatcher("procode", H3.Data.ComparisonOperatorType.Equal, detail["pcode"] + string.Empty));
                        // and.Add(new H3.Data.Filter.ItemMatcher("MbomStatus", H3.Data.ComparisonOperatorType.Equal, "正常使用"));
                        // bomFilter.Matcher = and;
                        // H3.DataModel.BizObject[] boms = H3.DataModel.BizObject.GetList(engine, userId, engine.BizObjectManager.GetPublishedSchema("D000119createbom"),
                        //     H3.DataModel.GetListScopeType.GlobalAll, bomFilter);
                        // if(boms != null && boms.Length == 1)
                        // {
                        //     // plandetail["makeBOM"] = boms[0].ObjectId; //获取制造bom
                        //     //  plandetail["RT1"] = boms[0]["special"]; //获取制造bom选配说明

                        //     //     //获取最小成套数   因一个客户有报错，“值对于 Int32 太大或太小” 估计是分母是0,20180606 雷颖钉钉记录，
                        //     //     int min = 0;

                        //     //     H3.DataModel.BizObjectSchema secondLevelMBOMSchema = engine.BizObjectManager.GetPublishedSchema("D000119SecondLevelMBOM");
                        //     //     H3.DataModel.BizObjectSchema product_saleSchema = engine.BizObjectManager.GetPublishedSchema("D000119Product_sale");
                        //     //     H3.Data.Filter.Filter bomfilter = new H3.Data.Filter.Filter();
                        //     //     bomfilter.Matcher = new H3.Data.Filter.ItemMatcher("MBOMcode", H3.Data.ComparisonOperatorType.Equal, boms[0].ObjectId);

                        //     //     H3.DataModel.BizObject[] secondBoms = H3.DataModel.BizObject.GetList(engine, userId, secondLevelMBOMSchema, H3.DataModel.GetListScopeType.GlobalAll, bomfilter);
                        //     //     if(secondBoms != null && secondBoms.Length > 0)
                        //     //     {
                        //     //         List < string > productIds = new List<string>();
                        //     //         Dictionary < string, H3.DataModel.BizObject > productDic = new Dictionary<string, H3.DataModel.BizObject>();
                        //     //         foreach(H3.DataModel.BizObject bom in secondBoms)
                        //     //         {
                        //     //             productIds.Add(bom["Pcode"] + string.Empty);
                        //     //         }


                        //     //         productDic = GetStocks(engine, productIds, string.Empty);

                        //     //         decimal firstStock = productDic.ContainsKey(secondBoms[0]["Pcode"] + string.Empty) ? Convert.ToDecimal(productDic[secondBoms[0]["Pcode"] + string.Empty]["youxiaostock"]) : 0;
                        //     //         min = Convert.ToInt32(Math.Floor(firstStock / Convert.ToDecimal(secondBoms[0]["BOMqty"])));
                        //     //         foreach(H3.DataModel.BizObject bom in secondBoms)
                        //     //         {
                        //     //             decimal stock = productDic.ContainsKey(bom["Pcode"] + string.Empty) ? Convert.ToDecimal(productDic[bom["Pcode"] + string.Empty]["youxiaostock"]) : 0;
                        //     //             if(!productDic.ContainsKey(bom["Pcode"] + string.Empty) || Convert.ToDecimal(productDic[bom["Pcode"] + string.Empty]["youxiaostock"]) <= 0)
                        //     //             {
                        //     //                 break;
                        //     //             }
                        //     //             else
                        //     //             {
                        //     //                 if(Convert.ToDecimal(productDic[bom["Pcode"] + string.Empty]["youxiaostock"]) / Convert.ToDecimal(bom["BOMqty"]) < min)
                        //     //                 {
                        //     //                     min = Convert.ToInt32(Math.Floor(Convert.ToDecimal(productDic[bom["Pcode"] + string.Empty]["youxiaostock"]) / Convert.ToDecimal(bom["BOMqty"])));
                        //     //                 }
                        //     //             }
                        //     //         }
                        //     //     }
                        //     //     if(min < 0)
                        //     //     {
                        //     //         min = 0;
                        //     //     }
                        //     //     plandetail["RD1"] = min;

                        // }

                        detailList.Add(plandetail);
                        H3.DataModel.BizObjectFileHeader[] headers = engine.BizObjectManager.CopyFiles("D000119salesorder", "D000119orderlist", "Attachment", detail.ObjectId, "D000119productionplan", "D000119plandetail", "Attachment", plandetail.ObjectId, false, false);
                    }
                }
                else //下推系统选择，有bom才生成生产计划
                {
                    H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();
                    filter.Matcher = new H3.Data.Filter.ItemMatcher("Pcode", H3.Data.ComparisonOperatorType.Equal, detail["pcode"] + string.Empty);
                    H3.DataModel.BizObject[] list = H3.DataModel.BizObject.GetList(engine, userId, engine.BizObjectManager.GetPublishedSchema("D000119MbomCreated"),
                        H3.DataModel.GetListScopeType.GlobalAll, filter);
                    if(list != null && list.Length > 0)
                    {
                        if(detail["makebuy"] + string.Empty != "采购" && Convert.ToDecimal(detail["processqty"]) > 0)
                        {
                            H3.DataModel.BizObject productSale = H3.DataModel.BizObject.Load(userId, engine, "D000119Product_sale", detail["pcode"] + string.Empty, false);
                            H3.DataModel.BizObject plandetail = new H3.DataModel.BizObject(engine, engine.BizObjectManager.GetPublishedSchema("D000119plandetail"), userId);
                            plandetail["pcode"] = detail["pcode"];
                            plandetail["Pcodes"] = detail["PcodeS"]; //辅助编码
                            plandetail["pname"] = productSale["Proname"];
                            plandetail["requirement"] = detail["requirement"];
                            plandetail["PstatusBefore"] = thisObj["SeqNo"] + ":" + detail["requirement"];
                            plandetail["LinkStatus"] = thisObj["SeqNo"]; //销售订单号码写到子表，为了订单合并时，记录都是那个销售订单的
                            plandetail["InspectionStandard"] = detail["InspectionStandard"]; // LS added on Jan 22nd, 2018
                            plandetail["pspec"] = productSale["Prospec"];
                            plandetail["unit"] = productSale["Unit"];
                            plandetail["DJ1"] = productSale["DJ1"]; // LS added on Jan 22nd, 2018
                            plandetail["DJ2"] = productSale["DJ2"]; // LS added on Jan 22nd, 2018
                            plandetail["DJ3"] = productSale["DJ3"]; // LS added on Jan 22nd, 2018
                            plandetail["DJ4"] = productSale["DJ4"]; // LS added on Jan 22nd, 2018
                            plandetail["DJ5"] = productSale["DJ5"]; // LS added on Jan 22nd, 2018
                            plandetail["PgroupS"] = productSale["PgroupS"]; // LS added on Apr 5th, 2018
                            plandetail["Pgroup"] = productSale["Pgroup"]; // LS added on Apr 5th, 2018
                            plandetail["PgroupM"] = productSale["PgroupM"]; // LS added on Apr 5th, 2018
                            plandetail["PgroupR"] = productSale["PgroupR"]; // LS added on Apr 5th, 2018
                            plandetail["makebuy"] = productSale["huoqu"];
                            plandetail["orderqty"] = detail["qty"];
                            decimal packrate = StockRelated.packrate(engine, Convert.ToString(detail["pcode"]), Convert.ToString(detail["unit"])); //******** 新增
                            if(detail["makebuy"] + string.Empty != "采购")
                            {
                                plandetail["recomqty"] = GetBumByVaribleLossRate(engine, detail["pcode"] + string.Empty, Convert.ToDecimal(detail["processqty"]) * packrate);
                                plandetail["RD3"] = GetBumByVaribleLossRate(engine, detail["pcode"] + string.Empty, Convert.ToDecimal(detail["processqty"]) * packrate); //计划数量 γ1.6
                            }
                            else
                            {
                                plandetail["recomqty"] = Convert.ToDecimal(detail["processqty"]) * packrate;
                                plandetail["RD3"] = Convert.ToDecimal(detail["processqty"]) * packrate; //计划数量 γ1.6
                            }
                            plandetail["RE1"] = detail["RE1"]; //交货日期
                            if(detail["RE1"] + string.Empty != "")
                            {
                                int Days = 0;
                                H3.DataModel.BizObject[] SPsales = H3.DataModel.BizObject.GetList(engine, userId,
                                    engine.BizObjectManager.GetPublishedSchema("D000119IntialSetup"), H3.DataModel.GetListScopeType.GlobalAll,
                                    new H3.Data.Filter.Filter());
                                if(SPsales != null && SPsales.Length > 0)
                                {
                                    Days = -Convert.ToInt32(SPsales[0]["F0000126"]);
                                }
                                DateTime time = Convert.ToDateTime(detail["RE1"]);
                                time = time.AddDays(Days);
                                plandetail["startdate"] = time;//开工日期，系统默认提前7天
                            }
                            detailList.Add(plandetail);
                            H3.DataModel.BizObjectFileHeader[] headers = engine.BizObjectManager.CopyFiles("D000119salesorder", "D000119orderlist", "Attachment", detail.ObjectId, "D000119productionplan", "D000119plandetail", "Attachment", plandetail.ObjectId, false, false);
                        }
                    }
                }
            }
        }
        if(detailList != null && detailList.Count > 0)
        {
            productionplan["D000119plandetail"] = detailList.ToArray();
            string instanceId = System.Guid.NewGuid().ToString();
            if(string.IsNullOrEmpty(productionplan.WorkflowInstanceId))
            {
                productionplan.WorkflowInstanceId = instanceId;
            }
            H3.ErrorCode createResult = productionplan.Create();
            if(createResult == H3.ErrorCode.Success)
            {
                //启动流程
                string workItemID = string.Empty;
                string errorMsg = string.Empty;
                H3.Workflow.Template.WorkflowTemplate wfTemp = engine.WorkflowTemplateManager.GetDefaultWorkflow(productionplan.Schema.SchemaCode);
                engine.Interactor.OriginateInstance(userId, productionplan.Schema.SchemaCode,
                    wfTemp.WorkflowVersion, productionplan.ObjectId, productionplan.WorkflowInstanceId, H3.Workflow.WorkItem.AccessMethod.Web,
                    true, string.Empty, true, out workItemID, out  errorMsg);
            }

        }
    }
    public void CreateAssemble(H3.IEngine engine, string userId, H3.DataModel.BizObject thisObj, bool flag) {
        H3.DataModel.BizObjectSchema  productionplanSchema = engine.BizObjectManager.GetPublishedSchema("D000119AssembleSheet");
        H3.DataModel.BizObject productionplan = new H3.DataModel.BizObject(engine, productionplanSchema, userId);
        productionplan["Employee"] = thisObj["Planner"]; //执行人
        productionplan["ProdType"] = "组装";
        productionplan["Remark"] = thisObj["ProdBase"]; //订单说明
        try //20191001
        {
            productionplan["deliverydate"] = thisObj["deliverydaterequired"];
        }
        catch(Exception e)
        { }
        productionplan["Reasons"] = thisObj["cnameshort"];
        productionplan["F0000120"] = thisObj["SeqNo"];
        try
        {
            productionplan["CoNo20200722"] = thisObj["CoNo20200722"];
        }
        catch(Exception e)
        {

        }
        List < H3.DataModel.BizObject > detailList = new List<H3.DataModel.BizObject>();
        Dictionary < string, string > objectIds = new Dictionary<string, string>();
        H3.DataModel.BizObject[] details = (H3.DataModel.BizObject[]) thisObj["D000119orderlist"];
        if(details != null && details.Length > 0)
        {
            foreach(H3.DataModel.BizObject detail in details)
            {
                decimal packrate = StockRelated.packrate(engine, Convert.ToString(detail["pcode"]), Convert.ToString(detail["unit"])); //******** 新增
                if(flag) //下推组装拆卸，直接生成
                {
                    if(detail["makebuy"] + string.Empty != "采购" && Convert.ToDecimal(detail["processqty"]) > 0)
                    {
                        string pcode = detail["pcode"] + string.Empty;
                        string unit = detail["unit"] + string.Empty;

                        H3.DataModel.BizObject tempObj = detailList.Find(x => x["Pcode"] + string.Empty == pcode);
                        if(tempObj != null)
                        {
                            tempObj["AssembleQty"] = Convert.ToDecimal(tempObj["AssembleQty"]) + Convert.ToDecimal(detail["qty"]) * packrate;
                        }
                        else
                        {
                            H3.DataModel.BizObject productSale = H3.DataModel.BizObject.Load(userId, engine, "D000119Product_sale", detail["pcode"] + string.Empty, false);
                            H3.DataModel.BizObject plandetail = new H3.DataModel.BizObject(engine, engine.BizObjectManager.GetPublishedSchema("D000119FGDetail"), userId);
                            try //20191001
                            {
                                plandetail["deliverydate"] = detail["RE1"];
                            }
                            catch(Exception e)
                            { }
                            plandetail["DetailRemark"] = detail["requirement"];
                            plandetail["Pcode"] = detail["pcode"];
                            plandetail["F0000132"] = detail["PcodeS"]; //辅助编码
                            plandetail["Pname"] = productSale["Proname"];
                            plandetail["Pspec"] = productSale["Prospec"];
                            plandetail["Unit"] = productSale["Unit"];
                            plandetail["DJ1"] = productSale["DJ1"]; // LS added on Jan 22nd, 2018
                            plandetail["DJ2"] = productSale["DJ2"]; // LS added on Jan 22nd, 2018
                            plandetail["DJ3"] = productSale["DJ3"]; // LS added on Jan 22nd, 2018
                            plandetail["DJ4"] = productSale["DJ4"]; // LS added on Jan 22nd, 2018
                            plandetail["DJ5"] = productSale["DJ5"]; // LS added on Jan 22nd, 2018
                            plandetail["AssembleQty"] = Convert.ToDecimal(detail["qty"]) * packrate;
                            detailList.Add(plandetail);
                            try //20191001
                            {
                                H3.DataModel.BizObjectFileHeader[] headers = engine.BizObjectManager.CopyFiles("D000119salesorder", "D000119orderlist", "Attachment", detail.ObjectId, "D000119AssembleSheet", "D000119FGDetail", "Attachment", plandetail.ObjectId, false, false);
                            }
                            catch(Exception e)
                            { }
                        }
                    }
                }
                else //下推系统选择，没有bom才生成组装拆卸，有bom就不生成
                {
                    H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();
                    filter.Matcher = new H3.Data.Filter.ItemMatcher("Pcode", H3.Data.ComparisonOperatorType.Equal, detail["pcode"] + string.Empty);
                    H3.DataModel.BizObject[] list = H3.DataModel.BizObject.GetList(engine, userId, engine.BizObjectManager.GetPublishedSchema("D000119MbomCreated"),
                        H3.DataModel.GetListScopeType.GlobalAll, filter);
                    if(list != null && list.Length > 0)
                    {
                    }
                    else
                    {
                        if(detail["makebuy"] + string.Empty != "采购" && Convert.ToDecimal(detail["processqty"]) > 0)
                        {
                            string pcode = detail["pcode"] + string.Empty;
                            H3.DataModel.BizObject tempObj = detailList.Find(x => x["Pcode"] + string.Empty == pcode);
                            if(tempObj != null)
                            {
                                tempObj["AssembleQty"] = Convert.ToDecimal(tempObj["AssembleQty"]) + Convert.ToDecimal(detail["qty"]) * packrate;
                            }
                            else
                            {
                                H3.DataModel.BizObject productSale = H3.DataModel.BizObject.Load(userId, engine, "D000119Product_sale", detail["pcode"] + string.Empty, false);
                                H3.DataModel.BizObject plandetail = new H3.DataModel.BizObject(engine, engine.BizObjectManager.GetPublishedSchema("D000119FGDetail"), userId);
                                try //20191001
                                {
                                    plandetail["deliverydate"] = detail["RE1"];
                                }
                                catch(Exception e)
                                { }
                                plandetail["DetailRemark"] = detail["requirement"];
                                plandetail["Pcode"] = detail["pcode"];
                                plandetail["F0000132"] = detail["PcodeS"]; //辅助编码
                                plandetail["Pname"] = productSale["Proname"];
                                plandetail["Pspec"] = productSale["Prospec"];
                                plandetail["Unit"] = productSale["Unit"];
                                plandetail["DJ1"] = productSale["DJ1"]; // LS added on Jan 22nd, 2018
                                plandetail["DJ2"] = productSale["DJ2"]; // LS added on Jan 22nd, 2018
                                plandetail["DJ3"] = productSale["DJ3"]; // LS added on Jan 22nd, 2018
                                plandetail["DJ4"] = productSale["DJ4"]; // LS added on Jan 22nd, 2018
                                plandetail["DJ5"] = productSale["DJ5"]; // LS added on Jan 22nd, 2018
                                plandetail["AssembleQty"] = Convert.ToDecimal(detail["qty"]) * packrate;
                                detailList.Add(plandetail);
                                try //20191001
                                {
                                    H3.DataModel.BizObjectFileHeader[] headers = engine.BizObjectManager.CopyFiles("D000119salesorder", "D000119orderlist", "Attachment", detail.ObjectId, "D000119AssembleSheet", "D000119FGDetail", "Attachment", plandetail.ObjectId, false, false);
                                }
                                catch(Exception e)
                                { }
                            }
                        }
                    }
                }
            }
        }
        if(detailList != null && detailList.Count > 0)
        {
            productionplan["D000119FGDetail"] = detailList.ToArray();
            string instanceId = System.Guid.NewGuid().ToString();
            if(string.IsNullOrEmpty(productionplan.WorkflowInstanceId))
            {
                productionplan.WorkflowInstanceId = instanceId;
            }
            H3.ErrorCode createResult = productionplan.Create();
            if(createResult == H3.ErrorCode.Success)
            {
                //启动流程
                string workItemID = string.Empty;
                string errorMsg = string.Empty;
                H3.Workflow.Template.WorkflowTemplate wfTemp = engine.WorkflowTemplateManager.GetDefaultWorkflow(productionplan.Schema.SchemaCode);
                engine.Interactor.OriginateInstance(userId, productionplan.Schema.SchemaCode,
                    wfTemp.WorkflowVersion, productionplan.ObjectId, productionplan.WorkflowInstanceId, H3.Workflow.WorkItem.AccessMethod.Web,
                    true, string.Empty, true, out workItemID, out  errorMsg);
            }
        }
    }
    public static void CreatePo(H3.IEngine engine, string userid, string pcode, decimal buyqty, string pofrom, string objId, DateTime maindate, DateTime detaildate, string coId, string cname, string cusseqno, string remark, string Orderfrom)
    {
        string targetId = "MPS";
        if(pofrom == "生产计划")
        {
            targetId = "MPS";
        }
        else if(pofrom == "销售订单")
        {
            targetId = "CO";
        }
        else if(pofrom == "采购申请")
        {
            targetId = "POapplyId";
        }
        else if(pofrom == "产品委外")
        {
            targetId = "WO";
        }
        int Days = -15;
        string sqlsep = "select ifnull(F0000132,15) as leadtime from i_D000119IntialSetup order by createdtime desc limit 1 ";
        System.Data.DataTable resultsep = engine.Query.QueryTable(sqlsep, null);
        if(resultsep != null && resultsep.Rows.Count > 0)
        {
            Days = -Convert.ToInt32(resultsep.Rows[0]["leadtime"]);
        }
        if(maindate != null && Days != 0)
        {
            maindate = maindate.AddDays(Days);
        }
        if(detaildate != null && Days != 0)
        {
            detaildate = detaildate.AddDays(Days);
        }
        H3.DataModel.BizObjectSchema poSchema = engine.BizObjectManager.GetPublishedSchema("D000119PO");
        string MyCompany = ""; //主要联系人
        string Myadd = ""; //电话
        string sqlsetup = "select mycompany, myadd from i_D000119OpenGuide order by createdtime desc";
        System.Data.DataTable resultsp = engine.Query.QueryTable(sqlsetup, null);
        if(resultsp != null && resultsp.Rows.Count > 0)
        {
            try
            {
                MyCompany = resultsp.Rows[0]["MyCompany"] + string.Empty; //2018/10/14 新增
                Myadd = resultsp.Rows[0]["Myadd"] + string.Empty; //2018/10/14 新增
            }
            catch(Exception e)
            {
            }
        }
        string pname = "";
        string pspec = "";
        string unit = "";
        string DJ1 = "";
        string DJ2 = "";
        string DJ3 = "";
        string DJ4 = "";
        string DJ5 = "";
        string sqlpd = "select proname,prospec,unit,DJ1,DJ2,DJ3,DJ4,DJ5 from i_D000119Product_sale where objectid = '" + pcode + "'";
        System.Data.DataTable resultpd = engine.Query.QueryTable(sqlpd, null);
        if(resultpd != null && resultpd.Rows.Count > 0)
        {
            pname = Convert.ToString(resultpd.Rows[0]["proname"]);
            pspec = Convert.ToString(resultpd.Rows[0]["prospec"]);
            unit = Convert.ToString(resultpd.Rows[0]["unit"]);
            DJ1 = Convert.ToString(resultpd.Rows[0]["DJ1"]);
            DJ2 = Convert.ToString(resultpd.Rows[0]["DJ2"]);
            DJ3 = Convert.ToString(resultpd.Rows[0]["DJ3"]);
            DJ4 = Convert.ToString(resultpd.Rows[0]["DJ4"]);
            DJ5 = Convert.ToString(resultpd.Rows[0]["DJ5"]);
        }
        decimal netprice = 0;
        decimal tax = 0;
        decimal vatprice = 0;

        string alloId = "";
        string sqla1 = "select objectid from i_D000119selectsupplier t1 where t1.pcode = '" + pcode + "' and t1.status = 1 order by t1.seqno desc limit 1 ";
        System.Data.DataTable resulta1 = engine.Query.QueryTable(sqla1, null);
        if(resulta1 != null && resulta1.Rows.Count > 0)
        {
            alloId = Convert.ToString(resulta1.Rows[0]["objectid"]);
        }
        string sqla2 = "select ifnull(t2.allocation,0) as allocation, t2.scode as scode from i_D000119mainsupplierlist t2 where t2.parentobjectid  = '" + alloId + "'";
        System.Data.DataTable resulta2 = engine.Query.QueryTable(sqla2, null);
        if(resulta2 != null && resulta2.Rows.Count > 0)
        {
            foreach(System.Data.DataRow row in resulta2.Rows)
            {
                string scode = row["scode"] + string.Empty;
                decimal allocation = Convert.ToDecimal(row["allocation"]);
                if(allocation <= 0)
                {
                    continue;
                }
                string Scodes = "";
                string InvoiceType = "";
                string pur_userid = userid;
                string snameshort = "";
                decimal rate = 1;
                string sname = "";
                string RT3 = "";
                string RT4 = "";
                string RT5 = "";
                string CurrencyS = "";
                if(scode != "")
                {
                    H3.DataModel.BizObject supplier = H3.DataModel.BizObject.Load(H3.Organization.User.SystemUserId, engine, "D000119supplier", scode, false);
                    snameshort = supplier["snameshort"] + string.Empty;
                    Scodes = supplier["scode"] + string.Empty;
                    try
                    {
                        InvoiceType = supplier["InvoiceType"] + string.Empty;
                    }
                    catch(Exception e)
                    { }
                    if(supplier["RP1"] + string.Empty != "")
                    {
                        pur_userid = supplier["RP1"] + string.Empty;
                    }
                    sname = supplier["sname"] + string.Empty; //供应商全称
                    RT3 = supplier["RT3"] + string.Empty; //联系人
                    RT4 = supplier["RT4"] + string.Empty; //电话
                    RT5 = supplier["RT5"] + string.Empty; //地址
                    CurrencyS = supplier["CurrencyS"] + string.Empty;
                }
                //最小包装量
                string sqlpoq = "select ExRate,SecondUnit from i_D000119ChangeUnit where pcode ='" + pcode + "' order by createdtime desc ";
                System.Data.DataTable resultpoq = engine.Query.QueryTable(sqlpoq, null);
                if(resultpoq != null && resultpoq.Rows.Count > 0)
                {
                    rate = Convert.ToDecimal(resultpoq.Rows[0]["ExRate"]);
                    unit = Convert.ToString(resultpoq.Rows[0]["SecondUnit"]);
                }
                string sqlp = "select netprice, tax, vatprice from i_D000119SPriceIndex where scode ='" + scode + "' and pcode ='" + pcode + "' and unit ='" + unit + "' and status=1 and Pstatus ='正常使用'order by createdtime desc";
                System.Data.DataTable resultp = engine.Query.QueryTable(sqlp, null);
                if(resultp != null && resultp.Rows.Count > 0)
                {
                    netprice = Convert.ToDecimal(resultp.Rows[0]["netprice"]);
                    tax = Convert.ToDecimal(resultp.Rows[0]["tax"]);
                    vatprice = Convert.ToDecimal(resultp.Rows[0]["vatprice"]);
                }
                //该计划和供应商已经有订单，那么加入子表
                string sqlpo = "select objectid from i_D000119PO where " + targetId + "='" + objId + "' and supplier='" + scode + "' and status!=1 order by createdtime desc ";
                System.Data.DataTable resultpo = engine.Query.QueryTable(sqlpo, null);
                if(resultpo != null && resultpo.Rows.Count > 0)
                {
                    bool flag = true;
                    string poId = Convert.ToString(resultpo.Rows[0]["objectid"]);
                    H3.DataModel.BizObject poObj = H3.DataModel.BizObject.Load(H3.Organization.User.SystemUserId, engine, "D000119PO", poId, false);
                    H3.DataModel.BizObject[] supplierDetails = (H3.DataModel.BizObject[]) poObj["D000119buydetail"];
                    List < H3.DataModel.BizObject > detailList = new List<H3.DataModel.BizObject>();
                    if(supplierDetails != null && supplierDetails.Length > 0)
                    {
                        detailList = new List<H3.DataModel.BizObject>(supplierDetails);
                        foreach(H3.DataModel.BizObject poDetail in detailList)
                        {
                            if(poDetail["pcode"] + string.Empty == pcode)
                            {
                                poDetail["ExchangeRate"] = Convert.ToDecimal(poDetail["ExchangeRate"]) + Math.Ceiling(buyqty * allocation);
                                poDetail["buyqty"] = Convert.ToDecimal(poDetail["buyqty"]) + Math.Ceiling(buyqty * allocation) / rate; //实际采购数量  20200415 rate是包装单位比率
                                poDetail["CheckWay"] = "全检";
                                poDetail["ETA"] = detaildate;
                                poDetail["pcode"] = pcode;
                                poDetail["netprice"] = netprice;
                                poDetail["Tax"] = tax;
                                poDetail["VATprice"] = vatprice;
                                poDetail["F0000023"] = vatprice; //为了实现采购价格低于报价价格走不同的审批流程 20180612
                                poDetail["netextend"] = Convert.ToDecimal(poDetail["netprice"]) * Convert.ToDecimal(poDetail["buyqty"]);
                                poDetail["VATextend"] = Convert.ToDecimal(poDetail["VATprice"]) * Convert.ToDecimal(poDetail["buyqty"]);
                                poDetail["pname"] = pname;
                                poDetail["pspec"] = pspec;
                                poDetail["unit"] = unit; //20200415
                                poDetail["DJ1"] = DJ1; //20200408
                                poDetail["DJ2"] = DJ2;
                                poDetail["DJ3"] = DJ3;
                                poDetail["DJ4"] = DJ4;
                                poDetail["DJ5"] = DJ5;
                                //获取辅助编码
                                string sqlfuzhu = "select Addremarks from I_D000119SecondLevelMBOM where Pcode='" + pcode + "' and Huoqu='" + Scodes + "'";
                                System.Data.DataTable resultfuzhu = engine.Query.QueryTable(sqlfuzhu, null);
                                if(resultfuzhu != null && resultfuzhu.Rows.Count > 0)
                                {
                                    poDetail["pstatus"] = resultfuzhu.Rows[0]["Addremarks"];
                                }
                                flag = false;
                            }
                        }
                    }
                    if(supplierDetails == null || supplierDetails.Length == 0) //全空子表，订单分批=1
                    {
                        H3.DataModel.BizObject newDetail = new H3.DataModel.BizObject(engine, engine.BizObjectManager.GetPublishedSchema("D000119buydetail"), pur_userid);
                        newDetail["ExchangeRate"] = Math.Ceiling(buyqty * allocation);
                        newDetail["buyqty"] = Math.Ceiling(buyqty * allocation) / rate; //实际采购数量  20200415 rate是包装单位比率
                        newDetail["CheckWay"] = "全检";
                        newDetail["ETA"] = detaildate;
                        newDetail["pcode"] = pcode;
                        newDetail["netprice"] = netprice;
                        newDetail["Tax"] = tax;
                        newDetail["VATprice"] = vatprice;
                        newDetail["F0000023"] = vatprice; //为了实现采购价格低于报价价格走不同的审批流程 20180612
                        newDetail["netextend"] = Convert.ToDecimal(newDetail["netprice"]) * Convert.ToDecimal(newDetail["buyqty"]);
                        newDetail["VATextend"] = Convert.ToDecimal(newDetail["VATprice"]) * Convert.ToDecimal(newDetail["buyqty"]);
                        newDetail["pname"] = pname;
                        newDetail["pspec"] = pspec;
                        newDetail["unit"] = unit; //20200415
                        newDetail["DJ1"] = DJ1; //20200408
                        newDetail["DJ2"] = DJ2;
                        newDetail["DJ3"] = DJ3;
                        newDetail["DJ4"] = DJ4;
                        newDetail["DJ5"] = DJ5;
                        //获取辅助编码
                        string sqlfuzhu = "select Addremarks from I_D000119SecondLevelMBOM where Pcode='" + pcode + "' and Huoqu='" + Scodes + "'";
                        System.Data.DataTable resultfuzhu = engine.Query.QueryTable(sqlfuzhu, null);
                        if(resultfuzhu != null && resultfuzhu.Rows.Count > 0)
                        {
                            newDetail["pstatus"] = resultfuzhu.Rows[0]["Addremarks"];
                        }
                        detailList.Add(newDetail);
                    }
                    if(supplierDetails != null && supplierDetails.Length > 0 && flag) //已有供应商，但是这次报价的供应商不同，订单分批=0
                    {
                        H3.DataModel.BizObject newDetail = new H3.DataModel.BizObject(engine, engine.BizObjectManager.GetPublishedSchema("D000119buydetail"), pur_userid);
                        newDetail["ExchangeRate"] = Math.Ceiling(buyqty * allocation);
                        newDetail["buyqty"] = Math.Ceiling(buyqty * allocation) / rate; //实际采购数量  20200415 rate是包装单位比率
                        newDetail["CheckWay"] = "全检";
                        newDetail["ETA"] = detaildate;
                        newDetail["pcode"] = pcode;
                        newDetail["netprice"] = netprice;
                        newDetail["Tax"] = tax;
                        newDetail["VATprice"] = vatprice;
                        newDetail["F0000023"] = vatprice; //为了实现采购价格低于报价价格走不同的审批流程 20180612
                        newDetail["netextend"] = Convert.ToDecimal(newDetail["netprice"]) * Convert.ToDecimal(newDetail["buyqty"]);
                        newDetail["VATextend"] = Convert.ToDecimal(newDetail["VATprice"]) * Convert.ToDecimal(newDetail["buyqty"]);
                        newDetail["pname"] = pname;
                        newDetail["pspec"] = pspec;
                        newDetail["unit"] = unit; //20200415
                        newDetail["DJ1"] = DJ1; //20200408
                        newDetail["DJ2"] = DJ2;
                        newDetail["DJ3"] = DJ3;
                        newDetail["DJ4"] = DJ4;
                        newDetail["DJ5"] = DJ5;
                        //获取辅助编码
                        string sqlfuzhu = "select Addremarks from I_D000119SecondLevelMBOM where Pcode='" + pcode + "' and Huoqu='" + Scodes + "'";
                        System.Data.DataTable resultfuzhu = engine.Query.QueryTable(sqlfuzhu, null);
                        if(resultfuzhu != null && resultfuzhu.Rows.Count > 0)
                        {
                            newDetail["pstatus"] = resultfuzhu.Rows[0]["Addremarks"];
                        }
                        detailList.Add(newDetail);
                    }
                    poObj["D000119buydetail"] = detailList.ToArray();
                    decimal nettotal = 0;
                    decimal vattotal = 0;
                    H3.DataModel.BizObject[] upDetails = (H3.DataModel.BizObject[]) poObj["D000119buydetail"];
                    if(upDetails != null && upDetails.Length > 0)
                    {
                        foreach(H3.DataModel.BizObject poDetail in upDetails)
                        {
                            nettotal += Convert.ToDecimal(poDetail["netextend"]);
                            vattotal += Convert.ToDecimal(poDetail["VATextend"]);
                        }
                    }
                    poObj["nettotal"] = nettotal;
                    poObj["VATtotal"] = vattotal;
                    poObj["TaxAmount"] = Convert.ToDecimal(poObj["VATtotal"]) - Convert.ToDecimal(poObj["nettotal"]);
                    poObj.Update();
                }
                else
                {
                    List < H3.DataModel.BizObject > detailList = new List<H3.DataModel.BizObject>();
                    H3.DataModel.BizObject poObj = new H3.DataModel.BizObject(engine, engine.BizObjectManager.GetPublishedSchema("D000119PO"), pur_userid);
                    H3.DataModel.BizObject newDetail = new H3.DataModel.BizObject(engine, engine.BizObjectManager.GetPublishedSchema("D000119buydetail"), pur_userid);
                    newDetail["ExchangeRate"] = Math.Ceiling(buyqty * allocation);
                    newDetail["buyqty"] = Math.Ceiling(buyqty * allocation) / rate; //实际采购数量  20200415 rate是包装单位比率
                    newDetail["CheckWay"] = "全检";
                    newDetail["ETA"] = detaildate;
                    newDetail["pcode"] = pcode;
                    newDetail["netprice"] = netprice;
                    newDetail["Tax"] = tax;
                    newDetail["VATprice"] = vatprice;
                    newDetail["F0000023"] = vatprice; //为了实现采购价格低于报价价格走不同的审批流程 20180612
                    newDetail["netextend"] = Convert.ToDecimal(newDetail["netprice"]) * Convert.ToDecimal(newDetail["buyqty"]);
                    newDetail["VATextend"] = Convert.ToDecimal(newDetail["VATprice"]) * Convert.ToDecimal(newDetail["buyqty"]);
                    newDetail["pname"] = pname;
                    newDetail["pspec"] = pspec;
                    newDetail["unit"] = unit; //20200415
                    newDetail["DJ1"] = DJ1; //20200408
                    newDetail["DJ2"] = DJ2;
                    newDetail["DJ3"] = DJ3;
                    newDetail["DJ4"] = DJ4;
                    newDetail["DJ5"] = DJ5;
                    //获取辅助编码
                    string sqlfuzhu = "select Addremarks from I_D000119SecondLevelMBOM where Pcode='" + pcode + "' and Huoqu='" + Scodes + "'";
                    System.Data.DataTable resultfuzhu = engine.Query.QueryTable(sqlfuzhu, null);
                    if(resultfuzhu != null && resultfuzhu.Rows.Count > 0)
                    {
                        newDetail["pstatus"] = resultfuzhu.Rows[0]["Addremarks"];
                    }
                    detailList.Add(newDetail);
                    poObj["D000119buydetail"] = detailList.ToArray();
                    poObj["snameshort"] = snameshort;
                    poObj["deliverydate"] = maindate;
                    poObj["ReturnDate"] = maindate;
                    poObj["CO"] = coId;
                    if(pofrom == "生产计划")
                    {
                        poObj["MPS"] = objId;
                    }
                    else if(pofrom == "采购申请")
                    {
                        poObj["POapplyId"] = objId;
                    }
                    else if(pofrom == "产品委外")
                    {
                        poObj["WO"] = objId;
                    }
                    try
                    {
                        poObj["CoNo20200722"] = cusseqno;
                    }
                    catch(Exception e)
                    {
                    }
                    poObj["Cname"] = cname;
                    poObj["Reason"] = remark; //20200309 订单说明
                    poObj["F0000021"] = pofrom;
                    poObj["postatus"] = "订单进行：待采购";
                    poObj["RT5"] = "正常";
                    poObj["check0sum"] = 0; //LS 20180715 走不同的审批流程，预先赋值
                    poObj["POapplied"] = pur_userid;
                    poObj["supplier"] = scode;
                    poObj["ProdBase"] = InvoiceType;//发票类型
                    poObj["PoStatusCal"] = sname; //供应商全称
                    poObj["InvoiceNo"] = RT3; //联系人
                    poObj["RT4"] = RT4; //电话
                    poObj["RT5"] = RT5; //地址
                    poObj["Payment"] = MyCompany; // LS 20200530
                    poObj["Myadd"] = Myadd; // LS 20200530
                    poObj["currency"] = CurrencyS;
                    poObj["OwnerId"] = pur_userid;
                    poObj["RT3"] = Orderfrom; //20191211 有效库存问题

                    poObj["nettotal"] = newDetail["netextend"];
                    poObj["VATtotal"] = newDetail["VATextend"];
                    poObj["TaxAmount"] = Convert.ToDecimal(poObj["VATtotal"]) - Convert.ToDecimal(poObj["nettotal"]);
                    string instanceId = System.Guid.NewGuid().ToString();
                    if(string.IsNullOrEmpty(poObj.WorkflowInstanceId))
                    {
                        poObj.WorkflowInstanceId = instanceId;
                    }
                    H3.ErrorCode createResult = poObj.Create();
                    if(createResult == H3.ErrorCode.Success)
                    {
                        //启动流程
                        string workItemID = string.Empty;
                        string errorMsg = string.Empty;
                        H3.Workflow.Template.WorkflowTemplate wfTemp = engine.WorkflowTemplateManager.GetDefaultWorkflow(poObj.Schema.SchemaCode);
                        engine.Interactor.OriginateInstance(pur_userid, poObj.Schema.SchemaCode,
                            wfTemp.WorkflowVersion, poObj.ObjectId, poObj.WorkflowInstanceId, H3.Workflow.WorkItem.AccessMethod.Web,
                            true, string.Empty, true, out workItemID, out  errorMsg);
                    }
                }
            }
        }
        else //没有订单分配
        {
            //最小包装量
            decimal rate = 1;
            string sqlpoq = "select ExRate,SecondUnit from i_D000119ChangeUnit where pcode ='" + pcode + "' order by createdtime desc ";
            System.Data.DataTable resultpoq = engine.Query.QueryTable(sqlpoq, null);
            if(resultpoq != null && resultpoq.Rows.Count > 0)
            {
                rate = Convert.ToDecimal(resultpoq.Rows[0]["ExRate"]);
                unit = Convert.ToString(resultpoq.Rows[0]["SecondUnit"]);
            }
            string scode = "";
            decimal allocation = 1;
            string sqlp = "select netprice, tax, vatprice, scode from i_D000119SPriceIndex where pcode ='" + pcode + "' and unit ='" + unit + "' and status=1 and Pstatus ='正常使用'order by createdtime desc";
            System.Data.DataTable resultp = engine.Query.QueryTable(sqlp, null);
            if(resultp != null && resultp.Rows.Count > 0)
            {
                netprice = Convert.ToDecimal(resultp.Rows[0]["netprice"]);
                tax = Convert.ToDecimal(resultp.Rows[0]["tax"]);
                vatprice = Convert.ToDecimal(resultp.Rows[0]["vatprice"]);
                scode = Convert.ToString(resultp.Rows[0]["scode"]);
                H3.DataModel.BizObject supplier = H3.DataModel.BizObject.Load(H3.Organization.User.SystemUserId, engine, "D000119supplier", scode, false);
                string Scodes = supplier["scode"] + string.Empty;
                string InvoiceType = "";
                try
                {
                    InvoiceType = supplier["InvoiceType"] + string.Empty;
                }
                catch(Exception e)
                { }
                string pur_userid = userid;
                if(supplier["RP1"] + string.Empty != "")
                {
                    pur_userid = supplier["RP1"] + string.Empty;
                }
                string snameshort = supplier["snameshort"] + string.Empty;
                //该计划和供应商已经有订单，那么加入子表
                string sqlpo = "select objectid from i_D000119PO where " + targetId + "='" + objId + "' and supplier='" + scode + "' and status!=1 order by createdtime desc ";
                System.Data.DataTable resultpo = engine.Query.QueryTable(sqlpo, null);
                if(resultpo != null && resultpo.Rows.Count > 0)
                {
                    bool flag = true;
                    string poId = Convert.ToString(resultpo.Rows[0]["objectid"]);
                    H3.DataModel.BizObject poObj = H3.DataModel.BizObject.Load(H3.Organization.User.SystemUserId, engine, "D000119PO", poId, false);
                    H3.DataModel.BizObject[] supplierDetails = (H3.DataModel.BizObject[]) poObj["D000119buydetail"];
                    List < H3.DataModel.BizObject > detailList = new List<H3.DataModel.BizObject>();
                    if(supplierDetails != null && supplierDetails.Length > 0)
                    {
                        detailList = new List<H3.DataModel.BizObject>(supplierDetails);
                        foreach(H3.DataModel.BizObject poDetail in detailList)
                        {
                            if(poDetail["pcode"] + string.Empty == pcode)
                            {
                                poDetail["ExchangeRate"] = Convert.ToDecimal(poDetail["ExchangeRate"]) + Math.Ceiling(buyqty * allocation);
                                poDetail["buyqty"] = Convert.ToDecimal(poDetail["buyqty"]) + Math.Ceiling(buyqty * allocation) / rate; //实际采购数量  20200415 rate是包装单位比率
                                poDetail["CheckWay"] = "全检";
                                poDetail["ETA"] = detaildate;
                                poDetail["pcode"] = pcode;
                                poDetail["netprice"] = netprice;
                                poDetail["Tax"] = tax;
                                poDetail["VATprice"] = vatprice;
                                poDetail["F0000023"] = vatprice; //为了实现采购价格低于报价价格走不同的审批流程 20180612
                                poDetail["netextend"] = Convert.ToDecimal(poDetail["netprice"]) * Convert.ToDecimal(poDetail["buyqty"]);
                                poDetail["VATextend"] = Convert.ToDecimal(poDetail["VATprice"]) * Convert.ToDecimal(poDetail["buyqty"]);
                                poDetail["pname"] = pname;
                                poDetail["pspec"] = pspec;
                                poDetail["unit"] = unit; //20200415
                                poDetail["DJ1"] = DJ1; //20200408
                                poDetail["DJ2"] = DJ2;
                                poDetail["DJ3"] = DJ3;
                                poDetail["DJ4"] = DJ4;
                                poDetail["DJ5"] = DJ5;
                                //获取辅助编码
                                string sqlfuzhu = "select Addremarks from I_D000119SecondLevelMBOM where Pcode='" + pcode + "' and Huoqu='" + Scodes + "'";
                                System.Data.DataTable resultfuzhu = engine.Query.QueryTable(sqlfuzhu, null);
                                if(resultfuzhu != null && resultfuzhu.Rows.Count > 0)
                                {
                                    poDetail["pstatus"] = resultfuzhu.Rows[0]["Addremarks"];
                                }
                                flag = false;
                            }
                        }
                    }
                    if(supplierDetails == null || supplierDetails.Length == 0) //全空子表，订单分批=1
                    {
                        H3.DataModel.BizObject newDetail = new H3.DataModel.BizObject(engine, engine.BizObjectManager.GetPublishedSchema("D000119buydetail"), pur_userid);
                        newDetail["ExchangeRate"] = Math.Ceiling(buyqty * allocation);
                        newDetail["buyqty"] = Math.Ceiling(buyqty * allocation) / rate; //实际采购数量  20200415 rate是包装单位比率
                        newDetail["CheckWay"] = "全检";
                        newDetail["ETA"] = detaildate;
                        newDetail["pcode"] = pcode;
                        newDetail["netprice"] = netprice;
                        newDetail["Tax"] = tax;
                        newDetail["VATprice"] = vatprice;
                        newDetail["F0000023"] = vatprice; //为了实现采购价格低于报价价格走不同的审批流程 20180612
                        newDetail["netextend"] = Convert.ToDecimal(newDetail["netprice"]) * Convert.ToDecimal(newDetail["buyqty"]);
                        newDetail["VATextend"] = Convert.ToDecimal(newDetail["VATprice"]) * Convert.ToDecimal(newDetail["buyqty"]);
                        newDetail["pname"] = pname;
                        newDetail["pspec"] = pspec;
                        newDetail["unit"] = unit; //20200415
                        newDetail["DJ1"] = DJ1; //20200408
                        newDetail["DJ2"] = DJ2;
                        newDetail["DJ3"] = DJ3;
                        newDetail["DJ4"] = DJ4;
                        newDetail["DJ5"] = DJ5;
                        //获取辅助编码
                        string sqlfuzhu = "select Addremarks from I_D000119SecondLevelMBOM where Pcode='" + pcode + "' and Huoqu='" + Scodes + "'";
                        System.Data.DataTable resultfuzhu = engine.Query.QueryTable(sqlfuzhu, null);
                        if(resultfuzhu != null && resultfuzhu.Rows.Count > 0)
                        {
                            newDetail["pstatus"] = resultfuzhu.Rows[0]["Addremarks"];
                        }
                        detailList.Add(newDetail);
                    }
                    if(supplierDetails != null && supplierDetails.Length > 0 && flag) //已有供应商，但是这次报价的供应商不同，订单分批=0
                    {
                        H3.DataModel.BizObject newDetail = new H3.DataModel.BizObject(engine, engine.BizObjectManager.GetPublishedSchema("D000119buydetail"), pur_userid);
                        newDetail["ExchangeRate"] = Math.Ceiling(buyqty * allocation);
                        newDetail["buyqty"] = Math.Ceiling(buyqty * allocation) / rate; //实际采购数量  20200415 rate是包装单位比率
                        newDetail["CheckWay"] = "全检";
                        newDetail["ETA"] = detaildate;
                        newDetail["pcode"] = pcode;
                        newDetail["netprice"] = netprice;
                        newDetail["Tax"] = tax;
                        newDetail["VATprice"] = vatprice;
                        newDetail["F0000023"] = vatprice; //为了实现采购价格低于报价价格走不同的审批流程 20180612
                        newDetail["netextend"] = Convert.ToDecimal(newDetail["netprice"]) * Convert.ToDecimal(newDetail["buyqty"]);
                        newDetail["VATextend"] = Convert.ToDecimal(newDetail["VATprice"]) * Convert.ToDecimal(newDetail["buyqty"]);
                        newDetail["pname"] = pname;
                        newDetail["pspec"] = pspec;
                        newDetail["unit"] = unit; //20200415
                        newDetail["DJ1"] = DJ1; //20200408
                        newDetail["DJ2"] = DJ2;
                        newDetail["DJ3"] = DJ3;
                        newDetail["DJ4"] = DJ4;
                        newDetail["DJ5"] = DJ5;
                        //获取辅助编码
                        string sqlfuzhu = "select Addremarks from I_D000119SecondLevelMBOM where Pcode='" + pcode + "' and Huoqu='" + Scodes + "'";
                        System.Data.DataTable resultfuzhu = engine.Query.QueryTable(sqlfuzhu, null);
                        if(resultfuzhu != null && resultfuzhu.Rows.Count > 0)
                        {
                            newDetail["pstatus"] = resultfuzhu.Rows[0]["Addremarks"];
                        }
                        detailList.Add(newDetail);
                    }
                    poObj["D000119buydetail"] = detailList.ToArray();
                    decimal nettotal = 0;
                    decimal vattotal = 0;
                    H3.DataModel.BizObject[] upDetails = (H3.DataModel.BizObject[]) poObj["D000119buydetail"];
                    if(upDetails != null && upDetails.Length > 0)
                    {
                        foreach(H3.DataModel.BizObject poDetail in upDetails)
                        {
                            nettotal += Convert.ToDecimal(poDetail["netextend"]);
                            vattotal += Convert.ToDecimal(poDetail["VATextend"]);
                        }
                    }
                    poObj["nettotal"] = nettotal;
                    poObj["VATtotal"] = vattotal;
                    poObj["TaxAmount"] = Convert.ToDecimal(poObj["VATtotal"]) - Convert.ToDecimal(poObj["nettotal"]);
                    poObj.Update();
                }
                else
                {
                    H3.DataModel.BizObject poObj = new H3.DataModel.BizObject(engine, engine.BizObjectManager.GetPublishedSchema("D000119PO"), pur_userid);
                    List < H3.DataModel.BizObject > detailList = new List<H3.DataModel.BizObject>();
                    H3.DataModel.BizObject newDetail = new H3.DataModel.BizObject(engine, engine.BizObjectManager.GetPublishedSchema("D000119buydetail"), pur_userid);
                    newDetail["ExchangeRate"] = Math.Ceiling(buyqty * allocation);
                    newDetail["buyqty"] = Math.Ceiling(buyqty * allocation) / rate; //实际采购数量  20200415 rate是包装单位比率
                    newDetail["CheckWay"] = "全检";
                    newDetail["ETA"] = detaildate;
                    newDetail["pcode"] = pcode;
                    newDetail["netprice"] = netprice;
                    newDetail["Tax"] = tax;
                    newDetail["VATprice"] = vatprice;
                    newDetail["F0000023"] = vatprice; //为了实现采购价格低于报价价格走不同的审批流程 20180612
                    newDetail["netextend"] = Convert.ToDecimal(newDetail["netprice"]) * Convert.ToDecimal(newDetail["buyqty"]);
                    newDetail["VATextend"] = Convert.ToDecimal(newDetail["VATprice"]) * Convert.ToDecimal(newDetail["buyqty"]);
                    newDetail["pname"] = pname;
                    newDetail["pspec"] = pspec;
                    newDetail["unit"] = unit; //20200415
                    newDetail["DJ1"] = DJ1; //20200408
                    newDetail["DJ2"] = DJ2;
                    newDetail["DJ3"] = DJ3;
                    newDetail["DJ4"] = DJ4;
                    newDetail["DJ5"] = DJ5;
                    //获取辅助编码
                    string sqlfuzhu = "select Addremarks from I_D000119SecondLevelMBOM where Pcode='" + pcode + "' and Huoqu='" + Scodes + "'";
                    System.Data.DataTable resultfuzhu = engine.Query.QueryTable(sqlfuzhu, null);
                    if(resultfuzhu != null && resultfuzhu.Rows.Count > 0)
                    {
                        newDetail["pstatus"] = resultfuzhu.Rows[0]["Addremarks"];
                    }
                    detailList.Add(newDetail);
                    poObj["D000119buydetail"] = detailList.ToArray();
                    poObj["snameshort"] = supplier["snameshort"];
                    poObj["deliverydate"] = maindate;
                    poObj["ReturnDate"] = maindate;

                    poObj["CO"] = coId;
                    if(pofrom == "生产计划")
                    {
                        poObj["MPS"] = objId;
                    }
                    else if(pofrom == "采购申请")
                    {
                        poObj["POapplyId"] = objId;
                    }
                    else if(pofrom == "产品委外")
                    {
                        poObj["WO"] = objId;
                    }
                    try
                    {
                        poObj["CoNo20200722"] = cusseqno;
                    }
                    catch(Exception e)
                    {
                    }
                    poObj["Cname"] = cname;
                    poObj["Reason"] = remark; //20200309 订单说明
                    poObj["F0000021"] = pofrom;
                    poObj["postatus"] = "订单进行：待采购";
                    poObj["RT5"] = "正常";
                    poObj["check0sum"] = 0; //LS 20180715 走不同的审批流程，预先赋值
                    poObj["POapplied"] = pur_userid;
                    poObj["supplier"] = scode;
                    poObj["ProdBase"] = InvoiceType;//发票类型
                    poObj["PoStatusCal"] = supplier["sname"]; //供应商全称
                    poObj["InvoiceNo"] = supplier["RT3"]; //联系人
                    poObj["RT4"] = supplier["RT4"]; //电话
                    poObj["RT5"] = supplier["RT5"]; //地址
                    poObj["Payment"] = MyCompany; // LS 20200530
                    poObj["Myadd"] = Myadd; // LS 20200530
                    poObj["currency"] = supplier["CurrencyS"];
                    poObj["OwnerId"] = pur_userid;
                    poObj["RT3"] = Orderfrom; //20191211 有效库存问题
                    poObj["nettotal"] = newDetail["netextend"];
                    poObj["VATtotal"] = newDetail["VATextend"];
                    poObj["TaxAmount"] = Convert.ToDecimal(poObj["VATtotal"]) - Convert.ToDecimal(poObj["nettotal"]);
                    string instanceId = System.Guid.NewGuid().ToString();
                    if(string.IsNullOrEmpty(poObj.WorkflowInstanceId))
                    {
                        poObj.WorkflowInstanceId = instanceId;
                    }
                    H3.ErrorCode createResult = poObj.Create();
                    if(createResult == H3.ErrorCode.Success)
                    {
                        //启动流程
                        string workItemID = string.Empty;
                        string errorMsg = string.Empty;
                        H3.Workflow.Template.WorkflowTemplate wfTemp = engine.WorkflowTemplateManager.GetDefaultWorkflow(poObj.Schema.SchemaCode);
                        engine.Interactor.OriginateInstance(pur_userid, poObj.Schema.SchemaCode,
                            wfTemp.WorkflowVersion, poObj.ObjectId, poObj.WorkflowInstanceId, H3.Workflow.WorkItem.AccessMethod.Web,
                            true, string.Empty, true, out workItemID, out  errorMsg);
                    }
                }
            }
            else //产品没有报价
            {
                H3.DataModel.BizObject productObj = H3.DataModel.BizObject.Load(H3.Organization.User.SystemUserId, engine, "D000119Product_sale", pcode, false);
                netprice = 0;
                tax = 0.13m;
                vatprice = 0;
                string pur_userid = userid;
                H3.DataModel.BizObject poObj = new H3.DataModel.BizObject(engine, engine.BizObjectManager.GetPublishedSchema("D000119PO"), pur_userid);
                List < H3.DataModel.BizObject > detailList = new List<H3.DataModel.BizObject>();
                H3.DataModel.BizObject newDetail = new H3.DataModel.BizObject(engine, engine.BizObjectManager.GetPublishedSchema("D000119buydetail"), pur_userid);
                newDetail["ExchangeRate"] = Math.Ceiling(buyqty * allocation);
                newDetail["buyqty"] = Math.Ceiling(buyqty * allocation) / rate; //实际采购数量  20200415 rate是包装单位比率
                newDetail["CheckWay"] = "全检";
                newDetail["ETA"] = detaildate;
                newDetail["pcode"] = pcode;
                newDetail["netprice"] = netprice;
                newDetail["Tax"] = tax;
                newDetail["VATprice"] = vatprice;
                newDetail["F0000023"] = vatprice; //为了实现采购价格低于报价价格走不同的审批流程 20180612
                newDetail["netextend"] = Convert.ToDecimal(newDetail["netprice"]) * Convert.ToDecimal(newDetail["buyqty"]);
                newDetail["VATextend"] = Convert.ToDecimal(newDetail["VATprice"]) * Convert.ToDecimal(newDetail["buyqty"]);
                newDetail["pname"] = pname;
                newDetail["pspec"] = pspec;
                newDetail["unit"] = unit; //20200415
                newDetail["DJ1"] = DJ1; //20200408
                newDetail["DJ2"] = DJ2;
                newDetail["DJ3"] = DJ3;
                newDetail["DJ4"] = DJ4;
                newDetail["DJ5"] = DJ5;

                detailList.Add(newDetail);
                poObj["D000119buydetail"] = detailList.ToArray();
                poObj["snameshort"] = "";
                poObj["deliverydate"] = maindate;
                poObj["ReturnDate"] = maindate;

                poObj["CO"] = coId;
                if(pofrom == "生产计划")
                {
                    poObj["MPS"] = objId;
                }
                else if(pofrom == "采购申请")
                {
                    poObj["POapplyId"] = objId;
                }
                else if(pofrom == "产品委外")
                {
                    poObj["WO"] = objId;
                }
                try
                {
                    poObj["CoNo20200722"] = cusseqno;
                }
                catch(Exception e)
                {
                }
                poObj["Cname"] = cname;
                poObj["Reason"] = remark; //20200309 订单说明
                poObj["F0000021"] = pofrom;
                poObj["postatus"] = "订单进行：待采购";
                poObj["RT5"] = "正常";
                poObj["check0sum"] = 0; //LS 20180715 走不同的审批流程，预先赋值
                poObj["POapplied"] = pur_userid;
                poObj["supplier"] = scode;
                poObj["ProdBase"] = "";//发票类型
                poObj["PoStatusCal"] = ""; //供应商全称
                poObj["InvoiceNo"] = ""; //联系人
                poObj["RT4"] = ""; //电话
                poObj["RT5"] = ""; //地址
                poObj["Payment"] = MyCompany; // LS 20200530
                poObj["Myadd"] = Myadd; // LS 20200530
                poObj["currency"] = "";
                poObj["OwnerId"] = pur_userid;
                poObj["RT3"] = Orderfrom; //20191211 有效库存问题

                poObj["nettotal"] = Convert.ToDecimal(poObj["nettotal"]) + Convert.ToDecimal(newDetail["netextend"]);
                poObj["VATtotal"] = Convert.ToDecimal(poObj["VATtotal"]) + Convert.ToDecimal(newDetail["VATextend"]);
                poObj["TaxAmount"] = Convert.ToDecimal(poObj["VATtotal"]) - Convert.ToDecimal(poObj["nettotal"]);
                string instanceId = System.Guid.NewGuid().ToString();
                if(string.IsNullOrEmpty(poObj.WorkflowInstanceId))
                {
                    poObj.WorkflowInstanceId = instanceId;
                }
                H3.ErrorCode createResult = poObj.Create();
                if(createResult == H3.ErrorCode.Success)
                {
                    //启动流程
                    string workItemID = string.Empty;
                    string errorMsg = string.Empty;
                    H3.Workflow.Template.WorkflowTemplate wfTemp = engine.WorkflowTemplateManager.GetDefaultWorkflow(poObj.Schema.SchemaCode);
                    engine.Interactor.OriginateInstance(pur_userid, poObj.Schema.SchemaCode,
                        wfTemp.WorkflowVersion, poObj.ObjectId, poObj.WorkflowInstanceId, H3.Workflow.WorkItem.AccessMethod.Web,
                        true, string.Empty, true, out workItemID, out  errorMsg);
                }
            }
        }
    }
    public void CreatePlan2nd(H3.IEngine engine, string userId, H3.DataModel.BizObject thisObj, bool flag) {
        H3.DataModel.BizObjectSchema productionplanSchema = engine.BizObjectManager.GetPublishedSchema("D000119productionplan");
        H3.DataModel.BizObject productionplan = new H3.DataModel.BizObject(engine, productionplanSchema, userId);
        productionplan["YesnoStock"] = "客户订单";
        productionplan["Cname"] = thisObj["cnameshort"];
        productionplan["deliverydate"] = thisObj["deliverydaterequired"]; //承诺交期
        productionplan["RE2"] = thisObj["deliverydaterequired"]; //要求交期
        productionplan["ppstatus"] = "订单进行：待计划";
        productionplan["Planner"] = thisObj["Planner"]; // LS added on Jan 22nd, 2018
        productionplan["CoLink"] = thisObj["SeqNo"];
        try
        {
            productionplan["CoNo20200722"] = thisObj["CoNo20200722"];
            productionplan["salesmanager20210501"] = thisObj["salesmanager"];
        }
        catch(Exception e)
        {

        }
        productionplan["co"] = thisObj.ObjectId;
        productionplan["ccode"] = thisObj["ccode"];
        productionplan["RD4"] = thisObj["OrderQty"]; //销售订单数量
        productionplan["RT1"] = thisObj["RT1"]; // LS added on Apr 9th, 2018
        productionplan["RT2"] = thisObj["RT2"]; // LS added on Apr 9th, 2018
        productionplan["RT3"] = thisObj["RT3"]; // LS added on Apr 9th, 2018
        productionplan["RD1"] = thisObj["RD1"]; // LS added on Apr 9th, 2018
        productionplan["RD2"] = thisObj["RD2"]; // LS added on Apr 9th, 2018
        productionplan["RD3"] = thisObj["RD3"]; // LS added on Apr 9th, 2018
        productionplan["RE1"] = thisObj["RE1"]; // LS added on Apr 9th, 2018
        productionplan["RP1"] = thisObj["RP1"]; // LS added on Apr 9th, 2018
        productionplan["ProdBase"] = thisObj["ProdBase"]; //简易说明 20200309
        List < H3.DataModel.BizObject > detailList = new List<H3.DataModel.BizObject>();
        Dictionary < string, string > objectIds = new Dictionary<string, string>();
        H3.DataModel.BizObject[] details = (H3.DataModel.BizObject[]) thisObj["D000119orderlist"];
        if(details != null && details.Length > 0)
        {
            foreach(H3.DataModel.BizObject detail in details)
            {
                if(flag) //下推生产计划，直接生成
                {
                    if(detail["makebuy"] + string.Empty != "采购" && Convert.ToDecimal(detail["processqty"]) > 0)
                    {
                        decimal packrate = StockRelated.packrate(engine, Convert.ToString(detail["pcode"]), Convert.ToString(detail["unit"])); //******** 新增
                        H3.DataModel.BizObject tempObj = detailList.Find(x => x["pcode"] + string.Empty == Convert.ToString(detail["pcode"]));
                        if(tempObj != null)
                        {
                            tempObj["recomqty"] = Convert.ToDecimal(tempObj["recomqty"]) + Convert.ToDecimal(detail["processqty"]) * packrate;
                            tempObj["RD3"] = Convert.ToDecimal(tempObj["RD3"]) + Convert.ToDecimal(detail["processqty"]) * packrate;
                        }
                        else
                        {
                            H3.DataModel.BizObject productSale = H3.DataModel.BizObject.Load(userId, engine, "D000119Product_sale", detail["pcode"] + string.Empty, false);
                            H3.DataModel.BizObject plandetail = new H3.DataModel.BizObject(engine, engine.BizObjectManager.GetPublishedSchema("D000119plandetail"), userId);
                            plandetail["pcode"] = detail["pcode"];
                            plandetail["Pcodes"] = detail["PcodeS"]; //辅助编码
                            plandetail["pname"] = productSale["Proname"];
                            plandetail["requirement"] = detail["requirement"];
                            plandetail["PstatusBefore"] = thisObj["SeqNo"] + ":" + detail["requirement"];
                            plandetail["LinkStatus"] = thisObj["SeqNo"]; //销售订单号码写到子表，为了订单合并时，记录都是那个销售订单的
                            plandetail["InspectionStandard"] = detail["InspectionStandard"]; // LS added on Jan 22nd, 2018
                            plandetail["pspec"] = productSale["Prospec"];
                            plandetail["unit"] = productSale["Unit"];
                            plandetail["DJ1"] = productSale["DJ1"]; // LS added on Jan 22nd, 2018
                            plandetail["DJ2"] = productSale["DJ2"]; // LS added on Jan 22nd, 2018
                            plandetail["DJ3"] = productSale["DJ3"]; // LS added on Jan 22nd, 2018
                            plandetail["DJ4"] = productSale["DJ4"]; // LS added on Jan 22nd, 2018
                            plandetail["DJ5"] = productSale["DJ5"]; // LS added on Jan 22nd, 2018
                            plandetail["PgroupS"] = productSale["PgroupS"]; // LS added on Apr 5th, 2018
                            plandetail["Pgroup"] = productSale["Pgroup"]; // LS added on Apr 5th, 2018
                            plandetail["PgroupM"] = productSale["PgroupM"]; // LS added on Apr 5th, 2018
                            plandetail["PgroupR"] = productSale["PgroupR"]; // LS added on Apr 5th, 2018
                            plandetail["makebuy"] = productSale["huoqu"];
                            plandetail["orderqty"] = detail["qty"];

                            if(detail["makebuy"] + string.Empty != "采购")
                            {
                                plandetail["recomqty"] = GetBumByVaribleLossRate(engine, detail["pcode"] + string.Empty, Convert.ToDecimal(detail["processqty"]) * packrate);
                                plandetail["RD3"] = GetBumByVaribleLossRate(engine, detail["pcode"] + string.Empty, Convert.ToDecimal(detail["processqty"]) * packrate); //计划数量 γ1.6
                            }
                            else
                            {
                                plandetail["recomqty"] = Convert.ToDecimal(detail["processqty"]) * packrate;
                                plandetail["RD3"] = Convert.ToDecimal(detail["processqty"]) * packrate; //计划数量 γ1.6
                            }
                            plandetail["RE1"] = detail["RE1"]; //交货日期
                            if(detail["RE1"] + string.Empty != "")
                            {
                                int Days = 0;
                                H3.DataModel.BizObject[] SPsales = H3.DataModel.BizObject.GetList(engine, userId,
                                    engine.BizObjectManager.GetPublishedSchema("D000119IntialSetup"), H3.DataModel.GetListScopeType.GlobalAll,
                                    new H3.Data.Filter.Filter());
                                if(SPsales != null && SPsales.Length > 0)
                                {
                                    Days = -Convert.ToInt32(SPsales[0]["F0000126"]);
                                }
                                DateTime time = Convert.ToDateTime(detail["RE1"]);
                                time = time.AddDays(Days);
                                plandetail["startdate"] = time;//开工日期，系统默认提前7天
                            }

                            try
                            {
                                plandetail["DJ620210830"] = productSale["DJ620210830"];
                                plandetail["DJ720210830"] = productSale["DJ720210830"];
                                plandetail["RD220210830"] = detail["RD220210830"];
                                plandetail["RD320210830"] = detail["RD320210830"];
                                plandetail["RT120210830"] = detail["RT120210830"];
                                plandetail["RT220210830"] = detail["RT220210830"];
                                plandetail["RT320210830"] = detail["RT320210830"];
                            }
                            catch(Exception e)
                            {
                            }
                            detailList.Add(plandetail);
                            H3.DataModel.BizObjectFileHeader[] headers = engine.BizObjectManager.CopyFiles("D000119salesorder", "D000119orderlist", "Attachment", detail.ObjectId, "D000119productionplan", "D000119plandetail", "Attachment", plandetail.ObjectId, false, false);
                            try
                            {
                                H3.DataModel.BizObjectFileHeader[] headers1 = engine.BizObjectManager.CopyFiles("D000119Product_sale", "", "DJ820210830", productSale.ObjectId, "D000119productionplan", "D000119plandetail", "DJ820210830", plandetail.ObjectId, false, false);
                                H3.DataModel.BizObjectFileHeader[] headers2 = engine.BizObjectManager.CopyFiles("D000119Product_sale", "", "DJ920210830", productSale.ObjectId, "D000119productionplan", "D000119plandetail", "DJ920210830", plandetail.ObjectId, false, false);
                            }
                            catch(Exception e)
                            {
                            }
                        }
                    }
                }
                else //下推系统选择，有bom才生成生产计划
                {
                    H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();
                    filter.Matcher = new H3.Data.Filter.ItemMatcher("Pcode", H3.Data.ComparisonOperatorType.Equal, detail["pcode"] + string.Empty);
                    H3.DataModel.BizObject[] list = H3.DataModel.BizObject.GetList(engine, userId, engine.BizObjectManager.GetPublishedSchema("D000119MbomCreated"),
                        H3.DataModel.GetListScopeType.GlobalAll, filter);
                    if(list != null && list.Length > 0)
                    {
                        if(detail["makebuy"] + string.Empty != "采购" && Convert.ToDecimal(detail["processqty"]) > 0)
                        {
                            decimal packrate = StockRelated.packrate(engine, Convert.ToString(detail["pcode"]), Convert.ToString(detail["unit"])); //******** 新增
                            H3.DataModel.BizObject tempObj = detailList.Find(x => x["pcode"] + string.Empty == detail["pcode"] + string.Empty);
                            if(tempObj != null)
                            {
                                tempObj["recomqty"] = Convert.ToDecimal(tempObj["recomqty"]) + Convert.ToDecimal(detail["processqty"]) * packrate;
                                tempObj["RD3"] = Convert.ToDecimal(tempObj["RD3"]) + Convert.ToDecimal(detail["processqty"]) * packrate;
                            }
                            else
                            {
                                H3.DataModel.BizObject productSale = H3.DataModel.BizObject.Load(userId, engine, "D000119Product_sale", detail["pcode"] + string.Empty, false);
                                H3.DataModel.BizObject plandetail = new H3.DataModel.BizObject(engine, engine.BizObjectManager.GetPublishedSchema("D000119plandetail"), userId);
                                plandetail["pcode"] = detail["pcode"];
                                plandetail["Pcodes"] = detail["PcodeS"]; //辅助编码
                                plandetail["pname"] = productSale["Proname"];
                                plandetail["requirement"] = detail["requirement"];
                                plandetail["PstatusBefore"] = thisObj["SeqNo"] + ":" + detail["requirement"];
                                plandetail["LinkStatus"] = thisObj["SeqNo"]; //销售订单号码写到子表，为了订单合并时，记录都是那个销售订单的
                                plandetail["InspectionStandard"] = detail["InspectionStandard"]; // LS added on Jan 22nd, 2018
                                plandetail["pspec"] = productSale["Prospec"];
                                plandetail["unit"] = productSale["Unit"];
                                plandetail["DJ1"] = productSale["DJ1"]; // LS added on Jan 22nd, 2018
                                plandetail["DJ2"] = productSale["DJ2"]; // LS added on Jan 22nd, 2018
                                plandetail["DJ3"] = productSale["DJ3"]; // LS added on Jan 22nd, 2018
                                plandetail["DJ4"] = productSale["DJ4"]; // LS added on Jan 22nd, 2018
                                plandetail["DJ5"] = productSale["DJ5"]; // LS added on Jan 22nd, 2018
                                plandetail["PgroupS"] = productSale["PgroupS"]; // LS added on Apr 5th, 2018
                                plandetail["Pgroup"] = productSale["Pgroup"]; // LS added on Apr 5th, 2018
                                plandetail["PgroupM"] = productSale["PgroupM"]; // LS added on Apr 5th, 2018
                                plandetail["PgroupR"] = productSale["PgroupR"]; // LS added on Apr 5th, 2018
                                plandetail["makebuy"] = productSale["huoqu"];
                                plandetail["orderqty"] = detail["qty"];

                                if(detail["makebuy"] + string.Empty != "采购")
                                {
                                    plandetail["recomqty"] = GetBumByVaribleLossRate(engine, detail["pcode"] + string.Empty, Convert.ToDecimal(detail["processqty"]) * packrate);
                                    plandetail["RD3"] = GetBumByVaribleLossRate(engine, detail["pcode"] + string.Empty, Convert.ToDecimal(detail["processqty"]) * packrate); //计划数量 γ1.6
                                }
                                else
                                {
                                    plandetail["recomqty"] = Convert.ToDecimal(detail["processqty"]) * packrate;
                                    plandetail["RD3"] = Convert.ToDecimal(detail["processqty"]) * packrate; //计划数量 γ1.6
                                }
                                plandetail["RE1"] = detail["RE1"]; //交货日期
                                if(detail["RE1"] + string.Empty != "")
                                {
                                    int Days = 0;
                                    H3.DataModel.BizObject[] SPsales = H3.DataModel.BizObject.GetList(engine, userId,
                                        engine.BizObjectManager.GetPublishedSchema("D000119IntialSetup"), H3.DataModel.GetListScopeType.GlobalAll,
                                        new H3.Data.Filter.Filter());
                                    if(SPsales != null && SPsales.Length > 0)
                                    {
                                        Days = -Convert.ToInt32(SPsales[0]["F0000126"]);
                                    }
                                    DateTime time = Convert.ToDateTime(detail["RE1"]);
                                    time = time.AddDays(Days);
                                    plandetail["startdate"] = time;//开工日期，系统默认提前7天
                                }
                                detailList.Add(plandetail);
                                H3.DataModel.BizObjectFileHeader[] headers = engine.BizObjectManager.CopyFiles("D000119salesorder", "D000119orderlist", "Attachment", detail.ObjectId, "D000119productionplan", "D000119plandetail", "Attachment", plandetail.ObjectId, false, false);
                            }
                        }
                    }
                }
            }
        }
        if(detailList != null && detailList.Count > 0)
        {
            productionplan["D000119plandetail"] = detailList.ToArray();
            string instanceId = System.Guid.NewGuid().ToString();
            if(string.IsNullOrEmpty(productionplan.WorkflowInstanceId))
            {
                productionplan.WorkflowInstanceId = instanceId;
            }
            H3.ErrorCode createResult = productionplan.Create();
            if(createResult == H3.ErrorCode.Success)
            {
                //启动流程
                string workItemID = string.Empty;
                string errorMsg = string.Empty;
                H3.Workflow.Template.WorkflowTemplate wfTemp = engine.WorkflowTemplateManager.GetDefaultWorkflow(productionplan.Schema.SchemaCode);
                engine.Interactor.OriginateInstance(userId, productionplan.Schema.SchemaCode,
                    wfTemp.WorkflowVersion, productionplan.ObjectId, productionplan.WorkflowInstanceId, H3.Workflow.WorkItem.AccessMethod.Web,
                    true, string.Empty, true, out workItemID, out  errorMsg);
            }

        }
    }
    public void CreateAssemble2nd(H3.IEngine engine, string userId, H3.DataModel.BizObject thisObj, bool flag) {
        H3.DataModel.BizObjectSchema  productionplanSchema = engine.BizObjectManager.GetPublishedSchema("D000119AssembleSheet");
        H3.DataModel.BizObject productionplan = new H3.DataModel.BizObject(engine, productionplanSchema, userId);
        productionplan["Employee"] = thisObj["Planner"]; //执行人
        productionplan["ProdType"] = "组装";
        productionplan["Remark"] = thisObj["ProdBase"]; //订单说明
        try //20191001
        {
            productionplan["deliverydate"] = thisObj["deliverydaterequired"];
        }
        catch(Exception e)
        { }
        productionplan["Reasons"] = thisObj["cnameshort"];
        productionplan["F0000120"] = thisObj["SeqNo"];
        try
        {
            productionplan["CoNo20200722"] = thisObj["CoNo20200722"];
        }
        catch(Exception e)
        {

        }
        List < H3.DataModel.BizObject > detailList = new List<H3.DataModel.BizObject>();
        Dictionary < string, string > objectIds = new Dictionary<string, string>();
        H3.DataModel.BizObject[] details = (H3.DataModel.BizObject[]) thisObj["D000119orderlist"];
        if(details != null && details.Length > 0)
        {
            foreach(H3.DataModel.BizObject detail in details)
            {
                decimal packrate = StockRelated.packrate(engine, Convert.ToString(detail["pcode"]), Convert.ToString(detail["unit"])); //******** 新增
                if(flag) //下推组装拆卸，直接生成
                {
                    if(detail["makebuy"] + string.Empty != "采购" && Convert.ToDecimal(detail["processqty"]) > 0)
                    {
                        string pcode = detail["pcode"] + string.Empty;
                        string unit = detail["unit"] + string.Empty;

                        H3.DataModel.BizObject tempObj = detailList.Find(x => x["Pcode"] + string.Empty == pcode);
                        if(tempObj != null)
                        {
                            tempObj["AssembleQty"] = Convert.ToDecimal(tempObj["AssembleQty"]) + Convert.ToDecimal(detail["qty"]) * packrate;
                        }
                        else
                        {
                            H3.DataModel.BizObject productSale = H3.DataModel.BizObject.Load(userId, engine, "D000119Product_sale", detail["pcode"] + string.Empty, false);
                            H3.DataModel.BizObject plandetail = new H3.DataModel.BizObject(engine, engine.BizObjectManager.GetPublishedSchema("D000119FGDetail"), userId);
                            try //20191001
                            {
                                plandetail["deliverydate"] = detail["RE1"];
                            }
                            catch(Exception e)
                            { }
                            plandetail["DetailRemark"] = detail["requirement"];
                            plandetail["Pcode"] = detail["pcode"];
                            plandetail["F0000132"] = detail["PcodeS"]; //辅助编码
                            plandetail["Pname"] = productSale["Proname"];
                            plandetail["Pspec"] = productSale["Prospec"];
                            plandetail["Unit"] = productSale["Unit"];
                            plandetail["DJ1"] = productSale["DJ1"]; // LS added on Jan 22nd, 2018
                            plandetail["DJ2"] = productSale["DJ2"]; // LS added on Jan 22nd, 2018
                            plandetail["DJ3"] = productSale["DJ3"]; // LS added on Jan 22nd, 2018
                            plandetail["DJ4"] = productSale["DJ4"]; // LS added on Jan 22nd, 2018
                            plandetail["DJ5"] = productSale["DJ5"]; // LS added on Jan 22nd, 2018
                            plandetail["AssembleQty"] = Convert.ToDecimal(detail["qty"]) * packrate;
                            detailList.Add(plandetail);
                            try //20191001
                            {
                                H3.DataModel.BizObjectFileHeader[] headers = engine.BizObjectManager.CopyFiles("D000119salesorder", "D000119orderlist", "Attachment", detail.ObjectId, "D000119AssembleSheet", "D000119FGDetail", "Attachment", plandetail.ObjectId, false, false);
                            }
                            catch(Exception e)
                            { }
                        }
                    }
                }
                else //下推系统选择，没有bom才生成组装拆卸，有bom就不生成
                {
                    H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();
                    filter.Matcher = new H3.Data.Filter.ItemMatcher("Pcode", H3.Data.ComparisonOperatorType.Equal, detail["pcode"] + string.Empty);
                    H3.DataModel.BizObject[] list = H3.DataModel.BizObject.GetList(engine, userId, engine.BizObjectManager.GetPublishedSchema("D000119MbomCreated"),
                        H3.DataModel.GetListScopeType.GlobalAll, filter);
                    if(list != null && list.Length > 0)
                    {
                    }
                    else
                    {
                        if(detail["makebuy"] + string.Empty != "采购" && Convert.ToDecimal(detail["processqty"]) > 0)
                        {
                            string pcode = detail["pcode"] + string.Empty;
                            H3.DataModel.BizObject tempObj = detailList.Find(x => x["Pcode"] + string.Empty == pcode);
                            if(tempObj != null)
                            {
                                tempObj["AssembleQty"] = Convert.ToDecimal(tempObj["AssembleQty"]) + Convert.ToDecimal(detail["qty"]) * packrate;
                            }
                            else
                            {
                                H3.DataModel.BizObject productSale = H3.DataModel.BizObject.Load(userId, engine, "D000119Product_sale", detail["pcode"] + string.Empty, false);
                                H3.DataModel.BizObject plandetail = new H3.DataModel.BizObject(engine, engine.BizObjectManager.GetPublishedSchema("D000119FGDetail"), userId);
                                try //20191001
                                {
                                    plandetail["deliverydate"] = detail["RE1"];
                                }
                                catch(Exception e)
                                { }
                                plandetail["DetailRemark"] = detail["requirement"];
                                plandetail["Pcode"] = detail["pcode"];
                                plandetail["F0000132"] = detail["PcodeS"]; //辅助编码
                                plandetail["Pname"] = productSale["Proname"];
                                plandetail["Pspec"] = productSale["Prospec"];
                                plandetail["Unit"] = productSale["Unit"];
                                plandetail["DJ1"] = productSale["DJ1"]; // LS added on Jan 22nd, 2018
                                plandetail["DJ2"] = productSale["DJ2"]; // LS added on Jan 22nd, 2018
                                plandetail["DJ3"] = productSale["DJ3"]; // LS added on Jan 22nd, 2018
                                plandetail["DJ4"] = productSale["DJ4"]; // LS added on Jan 22nd, 2018
                                plandetail["DJ5"] = productSale["DJ5"]; // LS added on Jan 22nd, 2018
                                plandetail["AssembleQty"] = Convert.ToDecimal(detail["qty"]) * packrate;
                                detailList.Add(plandetail);
                                try //20191001
                                {
                                    H3.DataModel.BizObjectFileHeader[] headers = engine.BizObjectManager.CopyFiles("D000119salesorder", "D000119orderlist", "Attachment", detail.ObjectId, "D000119AssembleSheet", "D000119FGDetail", "Attachment", plandetail.ObjectId, false, false);
                                }
                                catch(Exception e)
                                { }
                            }
                        }
                    }
                }
            }
        }
        if(detailList != null && detailList.Count > 0)
        {
            productionplan["D000119FGDetail"] = detailList.ToArray();
            string instanceId = System.Guid.NewGuid().ToString();
            if(string.IsNullOrEmpty(productionplan.WorkflowInstanceId))
            {
                productionplan.WorkflowInstanceId = instanceId;
            }
            H3.ErrorCode createResult = productionplan.Create();
            if(createResult == H3.ErrorCode.Success)
            {
                //启动流程
                string workItemID = string.Empty;
                string errorMsg = string.Empty;
                H3.Workflow.Template.WorkflowTemplate wfTemp = engine.WorkflowTemplateManager.GetDefaultWorkflow(productionplan.Schema.SchemaCode);
                engine.Interactor.OriginateInstance(userId, productionplan.Schema.SchemaCode,
                    wfTemp.WorkflowVersion, productionplan.ObjectId, productionplan.WorkflowInstanceId, H3.Workflow.WorkItem.AccessMethod.Web,
                    true, string.Empty, true, out workItemID, out  errorMsg);
            }
        }
    }
    public static void CreatePo2nd(H3.IEngine engine, string userid, string lineId, string pcode, decimal buyqty, string pofrom, string objId, DateTime maindate, DateTime detaildate, string coId, string cname, string cusseqno, string remark, string Orderfrom)
    {
        string targetId = "MPS";
        if(pofrom == "生产计划")
        {
            targetId = "MPS";
        }
        else if(pofrom == "销售订单")
        {
            targetId = "CO";
        }
        else if(pofrom == "采购申请")
        {
            targetId = "POapplyId";
        }
        else if(pofrom == "产品委外")
        {
            targetId = "WO";
        }
        string checkway = "";
        int Days = -15;
        string sqlsep = "select ifnull(F0000132,15) as leadtime,ifnull(F0000079,'全检') as checkway from i_D000119IntialSetup order by createdtime desc limit 1 ";
        System.Data.DataTable resultsep = engine.Query.QueryTable(sqlsep, null);
        if(resultsep != null && resultsep.Rows.Count > 0)
        {
            Days = -Convert.ToInt32(resultsep.Rows[0]["leadtime"]);
            checkway = Convert.ToString(resultsep.Rows[0]["checkway"]);
        }
        if(maindate != null && Days != 0)
        {
            maindate = maindate.AddDays(Days);
        }
        if(detaildate != null && Days != 0)
        {
            detaildate = detaildate.AddDays(Days);
        }
        H3.DataModel.BizObjectSchema poSchema = engine.BizObjectManager.GetPublishedSchema("D000119PO");
        string MyCompany = ""; //主要联系人
        string Myadd = ""; //电话
        string sqlsetup = "select mycompany, myadd from i_D000119OpenGuide order by createdtime desc";
        System.Data.DataTable resultsp = engine.Query.QueryTable(sqlsetup, null);
        if(resultsp != null && resultsp.Rows.Count > 0)
        {
            try
            {
                MyCompany = resultsp.Rows[0]["MyCompany"] + string.Empty; //2018/10/14 新增
                Myadd = resultsp.Rows[0]["Myadd"] + string.Empty; //2018/10/14 新增
            }
            catch(Exception e)
            {
            }
        }
        string pname = "";
        string pspec = "";
        string unit = "";
        string DJ1 = "";
        string DJ2 = "";
        string DJ3 = "";
        string DJ4 = "";
        string DJ5 = "";
        string sqlpd = "select proname,prospec,unit,DJ1,DJ2,DJ3,DJ4,DJ5 from i_D000119Product_sale where objectid = '" + pcode + "'";
        System.Data.DataTable resultpd = engine.Query.QueryTable(sqlpd, null);
        if(resultpd != null && resultpd.Rows.Count > 0)
        {
            pname = Convert.ToString(resultpd.Rows[0]["proname"]);
            pspec = Convert.ToString(resultpd.Rows[0]["prospec"]);
            unit = Convert.ToString(resultpd.Rows[0]["unit"]);
            DJ1 = Convert.ToString(resultpd.Rows[0]["DJ1"]);
            DJ2 = Convert.ToString(resultpd.Rows[0]["DJ2"]);
            DJ3 = Convert.ToString(resultpd.Rows[0]["DJ3"]);
            DJ4 = Convert.ToString(resultpd.Rows[0]["DJ4"]);
            DJ5 = Convert.ToString(resultpd.Rows[0]["DJ5"]);
        }
        decimal netprice = 0;
        decimal tax = 0;
        decimal vatprice = 0;

        string alloId = "";
        string sqla1 = "select objectid from i_D000119selectsupplier t1 where t1.pcode = '" + pcode + "' and t1.status = 1 order by t1.seqno desc limit 1 ";
        System.Data.DataTable resulta1 = engine.Query.QueryTable(sqla1, null);
        if(resulta1 != null && resulta1.Rows.Count > 0)
        {
            alloId = Convert.ToString(resulta1.Rows[0]["objectid"]);
        }
        string sqla2 = "select ifnull(t2.allocation,0) as allocation, t2.scode as scode from i_D000119mainsupplierlist t2 where t2.parentobjectid  = '" + alloId + "'";
        System.Data.DataTable resulta2 = engine.Query.QueryTable(sqla2, null);
        if(resulta2 != null && resulta2.Rows.Count > 0)
        {
            foreach(System.Data.DataRow row in resulta2.Rows)
            {
                string scode = row["scode"] + string.Empty;
                decimal allocation = Convert.ToDecimal(row["allocation"]);
                if(allocation <= 0)
                {
                    continue;
                }
                string Scodes = "";
                string InvoiceType = "";
                string pur_userid = userid;
                string snameshort = "";
                decimal rate = 1;
                string sname = "";
                string RT3 = "";
                string RT4 = "";
                string RT5 = "";
                string CurrencyS = "";
                if(scode != "")
                {
                    H3.DataModel.BizObject supplier = H3.DataModel.BizObject.Load(H3.Organization.User.SystemUserId, engine, "D000119supplier", scode, false);
                    snameshort = supplier["snameshort"] + string.Empty;
                    Scodes = supplier["scode"] + string.Empty;
                    try
                    {
                        InvoiceType = supplier["InvoiceType"] + string.Empty;
                    }
                    catch(Exception e)
                    { }
                    if(supplier["RP1"] + string.Empty != "")
                    {
                        pur_userid = supplier["RP1"] + string.Empty;
                    }
                    sname = supplier["sname"] + string.Empty; //供应商全称
                    RT3 = supplier["RT3"] + string.Empty; //联系人
                    RT4 = supplier["RT4"] + string.Empty; //电话
                    RT5 = supplier["RT5"] + string.Empty; //地址
                    CurrencyS = supplier["CurrencyS"] + string.Empty;
                }
                //找检验方式
                string sqlcw = "select ifnull(t2.checkway,'全检') as checkway from i_D000119AVLlist t2, i_D000119AVL t1 where t2.parentobjectid  = t1.objectid and t1.Scode='" + scode + "' and t2.pcode ='" + pcode + "'";
                System.Data.DataTable resultcw = engine.Query.QueryTable(sqlcw, null);
                if(resultcw != null && resultcw.Rows.Count > 0)
                {
                    checkway = Convert.ToString(resultcw.Rows[0]["checkway"]);
                }
                //最小包装量
                string sqlpoq = "select ExRate,SecondUnit from i_D000119ChangeUnit where pcode ='" + pcode + "' order by createdtime desc ";
                System.Data.DataTable resultpoq = engine.Query.QueryTable(sqlpoq, null);
                if(resultpoq != null && resultpoq.Rows.Count > 0)
                {
                    rate = Convert.ToDecimal(resultpoq.Rows[0]["ExRate"]);
                    unit = Convert.ToString(resultpoq.Rows[0]["SecondUnit"]);
                }
                string sqlp = "select netprice, tax, vatprice from i_D000119SPriceIndex where scode ='" + scode + "' and pcode ='" + pcode + "' and unit ='" + unit + "' and status=1 and Pstatus ='正常使用'order by createdtime desc";
                System.Data.DataTable resultp = engine.Query.QueryTable(sqlp, null);
                if(resultp != null && resultp.Rows.Count > 0)
                {
                    netprice = Convert.ToDecimal(resultp.Rows[0]["netprice"]);
                    tax = Convert.ToDecimal(resultp.Rows[0]["tax"]);
                    vatprice = Convert.ToDecimal(resultp.Rows[0]["vatprice"]);
                }
                //该计划和供应商已经有订单，那么加入子表
                string sqlpo = "select objectid from i_D000119PO where " + targetId + "='" + objId + "' and supplier='" + scode + "' and status!=1 order by createdtime desc ";
                System.Data.DataTable resultpo = engine.Query.QueryTable(sqlpo, null);
                if(resultpo != null && resultpo.Rows.Count > 0)
                {
                    bool flag = true;
                    string poId = Convert.ToString(resultpo.Rows[0]["objectid"]);
                    H3.DataModel.BizObject poObj = H3.DataModel.BizObject.Load(H3.Organization.User.SystemUserId, engine, "D000119PO", poId, false);
                    H3.DataModel.BizObject[] supplierDetails = (H3.DataModel.BizObject[]) poObj["D000119buydetail"];
                    List < H3.DataModel.BizObject > detailList = new List<H3.DataModel.BizObject>();
                    if(supplierDetails != null && supplierDetails.Length > 0)
                    {
                        detailList = new List<H3.DataModel.BizObject>(supplierDetails);
                        foreach(H3.DataModel.BizObject poDetail in detailList)
                        {
                            if(poDetail["pcode"] + string.Empty == pcode)
                            {
                                poDetail["ExchangeRate"] = Convert.ToDecimal(poDetail["ExchangeRate"]) + Math.Ceiling(buyqty * allocation);
                                poDetail["buyqty"] = Convert.ToDecimal(poDetail["buyqty"]) + Math.Ceiling(buyqty * allocation) / rate; //实际采购数量  20200415 rate是包装单位比率
                                poDetail["CheckWay"] = checkway;
                                poDetail["ETA"] = detaildate;
                                poDetail["pcode"] = pcode;
                                poDetail["netprice"] = netprice;
                                poDetail["Tax"] = tax;
                                poDetail["VATprice"] = vatprice;
                                poDetail["F0000023"] = vatprice; //为了实现采购价格低于报价价格走不同的审批流程 20180612
                                poDetail["netextend"] = Convert.ToDecimal(poDetail["netprice"]) * Convert.ToDecimal(poDetail["buyqty"]);
                                poDetail["VATextend"] = Convert.ToDecimal(poDetail["VATprice"]) * Convert.ToDecimal(poDetail["buyqty"]);
                                poDetail["pname"] = pname;
                                poDetail["pspec"] = pspec;
                                poDetail["unit"] = unit; //20200415
                                poDetail["DJ1"] = DJ1; //20200408
                                poDetail["DJ2"] = DJ2;
                                poDetail["DJ3"] = DJ3;
                                poDetail["DJ4"] = DJ4;
                                poDetail["DJ5"] = DJ5;
                                try //20210321 新增
                                {
                                    poDetail["UplineId1118"] = lineId;
                                }
                                catch(Exception e)
                                { }
                                //获取辅助编码
                                string sqlfuzhu = "select Addremarks from I_D000119SecondLevelMBOM where Pcode='" + pcode + "' and Huoqu='" + Scodes + "'";
                                System.Data.DataTable resultfuzhu = engine.Query.QueryTable(sqlfuzhu, null);
                                if(resultfuzhu != null && resultfuzhu.Rows.Count > 0)
                                {
                                    poDetail["pstatus"] = resultfuzhu.Rows[0]["Addremarks"];
                                }
                                flag = false;
                            }
                        }
                    }
                    if(supplierDetails == null || supplierDetails.Length == 0) //全空子表，订单分批=1
                    {
                        H3.DataModel.BizObject newDetail = new H3.DataModel.BizObject(engine, engine.BizObjectManager.GetPublishedSchema("D000119buydetail"), pur_userid);
                        newDetail["ExchangeRate"] = Math.Ceiling(buyqty * allocation);
                        newDetail["buyqty"] = Math.Ceiling(buyqty * allocation) / rate; //实际采购数量  20200415 rate是包装单位比率
                        newDetail["CheckWay"] = checkway;
                        newDetail["ETA"] = detaildate;
                        newDetail["pcode"] = pcode;
                        newDetail["netprice"] = netprice;
                        newDetail["Tax"] = tax;
                        newDetail["VATprice"] = vatprice;
                        newDetail["F0000023"] = vatprice; //为了实现采购价格低于报价价格走不同的审批流程 20180612
                        newDetail["netextend"] = Convert.ToDecimal(newDetail["netprice"]) * Convert.ToDecimal(newDetail["buyqty"]);
                        newDetail["VATextend"] = Convert.ToDecimal(newDetail["VATprice"]) * Convert.ToDecimal(newDetail["buyqty"]);
                        newDetail["pname"] = pname;
                        newDetail["pspec"] = pspec;
                        newDetail["unit"] = unit; //20200415
                        newDetail["DJ1"] = DJ1; //20200408
                        newDetail["DJ2"] = DJ2;
                        newDetail["DJ3"] = DJ3;
                        newDetail["DJ4"] = DJ4;
                        newDetail["DJ5"] = DJ5;
                        try //20210321 新增
                        {
                            newDetail["UplineId1118"] = lineId;
                        }
                        catch(Exception e)
                        { }
                        //获取辅助编码
                        string sqlfuzhu = "select Addremarks from I_D000119SecondLevelMBOM where Pcode='" + pcode + "' and Huoqu='" + Scodes + "'";
                        System.Data.DataTable resultfuzhu = engine.Query.QueryTable(sqlfuzhu, null);
                        if(resultfuzhu != null && resultfuzhu.Rows.Count > 0)
                        {
                            newDetail["pstatus"] = resultfuzhu.Rows[0]["Addremarks"];
                        }
                        detailList.Add(newDetail);
                    }
                    if(supplierDetails != null && supplierDetails.Length > 0 && flag) //已有供应商，但是这次报价的供应商不同，订单分批=0
                    {
                        H3.DataModel.BizObject newDetail = new H3.DataModel.BizObject(engine, engine.BizObjectManager.GetPublishedSchema("D000119buydetail"), pur_userid);
                        newDetail["ExchangeRate"] = Math.Ceiling(buyqty * allocation);
                        newDetail["buyqty"] = Math.Ceiling(buyqty * allocation) / rate; //实际采购数量  20200415 rate是包装单位比率
                        newDetail["CheckWay"] = checkway;
                        newDetail["ETA"] = detaildate;
                        newDetail["pcode"] = pcode;
                        newDetail["netprice"] = netprice;
                        newDetail["Tax"] = tax;
                        newDetail["VATprice"] = vatprice;
                        newDetail["F0000023"] = vatprice; //为了实现采购价格低于报价价格走不同的审批流程 20180612
                        newDetail["netextend"] = Convert.ToDecimal(newDetail["netprice"]) * Convert.ToDecimal(newDetail["buyqty"]);
                        newDetail["VATextend"] = Convert.ToDecimal(newDetail["VATprice"]) * Convert.ToDecimal(newDetail["buyqty"]);
                        newDetail["pname"] = pname;
                        newDetail["pspec"] = pspec;
                        newDetail["unit"] = unit; //20200415
                        newDetail["DJ1"] = DJ1; //20200408
                        newDetail["DJ2"] = DJ2;
                        newDetail["DJ3"] = DJ3;
                        newDetail["DJ4"] = DJ4;
                        newDetail["DJ5"] = DJ5;
                        try //20210321 新增
                        {
                            newDetail["UplineId1118"] = lineId;
                        }
                        catch(Exception e)
                        { }
                        //获取辅助编码
                        string sqlfuzhu = "select Addremarks from I_D000119SecondLevelMBOM where Pcode='" + pcode + "' and Huoqu='" + Scodes + "'";
                        System.Data.DataTable resultfuzhu = engine.Query.QueryTable(sqlfuzhu, null);
                        if(resultfuzhu != null && resultfuzhu.Rows.Count > 0)
                        {
                            newDetail["pstatus"] = resultfuzhu.Rows[0]["Addremarks"];
                        }
                        detailList.Add(newDetail);
                    }
                    poObj["D000119buydetail"] = detailList.ToArray();
                    decimal nettotal = 0;
                    decimal vattotal = 0;
                    H3.DataModel.BizObject[] upDetails = (H3.DataModel.BizObject[]) poObj["D000119buydetail"];
                    if(upDetails != null && upDetails.Length > 0)
                    {
                        foreach(H3.DataModel.BizObject poDetail in upDetails)
                        {
                            nettotal += Convert.ToDecimal(poDetail["netextend"]);
                            vattotal += Convert.ToDecimal(poDetail["VATextend"]);
                        }
                    }
                    poObj["nettotal"] = nettotal;
                    poObj["VATtotal"] = vattotal;
                    poObj["TaxAmount"] = Convert.ToDecimal(poObj["VATtotal"]) - Convert.ToDecimal(poObj["nettotal"]);
                    poObj.Update();
                }
                else
                {
                    List < H3.DataModel.BizObject > detailList = new List<H3.DataModel.BizObject>();
                    H3.DataModel.BizObject poObj = new H3.DataModel.BizObject(engine, engine.BizObjectManager.GetPublishedSchema("D000119PO"), pur_userid);
                    H3.DataModel.BizObject newDetail = new H3.DataModel.BizObject(engine, engine.BizObjectManager.GetPublishedSchema("D000119buydetail"), pur_userid);
                    newDetail["ExchangeRate"] = Math.Ceiling(buyqty * allocation);
                    newDetail["buyqty"] = Math.Ceiling(buyqty * allocation) / rate; //实际采购数量  20200415 rate是包装单位比率
                    newDetail["CheckWay"] = checkway;
                    newDetail["ETA"] = detaildate;
                    newDetail["pcode"] = pcode;
                    newDetail["netprice"] = netprice;
                    newDetail["Tax"] = tax;
                    newDetail["VATprice"] = vatprice;
                    newDetail["F0000023"] = vatprice; //为了实现采购价格低于报价价格走不同的审批流程 20180612
                    newDetail["netextend"] = Convert.ToDecimal(newDetail["netprice"]) * Convert.ToDecimal(newDetail["buyqty"]);
                    newDetail["VATextend"] = Convert.ToDecimal(newDetail["VATprice"]) * Convert.ToDecimal(newDetail["buyqty"]);
                    newDetail["pname"] = pname;
                    newDetail["pspec"] = pspec;
                    newDetail["unit"] = unit; //20200415
                    newDetail["DJ1"] = DJ1; //20200408
                    newDetail["DJ2"] = DJ2;
                    newDetail["DJ3"] = DJ3;
                    newDetail["DJ4"] = DJ4;
                    newDetail["DJ5"] = DJ5;
                    try //20210321 新增
                    {
                        newDetail["UplineId1118"] = lineId;
                    }
                    catch(Exception e)
                    { }
                    //获取辅助编码
                    string sqlfuzhu = "select Addremarks from I_D000119SecondLevelMBOM where Pcode='" + pcode + "' and Huoqu='" + Scodes + "'";
                    System.Data.DataTable resultfuzhu = engine.Query.QueryTable(sqlfuzhu, null);
                    if(resultfuzhu != null && resultfuzhu.Rows.Count > 0)
                    {
                        newDetail["pstatus"] = resultfuzhu.Rows[0]["Addremarks"];
                    }
                    detailList.Add(newDetail);
                    poObj["D000119buydetail"] = detailList.ToArray();
                    poObj["snameshort"] = snameshort;
                    poObj["deliverydate"] = maindate;
                    poObj["ReturnDate"] = maindate;
                    poObj["CO"] = coId;
                    if(pofrom == "生产计划")
                    {
                        poObj["MPS"] = objId;
                    }
                    else if(pofrom == "采购申请")
                    {
                        poObj["POapplyId"] = objId;
                    }
                    else if(pofrom == "产品委外")
                    {
                        poObj["WO"] = objId;
                    }
                    try
                    {
                        poObj["CoNo20200722"] = cusseqno;
                    }
                    catch(Exception e)
                    {
                    }
                    poObj["Cname"] = cname;
                    poObj["Reason"] = remark; //20200309 订单说明
                    poObj["F0000021"] = pofrom;
                    poObj["postatus"] = "订单进行：待采购";
                    poObj["RT5"] = "正常";
                    poObj["check0sum"] = 0; //LS 20180715 走不同的审批流程，预先赋值
                    poObj["POapplied"] = pur_userid;
                    poObj["supplier"] = scode;
                    poObj["ProdBase"] = InvoiceType;//发票类型
                    poObj["PoStatusCal"] = sname; //供应商全称
                    poObj["InvoiceNo"] = RT3; //联系人
                    poObj["RT4"] = RT4; //电话
                    poObj["RT5"] = RT5; //地址
                    poObj["Payment"] = MyCompany; // LS 20200530
                    poObj["Myadd"] = Myadd; // LS 20200530
                    poObj["currency"] = CurrencyS;
                    poObj["OwnerId"] = pur_userid;
                    poObj["RT3"] = Orderfrom; //20191211 有效库存问题

                    poObj["nettotal"] = newDetail["netextend"];
                    poObj["VATtotal"] = newDetail["VATextend"];
                    poObj["TaxAmount"] = Convert.ToDecimal(poObj["VATtotal"]) - Convert.ToDecimal(poObj["nettotal"]);
                    string instanceId = System.Guid.NewGuid().ToString();
                    if(string.IsNullOrEmpty(poObj.WorkflowInstanceId))
                    {
                        poObj.WorkflowInstanceId = instanceId;
                    }
                    H3.ErrorCode createResult = poObj.Create();
                    if(createResult == H3.ErrorCode.Success)
                    {
                        //启动流程
                        string workItemID = string.Empty;
                        string errorMsg = string.Empty;
                        H3.Workflow.Template.WorkflowTemplate wfTemp = engine.WorkflowTemplateManager.GetDefaultWorkflow(poObj.Schema.SchemaCode);
                        engine.Interactor.OriginateInstance(pur_userid, poObj.Schema.SchemaCode,
                            wfTemp.WorkflowVersion, poObj.ObjectId, poObj.WorkflowInstanceId, H3.Workflow.WorkItem.AccessMethod.Web,
                            true, string.Empty, true, out workItemID, out  errorMsg);
                    }
                }
            }
        }
        else //没有订单分配
        {
            //最小包装量
            decimal rate = 1;
            string sqlpoq = "select ExRate,SecondUnit from i_D000119ChangeUnit where pcode ='" + pcode + "' order by createdtime desc ";
            System.Data.DataTable resultpoq = engine.Query.QueryTable(sqlpoq, null);
            if(resultpoq != null && resultpoq.Rows.Count > 0)
            {
                rate = Convert.ToDecimal(resultpoq.Rows[0]["ExRate"]);
                unit = Convert.ToString(resultpoq.Rows[0]["SecondUnit"]);
            }
            string scode = "";
            decimal allocation = 1;
            string sqlp = "select netprice, tax, vatprice, scode from i_D000119SPriceIndex where pcode ='" + pcode + "' and unit ='" + unit + "' and status=1 and Pstatus ='正常使用'order by createdtime desc";
            System.Data.DataTable resultp = engine.Query.QueryTable(sqlp, null);
            if(resultp != null && resultp.Rows.Count > 0)
            {
                netprice = Convert.ToDecimal(resultp.Rows[0]["netprice"]);
                tax = Convert.ToDecimal(resultp.Rows[0]["tax"]);
                vatprice = Convert.ToDecimal(resultp.Rows[0]["vatprice"]);
                scode = Convert.ToString(resultp.Rows[0]["scode"]);
                //找检验方式
                string sqlcw = "select ifnull(t2.checkway,'全检') as checkway from i_D000119AVLlist t2, i_D000119AVL t1 where t2.parentobjectid  = t1.objectid and t1.Scode='" + scode + "' and t2.pcode ='" + pcode + "'";
                System.Data.DataTable resultcw = engine.Query.QueryTable(sqlcw, null);
                if(resultcw != null && resultcw.Rows.Count > 0)
                {
                    checkway = Convert.ToString(resultcw.Rows[0]["checkway"]);
                }
                H3.DataModel.BizObject supplier = H3.DataModel.BizObject.Load(H3.Organization.User.SystemUserId, engine, "D000119supplier", scode, false);
                string Scodes = supplier["scode"] + string.Empty;
                string InvoiceType = "";
                try
                {
                    InvoiceType = supplier["InvoiceType"] + string.Empty;
                }
                catch(Exception e)
                { }
                string pur_userid = userid;
                if(supplier["RP1"] + string.Empty != "")
                {
                    pur_userid = supplier["RP1"] + string.Empty;
                }
                string snameshort = supplier["snameshort"] + string.Empty;
                //该计划和供应商已经有订单，那么加入子表
                string sqlpo = "select objectid from i_D000119PO where " + targetId + "='" + objId + "' and supplier='" + scode + "' and status!=1 order by createdtime desc ";
                System.Data.DataTable resultpo = engine.Query.QueryTable(sqlpo, null);
                if(resultpo != null && resultpo.Rows.Count > 0)
                {
                    bool flag = true;
                    string poId = Convert.ToString(resultpo.Rows[0]["objectid"]);
                    H3.DataModel.BizObject poObj = H3.DataModel.BizObject.Load(H3.Organization.User.SystemUserId, engine, "D000119PO", poId, false);
                    H3.DataModel.BizObject[] supplierDetails = (H3.DataModel.BizObject[]) poObj["D000119buydetail"];
                    List < H3.DataModel.BizObject > detailList = new List<H3.DataModel.BizObject>();
                    if(supplierDetails != null && supplierDetails.Length > 0)
                    {
                        detailList = new List<H3.DataModel.BizObject>(supplierDetails);
                        foreach(H3.DataModel.BizObject poDetail in detailList)
                        {
                            if(poDetail["pcode"] + string.Empty == pcode)
                            {
                                poDetail["ExchangeRate"] = Convert.ToDecimal(poDetail["ExchangeRate"]) + Math.Ceiling(buyqty * allocation);
                                poDetail["buyqty"] = Convert.ToDecimal(poDetail["buyqty"]) + Math.Ceiling(buyqty * allocation) / rate; //实际采购数量  20200415 rate是包装单位比率
                                poDetail["CheckWay"] = checkway;
                                poDetail["ETA"] = detaildate;
                                poDetail["pcode"] = pcode;
                                poDetail["netprice"] = netprice;
                                poDetail["Tax"] = tax;
                                poDetail["VATprice"] = vatprice;
                                poDetail["F0000023"] = vatprice; //为了实现采购价格低于报价价格走不同的审批流程 20180612
                                poDetail["netextend"] = Convert.ToDecimal(poDetail["netprice"]) * Convert.ToDecimal(poDetail["buyqty"]);
                                poDetail["VATextend"] = Convert.ToDecimal(poDetail["VATprice"]) * Convert.ToDecimal(poDetail["buyqty"]);
                                poDetail["pname"] = pname;
                                poDetail["pspec"] = pspec;
                                poDetail["unit"] = unit; //20200415
                                poDetail["DJ1"] = DJ1; //20200408
                                poDetail["DJ2"] = DJ2;
                                poDetail["DJ3"] = DJ3;
                                poDetail["DJ4"] = DJ4;
                                poDetail["DJ5"] = DJ5;
                                try //20210321 新增
                                {
                                    poDetail["UplineId1118"] = lineId;
                                }
                                catch(Exception e)
                                { }
                                //获取辅助编码
                                string sqlfuzhu = "select Addremarks from I_D000119SecondLevelMBOM where Pcode='" + pcode + "' and Huoqu='" + Scodes + "'";
                                System.Data.DataTable resultfuzhu = engine.Query.QueryTable(sqlfuzhu, null);
                                if(resultfuzhu != null && resultfuzhu.Rows.Count > 0)
                                {
                                    poDetail["pstatus"] = resultfuzhu.Rows[0]["Addremarks"];
                                }
                                flag = false;
                            }
                        }
                    }
                    if(supplierDetails == null || supplierDetails.Length == 0) //全空子表，订单分批=1
                    {
                        H3.DataModel.BizObject newDetail = new H3.DataModel.BizObject(engine, engine.BizObjectManager.GetPublishedSchema("D000119buydetail"), pur_userid);
                        newDetail["ExchangeRate"] = Math.Ceiling(buyqty * allocation);
                        newDetail["buyqty"] = Math.Ceiling(buyqty * allocation) / rate; //实际采购数量  20200415 rate是包装单位比率
                        newDetail["CheckWay"] = checkway;
                        newDetail["ETA"] = detaildate;
                        newDetail["pcode"] = pcode;
                        newDetail["netprice"] = netprice;
                        newDetail["Tax"] = tax;
                        newDetail["VATprice"] = vatprice;
                        newDetail["F0000023"] = vatprice; //为了实现采购价格低于报价价格走不同的审批流程 20180612
                        newDetail["netextend"] = Convert.ToDecimal(newDetail["netprice"]) * Convert.ToDecimal(newDetail["buyqty"]);
                        newDetail["VATextend"] = Convert.ToDecimal(newDetail["VATprice"]) * Convert.ToDecimal(newDetail["buyqty"]);
                        newDetail["pname"] = pname;
                        newDetail["pspec"] = pspec;
                        newDetail["unit"] = unit; //20200415
                        newDetail["DJ1"] = DJ1; //20200408
                        newDetail["DJ2"] = DJ2;
                        newDetail["DJ3"] = DJ3;
                        newDetail["DJ4"] = DJ4;
                        newDetail["DJ5"] = DJ5;
                        try //20210321 新增
                        {
                            newDetail["UplineId1118"] = lineId;
                        }
                        catch(Exception e)
                        { }
                        //获取辅助编码
                        string sqlfuzhu = "select Addremarks from I_D000119SecondLevelMBOM where Pcode='" + pcode + "' and Huoqu='" + Scodes + "'";
                        System.Data.DataTable resultfuzhu = engine.Query.QueryTable(sqlfuzhu, null);
                        if(resultfuzhu != null && resultfuzhu.Rows.Count > 0)
                        {
                            newDetail["pstatus"] = resultfuzhu.Rows[0]["Addremarks"];
                        }
                        detailList.Add(newDetail);
                    }
                    if(supplierDetails != null && supplierDetails.Length > 0 && flag) //已有供应商，但是这次报价的供应商不同，订单分批=0
                    {
                        H3.DataModel.BizObject newDetail = new H3.DataModel.BizObject(engine, engine.BizObjectManager.GetPublishedSchema("D000119buydetail"), pur_userid);
                        newDetail["ExchangeRate"] = Math.Ceiling(buyqty * allocation);
                        newDetail["buyqty"] = Math.Ceiling(buyqty * allocation) / rate; //实际采购数量  20200415 rate是包装单位比率
                        newDetail["CheckWay"] = checkway;
                        newDetail["ETA"] = detaildate;
                        newDetail["pcode"] = pcode;
                        newDetail["netprice"] = netprice;
                        newDetail["Tax"] = tax;
                        newDetail["VATprice"] = vatprice;
                        newDetail["F0000023"] = vatprice; //为了实现采购价格低于报价价格走不同的审批流程 20180612
                        newDetail["netextend"] = Convert.ToDecimal(newDetail["netprice"]) * Convert.ToDecimal(newDetail["buyqty"]);
                        newDetail["VATextend"] = Convert.ToDecimal(newDetail["VATprice"]) * Convert.ToDecimal(newDetail["buyqty"]);
                        newDetail["pname"] = pname;
                        newDetail["pspec"] = pspec;
                        newDetail["unit"] = unit; //20200415
                        newDetail["DJ1"] = DJ1; //20200408
                        newDetail["DJ2"] = DJ2;
                        newDetail["DJ3"] = DJ3;
                        newDetail["DJ4"] = DJ4;
                        newDetail["DJ5"] = DJ5;
                        try //20210321 新增
                        {
                            newDetail["UplineId1118"] = lineId;
                        }
                        catch(Exception e)
                        { }
                        //获取辅助编码
                        string sqlfuzhu = "select Addremarks from I_D000119SecondLevelMBOM where Pcode='" + pcode + "' and Huoqu='" + Scodes + "'";
                        System.Data.DataTable resultfuzhu = engine.Query.QueryTable(sqlfuzhu, null);
                        if(resultfuzhu != null && resultfuzhu.Rows.Count > 0)
                        {
                            newDetail["pstatus"] = resultfuzhu.Rows[0]["Addremarks"];
                        }
                        detailList.Add(newDetail);
                    }
                    poObj["D000119buydetail"] = detailList.ToArray();
                    decimal nettotal = 0;
                    decimal vattotal = 0;
                    H3.DataModel.BizObject[] upDetails = (H3.DataModel.BizObject[]) poObj["D000119buydetail"];
                    if(upDetails != null && upDetails.Length > 0)
                    {
                        foreach(H3.DataModel.BizObject poDetail in upDetails)
                        {
                            nettotal += Convert.ToDecimal(poDetail["netextend"]);
                            vattotal += Convert.ToDecimal(poDetail["VATextend"]);
                        }
                    }
                    poObj["nettotal"] = nettotal;
                    poObj["VATtotal"] = vattotal;
                    poObj["TaxAmount"] = Convert.ToDecimal(poObj["VATtotal"]) - Convert.ToDecimal(poObj["nettotal"]);
                    poObj.Update();
                }
                else
                {
                    H3.DataModel.BizObject poObj = new H3.DataModel.BizObject(engine, engine.BizObjectManager.GetPublishedSchema("D000119PO"), pur_userid);
                    List < H3.DataModel.BizObject > detailList = new List<H3.DataModel.BizObject>();
                    H3.DataModel.BizObject newDetail = new H3.DataModel.BizObject(engine, engine.BizObjectManager.GetPublishedSchema("D000119buydetail"), pur_userid);
                    newDetail["ExchangeRate"] = Math.Ceiling(buyqty * allocation);
                    newDetail["buyqty"] = Math.Ceiling(buyqty * allocation) / rate; //实际采购数量  20200415 rate是包装单位比率
                    newDetail["CheckWay"] = checkway;
                    newDetail["ETA"] = detaildate;
                    newDetail["pcode"] = pcode;
                    newDetail["netprice"] = netprice;
                    newDetail["Tax"] = tax;
                    newDetail["VATprice"] = vatprice;
                    newDetail["F0000023"] = vatprice; //为了实现采购价格低于报价价格走不同的审批流程 20180612
                    newDetail["netextend"] = Convert.ToDecimal(newDetail["netprice"]) * Convert.ToDecimal(newDetail["buyqty"]);
                    newDetail["VATextend"] = Convert.ToDecimal(newDetail["VATprice"]) * Convert.ToDecimal(newDetail["buyqty"]);
                    newDetail["pname"] = pname;
                    newDetail["pspec"] = pspec;
                    newDetail["unit"] = unit; //20200415
                    newDetail["DJ1"] = DJ1; //20200408
                    newDetail["DJ2"] = DJ2;
                    newDetail["DJ3"] = DJ3;
                    newDetail["DJ4"] = DJ4;
                    newDetail["DJ5"] = DJ5;
                    try //20210321 新增
                    {
                        newDetail["UplineId1118"] = lineId;
                    }
                    catch(Exception e)
                    { }
                    //获取辅助编码
                    string sqlfuzhu = "select Addremarks from I_D000119SecondLevelMBOM where Pcode='" + pcode + "' and Huoqu='" + Scodes + "'";
                    System.Data.DataTable resultfuzhu = engine.Query.QueryTable(sqlfuzhu, null);
                    if(resultfuzhu != null && resultfuzhu.Rows.Count > 0)
                    {
                        newDetail["pstatus"] = resultfuzhu.Rows[0]["Addremarks"];
                    }
                    detailList.Add(newDetail);
                    poObj["D000119buydetail"] = detailList.ToArray();
                    poObj["snameshort"] = supplier["snameshort"];
                    poObj["deliverydate"] = maindate;
                    poObj["ReturnDate"] = maindate;

                    poObj["CO"] = coId;
                    if(pofrom == "生产计划")
                    {
                        poObj["MPS"] = objId;
                    }
                    else if(pofrom == "采购申请")
                    {
                        poObj["POapplyId"] = objId;
                    }
                    else if(pofrom == "产品委外")
                    {
                        poObj["WO"] = objId;
                    }
                    try
                    {
                        poObj["CoNo20200722"] = cusseqno;
                    }
                    catch(Exception e)
                    {
                    }
                    poObj["Cname"] = cname;
                    poObj["Reason"] = remark; //20200309 订单说明
                    poObj["F0000021"] = pofrom;
                    poObj["postatus"] = "订单进行：待采购";
                    poObj["RT5"] = "正常";
                    poObj["check0sum"] = 0; //LS 20180715 走不同的审批流程，预先赋值
                    poObj["POapplied"] = pur_userid;
                    poObj["supplier"] = scode;
                    poObj["ProdBase"] = InvoiceType;//发票类型
                    poObj["PoStatusCal"] = supplier["sname"]; //供应商全称
                    poObj["InvoiceNo"] = supplier["RT3"]; //联系人
                    poObj["RT4"] = supplier["RT4"]; //电话
                    poObj["RT5"] = supplier["RT5"]; //地址
                    poObj["Payment"] = MyCompany; // LS 20200530
                    poObj["Myadd"] = Myadd; // LS 20200530
                    poObj["currency"] = supplier["CurrencyS"];
                    poObj["OwnerId"] = pur_userid;
                    poObj["RT3"] = Orderfrom; //20191211 有效库存问题
                    poObj["nettotal"] = newDetail["netextend"];
                    poObj["VATtotal"] = newDetail["VATextend"];
                    poObj["TaxAmount"] = Convert.ToDecimal(poObj["VATtotal"]) - Convert.ToDecimal(poObj["nettotal"]);
                    string instanceId = System.Guid.NewGuid().ToString();
                    if(string.IsNullOrEmpty(poObj.WorkflowInstanceId))
                    {
                        poObj.WorkflowInstanceId = instanceId;
                    }
                    H3.ErrorCode createResult = poObj.Create();
                    if(createResult == H3.ErrorCode.Success)
                    {
                        //启动流程
                        string workItemID = string.Empty;
                        string errorMsg = string.Empty;
                        H3.Workflow.Template.WorkflowTemplate wfTemp = engine.WorkflowTemplateManager.GetDefaultWorkflow(poObj.Schema.SchemaCode);
                        engine.Interactor.OriginateInstance(pur_userid, poObj.Schema.SchemaCode,
                            wfTemp.WorkflowVersion, poObj.ObjectId, poObj.WorkflowInstanceId, H3.Workflow.WorkItem.AccessMethod.Web,
                            true, string.Empty, true, out workItemID, out  errorMsg);
                    }
                }
            }
            else //产品没有报价
            {
                H3.DataModel.BizObject productObj = H3.DataModel.BizObject.Load(H3.Organization.User.SystemUserId, engine, "D000119Product_sale", pcode, false);
                netprice = 0;
                tax = 0.13m;
                vatprice = 0;
                string pur_userid = userid;
                H3.DataModel.BizObject poObj = new H3.DataModel.BizObject(engine, engine.BizObjectManager.GetPublishedSchema("D000119PO"), pur_userid);
                List < H3.DataModel.BizObject > detailList = new List<H3.DataModel.BizObject>();
                H3.DataModel.BizObject newDetail = new H3.DataModel.BizObject(engine, engine.BizObjectManager.GetPublishedSchema("D000119buydetail"), pur_userid);
                newDetail["ExchangeRate"] = Math.Ceiling(buyqty * allocation);
                newDetail["buyqty"] = Math.Ceiling(buyqty * allocation) / rate; //实际采购数量  20200415 rate是包装单位比率
                newDetail["CheckWay"] = checkway;
                newDetail["ETA"] = detaildate;
                newDetail["pcode"] = pcode;
                newDetail["netprice"] = netprice;
                newDetail["Tax"] = tax;
                newDetail["VATprice"] = vatprice;
                newDetail["F0000023"] = vatprice; //为了实现采购价格低于报价价格走不同的审批流程 20180612
                newDetail["netextend"] = Convert.ToDecimal(newDetail["netprice"]) * Convert.ToDecimal(newDetail["buyqty"]);
                newDetail["VATextend"] = Convert.ToDecimal(newDetail["VATprice"]) * Convert.ToDecimal(newDetail["buyqty"]);
                newDetail["pname"] = pname;
                newDetail["pspec"] = pspec;
                newDetail["unit"] = unit; //20200415
                newDetail["DJ1"] = DJ1; //20200408
                newDetail["DJ2"] = DJ2;
                newDetail["DJ3"] = DJ3;
                newDetail["DJ4"] = DJ4;
                newDetail["DJ5"] = DJ5;
                try //20210321 新增
                {
                    newDetail["UplineId1118"] = lineId;
                }
                catch(Exception e)
                { }
                detailList.Add(newDetail);
                poObj["D000119buydetail"] = detailList.ToArray();
                poObj["snameshort"] = "";
                poObj["deliverydate"] = maindate;
                poObj["ReturnDate"] = maindate;

                poObj["CO"] = coId;
                if(pofrom == "生产计划")
                {
                    poObj["MPS"] = objId;
                }
                else if(pofrom == "采购申请")
                {
                    poObj["POapplyId"] = objId;
                }
                else if(pofrom == "产品委外")
                {
                    poObj["WO"] = objId;
                }
                try
                {
                    poObj["CoNo20200722"] = cusseqno;
                }
                catch(Exception e)
                {
                }
                poObj["Cname"] = cname;
                poObj["Reason"] = remark; //20200309 订单说明
                poObj["F0000021"] = pofrom;
                poObj["postatus"] = "订单进行：待采购";
                poObj["RT5"] = "正常";
                poObj["check0sum"] = 0; //LS 20180715 走不同的审批流程，预先赋值
                poObj["POapplied"] = pur_userid;
                poObj["supplier"] = scode;
                poObj["ProdBase"] = "";//发票类型
                poObj["PoStatusCal"] = ""; //供应商全称
                poObj["InvoiceNo"] = ""; //联系人
                poObj["RT4"] = ""; //电话
                poObj["RT5"] = ""; //地址
                poObj["Payment"] = MyCompany; // LS 20200530
                poObj["Myadd"] = Myadd; // LS 20200530
                poObj["currency"] = "";
                poObj["OwnerId"] = pur_userid;
                poObj["RT3"] = Orderfrom; //20191211 有效库存问题

                poObj["nettotal"] = Convert.ToDecimal(poObj["nettotal"]) + Convert.ToDecimal(newDetail["netextend"]);
                poObj["VATtotal"] = Convert.ToDecimal(poObj["VATtotal"]) + Convert.ToDecimal(newDetail["VATextend"]);
                poObj["TaxAmount"] = Convert.ToDecimal(poObj["VATtotal"]) - Convert.ToDecimal(poObj["nettotal"]);
                string instanceId = System.Guid.NewGuid().ToString();
                if(string.IsNullOrEmpty(poObj.WorkflowInstanceId))
                {
                    poObj.WorkflowInstanceId = instanceId;
                }
                H3.ErrorCode createResult = poObj.Create();
                if(createResult == H3.ErrorCode.Success)
                {
                    //启动流程
                    string workItemID = string.Empty;
                    string errorMsg = string.Empty;
                    H3.Workflow.Template.WorkflowTemplate wfTemp = engine.WorkflowTemplateManager.GetDefaultWorkflow(poObj.Schema.SchemaCode);
                    engine.Interactor.OriginateInstance(pur_userid, poObj.Schema.SchemaCode,
                        wfTemp.WorkflowVersion, poObj.ObjectId, poObj.WorkflowInstanceId, H3.Workflow.WorkItem.AccessMethod.Web,
                        true, string.Empty, true, out workItemID, out  errorMsg);
                }
            }
        }
    }
    public static decimal GetBumByVaribleLossRate(H3.IEngine engine, string productId, decimal num) { //20201101 改为从单独变动损耗率表单取值
        decimal rate = 0;
        H3.Data.Filter.Filter filter = new H3.Data.Filter.Filter();
        H3.Data.Filter.And and = new H3.Data.Filter.And();
        and.Add(new H3.Data.Filter.ItemMatcher("Pcode", H3.Data.ComparisonOperatorType.Equal, productId));
        and.Add(new H3.Data.Filter.ItemMatcher("recomqtyFROM", H3.Data.ComparisonOperatorType.NotAbove, num));
        and.Add(new H3.Data.Filter.ItemMatcher("recomqtyTO", H3.Data.ComparisonOperatorType.NotBelow, num));
        filter.Matcher = and;
        H3.DataModel.BizObjectSchema schema = engine.BizObjectManager.GetPublishedSchema("D000119Vlossrate");
        H3.DataModel.BizObject[] details = H3.DataModel.BizObject.GetList(engine, H3.Organization.User.SystemUserId, schema,
            H3.DataModel.GetListScopeType.GlobalAll, filter);

        if(details != null && details.Length > 0 && !string.IsNullOrEmpty(details[0]["VaribleLossRate"] + string.Empty))
        {
            rate = Convert.ToDecimal(details[0]["VaribleLossRate"]);
        }
        num = Math.Round(num / (1 - rate), 4);
        return num;
    }
}

