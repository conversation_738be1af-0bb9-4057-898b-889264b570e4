cd C:/Users/<USER>/IdeaProjects/study-pc

git checkout master
git pull

@rem npm package, build mode app.
call npm install
call npm run build -- --mode pc

@rem win rar zip,-r is all sub dir, a is compress
@rem winrar a -r  study_app.zip ./dist

7z a study_pc.zip ./dist/*

@rem 开启ssh 隧道
ssh -CNfg -L 5550:**************:22 root@*************

@rem scp zip to server
scp -P 5550  ./study_pc.zip root@127.0.0.1:/usr/share/nginx/html

echo "ssh nginx dir to unzip study, and rm zip"
ssh root@127.0.0.1 -p 5550 "cd /usr/share/nginx/html;rm -rf ./study_pc;unzip -d study_pc  study_pc.zip;rm -rf ./study_pc.zip"

echo "del local study zip"
del /f /q "./study_*.zip"


for /f "tokens=2" %%i in ('tasklist^|findstr ssh') do (
    echo "ssh 隧道PID:" %%i
    taskkill /F /PID %%i
)

pause
