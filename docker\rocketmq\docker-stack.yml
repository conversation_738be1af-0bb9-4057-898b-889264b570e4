version: '3.7'

services:
  namesrv1:
    image: apache/rocketmq:latest
    ports:
      - 9876:9876
    volumes:
      - /srv/rocketmq/namesrv:/opt
    deploy:
      placement:
        constraints:
          - node.hostname == dev-200
    environment:
      JAVA_OPT_EXT: "-Duser.home=/opt -Xms512M -Xmx512M -Xmn128m"
    command: ["sh","mqnamesrv"]
  namesrv2:
    image: apache/rocketmq:latest
    ports:
      - 9877:9876
    volumes:
      - /srv/rocketmq/namesrv:/opt
    deploy:
      placement:
        constraints:
          - node.hostname == dev-204
    environment:
      JAVA_OPT_EXT: "-Duser.home=/opt -Xms512M -Xmx512M -Xmn128m"
    command: ["sh","mqnamesrv"]


  broker1:
    image: apache/rocketmq:latest
    ports:
      - 10911:10911
      - 10909:10909
    volumes:
      - /srv/rocketmq/broker:/opt
    deploy:
      placement:
        constraints:
          - node.hostname == dev-200
    environment:
        JAVA_OPT_EXT: "-Duser.home=/opt -Xms512M -Xmx512M -Xmn128m"
    command: ["sh","mqbroker","-c","/opt/broker.conf","autoCreateTopicEnable=true"]
    depends_on:
      - namesrv1
      - namesrv2

  broker2:
    image: apache/rocketmq:latest
    ports:
      - 11911:11911
      - 11909:11909
    volumes:
      - /srv/rocketmq/broker:/opt
    deploy:
      placement:
        constraints:
          - node.hostname == dev-204
    environment:
        JAVA_OPT_EXT: "-Duser.home=/opt -Xms512M -Xmx512M -Xmn128m"
    command: ["sh","mqbroker","-c","/opt/broker.conf","autoCreateTopicEnable=true"]
    depends_on:
      - namesrv1
      - namesrv2

  rmqconsole:
    image: hub.91cloudpay.com:4001/library/rocketmq-console-ng
    ports:
      - 8180:8080
    volumes:
      - console_data:/tmp
    environment:
        JAVA_OPTS: "-Drocketmq.namesrv.addr=*************:9876;*************:9877 -Dcom.rocketmq.sendMessageWithVIPChannel=false -Dspring.config.location=/tmp/application.properties"
    depends_on:
      - namesrv1
      - namesrv2

volumes:
  console_data:
    driver_opts:
      device: "${CONSOLE_DATA_PATH:-:/DataVolume/rocketmq/console}"
      o: "addr=*************,vers=4,rw"
      type: "nfs4"