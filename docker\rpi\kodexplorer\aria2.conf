dir=/downloads
disable-ipv6=true

# 定时保存会话, 0为退出时才保存, 需1.16.1以上版本, 默认:0
save-session-interval=60

force-save=true
bt-hash-check-seed=true
bt-seed-unverified=true
bt-save-metadata=true

# Specify a port number for JSON-RPC/XML-RPC server to listen to. Possible Values: 1024 -65535 Default: 6800
rpc-listen-port=6800

#Listen incoming JSON-RPC/XML-RPC requests on all network interfaces. If false is given, listen only on local loopback interface. Default: false
rpc-listen-all=true

# Enable JSON-RPC/XML-RPC server. Default: false
enable-rpc=true
rpc-secret=1234qwerasdfzxcv%

#最大同时下载数(任务数), 路由建议值: 3
max-concurrent-downloads=5
#断点续传
continue=true
#同服务器连接数
max-connection-per-server=5
#最小文件分片大小, 下载线程数上限取决于能分出多少片, 对于小文件重要
min-split-size=10M
#单文件最大线程数, 路由建议值: 5
split=10
#下载速度限制
max-overall-download-limit=0
#单文件速度限制
max-download-limit=0
#上传速度限制
max-overall-upload-limit=0
#单文件速度限制
max-upload-limit=0


on-download-complete=/etc/conf/aria2/download-complete.sh

 bt-tracker=http://tracker.city9x.com:2710/announce,http://retracker.mgts.by:80/announce,http://retracker.telecom.by:80/announce,http://tracker.skyts.net:6969/announce,http://alpha.torrenttracker.nl:443/announce,http://torrent.nwps.ws:6969/announce,http://0d.kebhana.mx:443/announce,http://tracker2.itzmx.com:6961/announce,http://tracker.vanitycore.co:6969/announce,http://tracker.torrentyorg.pl:80/announce,http://tracker.tfile.me:80/announce,http://tracker.mg64.net:6881/announce,http://tracker.electro-torrent.pl:80/announce,http://t.nyaatracker.com:80/announce,http://share.camoe.cn:8080/announce,http://open.kickasstracker.com:80/announce,http://open.acgtracker.com:1096/announce,http://open.acgnxtracker.com:80/announce,http://omg.wtftrackr.pw:1337/announce,http://mgtracker.org:6969/announce,http://fxtt.ru:80/announce,http://bt.dl1234.com:80/announce,http://agusiq-torrents.pl:6969/announce,http://104.238.198.186:8000/announce
