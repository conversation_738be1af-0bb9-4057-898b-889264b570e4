
using System;
using System.Collections.Generic;
using System.Text;
using H3;
using H3.DataModel;
using System.Data;
public class D2826052468b3ca3f39406a80a5ee6055d35ec1_ListViewController: H3.SmartForm.ListViewController
{
    public D2826052468b3ca3f39406a80a5ee6055d35ec1_ListViewController(H3.SmartForm.ListViewRequest request): base(request)
    {
    }

    protected override void OnLoad(H3.SmartForm.LoadListViewResponse response)
    {
        base.OnLoad(response);
    }

    protected override void OnSubmit(string actionName, H3.SmartForm.ListViewPostValue postValue, H3.SmartForm.SubmitListViewResponse response)
    {
        if(actionName == "rollback")
        {
            string[] objectIds = this.Deserialize<string[]>(this.Request["ObjectIds"]);

            DPuXinInventory inventory = new DPuXinInventory(this.Engine, this.Request.UserContext.UserId);

            Dictionary < string, List < BizObject >> reportDict =  inventory.QueryReportDict(objectIds);

            List < DPuXinValidateError > errors = inventory.RollbackValidate(reportDict);
            if(errors.Count > 0)
            {
                response.ReturnData = new Dictionary<string, object>();
                response.ReturnData.Add("Errors", this.Serialize(errors));
                return;
            }
            inventory.WriteInventory();
            inventory.SubmitPlans(reportDict, true);
            inventory.DelReportDetails(objectIds);
            return;
        }
        base.OnSubmit(actionName, postValue, response);
    }
}