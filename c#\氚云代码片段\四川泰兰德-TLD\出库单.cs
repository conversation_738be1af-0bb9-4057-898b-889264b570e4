using System;
using System.Collections.Generic;
using System.Text;
using H3;

public class D149913Sde2p55qe494ogxxwj9lk9z5z1: H3.SmartForm.SmartFormController
{
    public D149913Sde2p55qe494ogxxwj9lk9z5z1(H3.SmartForm.SmartFormRequest request): base(request)
    {
    }

    protected override void OnLoad(H3.SmartForm.LoadSmartFormResponse response)
    {
        base.OnLoad(response);
    }


    protected override void OnSubmit(string actionName, H3.SmartForm.SmartFormPostValue postValue, H3.SmartForm.SubmitSmartFormResponse response)
    {
        if(actionName == "Submit")
        {
            object obj = this.Request;
            string taskId = this.Request.BizObject["F0000063"] + string.Empty;
            if(string.IsNullOrEmpty(taskId))
            {
                rollBackWorkflow(response, "出库任务不存在");
                return;
            }
            H3.DataModel.BizObject task = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId,
                this.Engine, "D149913efcdbdae2c964aee8ad2c233a7cb7243", taskId, false);
            if(task == null)
            {
                rollBackWorkflow(response, "出库任务不存在");
                return;
            }
            H3.DataModel.BizObject[] outDetails = (H3.DataModel.BizObject[]) task["D149913F1e941b9534ad4f73998c9d8d38b042c1"];
            if(outDetails == null || outDetails.Length == 0)
            {
                rollBackWorkflow(response, "出库任务中出库明细不存在");
                return;
            }
            List < H3.DataModel.BizObject > outDetailsList = new List<H3.DataModel.BizObject>(outDetails);



            H3.DataModel.BizObject[] details = (H3.DataModel.BizObject[]) this.Request.BizObject["D149913Frg2fuy8v6adtwbbqzmv2k5wk4"]; // 库存明细

            Dictionary < string, int > dict = new Dictionary<string, int>();
            // 合并同类产品
            foreach(H3.DataModel.BizObject detail in details)
            {
                string productId = detail["F0000005"] + string.Empty; // 产品id
                string remark = detail["F0000021"] + string.Empty; // 参数备注
                int outNum = Convert.ToInt32(String.IsNullOrEmpty(detail["F0000007"] + "") ? "0" + detail["F0000007"] : detail["F0000007"]);
                string key = productId + "_" + remark;
                if(!dict.ContainsKey(key))
                {
                    dict.Add(key, outNum);
                }
                else
                {
                    dict[key] = dict[key] + outNum;
                }

                H3.DataModel.BizObject outDetail = outDetailsList.Find(item =>
                {
                    string outProductId = item["F0000019"] + string.Empty;
                    string outRemark = item["F0000038"] + string.Empty;
                    return productId == outProductId && remark == outRemark;
                });

                if(outDetail != null)
                {
                    decimal waitNum = Convert.ToDecimal(outDetail["F0000024"]);
                    if(waitNum < outNum)
                    {
                        rollBackWorkflow(response, "出库明细出库数量已超过待发数量");
                        return;
                    }
                }
                else
                {
                    rollBackWorkflow(response, "出库任务中出库明细不存在");
                    return;
                }
            }
            // 库存校验
            foreach(KeyValuePair < string, int > pair in dict)
            {
                //查询库存,产品id + 参数备注
                string[] result = pair.Key.Split('_');
                string sqlpo = "select F0000007,F0000014,F0000011 from i_D149913b779923207a14917a7e6b909ddcf3863 where F0000001 = '" + result[0] + "'"; // 查询库存实际量
                if(string.IsNullOrEmpty(result[1]))
                {
                    sqlpo += " and F0000011 is null";
                } else
                {
                    sqlpo += " and F0000011 = '" + result[1] + "'";
                }
                System.Data.DataTable stockTable = this.Request.Engine.Query.QueryTable(sqlpo, null);
                int stockNum = 0;
                int warningNum = 0;
                if(stockTable != null && stockTable.Rows.Count > 0)
                {
                    stockNum = Convert.ToInt32(String.IsNullOrEmpty(stockTable.Rows[0]["F0000007"] + "") ? "0" + stockTable.Rows[0]["F0000007"] : stockTable.Rows[0]["F0000007"]);
                }
                if(stockNum <= 0 || stockNum < pair.Value)
                {
                    // H3.Workflow.Messages.ActivateInstanceMessage   activiteinstanceMessage = new   H3.Workflow.Messages.ActivateInstanceMessage(this.Request.InstanceId); //传递参数为流程实例ID
                    // this.Request.Engine.WorkflowInstanceManager.SendMessage(activiteinstanceMessage);


                    //流程回到发起节点
                    // H3.Workflow.Messages.ActivateActivityMessage activiteMessage = new H3.Workflow.Messages.ActivateActivityMessage(this.Request.InstanceId,
                    //     "Activity2", H3.Workflow.Instance.Token.UnspecifiedId, new string[] {}, null, false, H3.Workflow.WorkItem.ActionEventType.Adjust);//参数对应描述：流程实例ID，活动节点编码，令牌ID，参与者，前驱令牌，是否检测入口条件，激活类型
                    // this.Request.Engine.WorkflowInstanceManager.SendMessage(activiteMessage);
                    // 更新状态

                    // H3.DataModel.BizObject order = H3.DataModel.BizObject.Load(this.Request.UserContext.UserId,
                    //     this.Engine, "D149913Snud6aia8egylsjk2dq8m7k000", this.Request.BizObjectId, false);
                    // order.Status = H3.DataModel.BizObjectStatus.Draft; //设置状态生效
                    // order.Update();
                    //postValue.DestActivityCode = "Activity2";

                    //base.OnSubmit("ForBackActivateClick", postValue, response);
                    //base.OnAction("ForBackActivateClick", postValue, H3.Workflow.Instance.WorkflowInstanceState.);
                    //response.Errors.Add("存在产品出库数量超过库存数量，请重新发起"); //弹出报错窗口

                    rollBackWorkflow(response, "存在产品出库数量超过库存数量");
                    return;
                }
            }
        }

        base.OnSubmit(actionName, postValue, response);

    }

    private void rollBackWorkflow(H3.SmartForm.SubmitSmartFormResponse response, string error)
    {
        string code = this.Request.ActivityCode;
        if(code != "Activity2")
        {
            H3.Workflow.Messages.CancelActivityMessage cancelMessage = new H3.Workflow.Messages.CancelActivityMessage(this.Request.InstanceId, this.Request.ActivityCode, true);
            this.Request.Engine.WorkflowInstanceManager.SendMessage(cancelMessage);
        }
        response.ReturnData = new Dictionary<string, object>();
        response.ReturnData.Add("error", error);
    }
}